#!/bin/bash

# Get the latest version
latest_version=$(git describe --tags --abbrev=0)

# Print all versions
echo "All versions:"
tags=$(git tag)
for tag in $tags
do
  echo $tag
done

# Prompt the user for the new version
echo "Enter the new version (current version is $latest_version):"
read new_version

git pull origin development

# Create the new version tag
git tag $new_version

# Push the new tag to the origin repository
git push origin $new_version

echo "New tag $new_version created and pushed to origin."
