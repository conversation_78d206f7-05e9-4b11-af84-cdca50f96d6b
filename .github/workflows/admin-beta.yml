# name: Beta Build and Deploy

# on:
#   push:
#     branches:
#       - development

# jobs:
#   build:
#     runs-on: ubuntu-latest
#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v2

#       - name: Setup Node.js
#         uses: actions/setup-node@v2
#         with:
#           node-version: 16.16.0


#       - run: 'cp env.production.cicd .env.production.local'
#       - run: 'rm .npmrc'
#         shell: bash

#       - run: 'echo "//npm.pkg.github.com/:_authToken=$NPM_TOKEN" >> .npmrc'
#         shell: bash
#         env:
#           NPM_TOKEN: ${{ secrets.NPM_PKG_TOKEN }}
#           CI: false
#       - run: 'echo "@shoutout-labs:registry=https://npm.pkg.github.com/shoutout-labs" >> .npmrc'
#         shell: bash
#       - run: 'npm i'
#       - run: 'npm run build:staging:github'
#         env:
#           REACT_APP_BUILD_VARIANT: dev

#       - name: Set up AWS CLI
#         run: |
#           echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
#           echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
#           echo "AWS_DEFAULT_REGION=us-west-2" >> $GITHUB_ENV

#       - name: Copy build files to S3 bucket
#         run: |
#           aws s3 cp ./build s3://loyalty-admin-portal-gfbeta/ --recursive

#       - name: CloudFront Invalidation
#         run: |
#           aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
#           aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
#           aws configure set default.region $AWS_DEFAULT_REGION

#           aws cloudfront create-invalidation --distribution-id E2F2CUZTU8CGSB --paths "/*"

#       - name: Slack Notification
#         uses: rtCamp/action-slack-notify@v2
#         env:
#           SLACK_USERNAME: ShoutOUT Enterprise Loyalty Staging
#           SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
#           SLACK_TITLE: New staging version deployed to admin beta
#           SLACK_MESSAGE: New staging version of Enterprise Loyalty Dashboard is available on https://admin.loyaltybeta.cxforge.com/
#           SLACK_ICON_EMOJI: hammer_and_wrench
#           SLACK_COLOR: ${{ job.status }}
