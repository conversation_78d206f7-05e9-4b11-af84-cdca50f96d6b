version: 0.2
env:
  variables:
    BUCKET_NAME: loyalty-admin-portal-gfcyc
    REACT_APP_BUILD_VARIANT: prod
  parameter-store:
    NPM_TOKEN: /shared/codeBuild/shoutout/github/token
    AWS_ACCESS_KEY_ID: /shared/codepipline/shoutout/cicd-user/access_key_id
    AWS_SECRET_ACCESS_KEY: /shared/codepipline/shoutout/cicd-user/secret_access_key
phases:
  install:
    commands:
      - echo Entered the install phase...
      - cp env.production.cicd .env.production.local
      - rm .npmrc
      - echo "//npm.pkg.github.com/:_authToken=$NPM_TOKEN" >> .npmrc
      - echo "@shoutout-labs:registry=https://npm.pkg.github.com/shoutout-labs" >> .npmrc
      - npm install -g npm-install-peers
      - npm ci --legacy-peer-deps
      
  build:
    commands:
      - echo Entered the build phase...
      - npm run build
  post_build:
    commands:
      - echo Entered the post_build phase...
      - echo Export AWS credentials to CodePipeline
      - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
      - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
      - export AWS_DEFAULT_REGION=us-west-2
      - echo Uploading the artifacts to S3 bucket
      - aws s3 sync s3://loyalty-admin-portal-gfcyc s3://loyalty-deployment-archives-tfryaeye/loyalty-admin-portal-gfcyc
      - aws s3 cp build s3://$BUCKET_NAME/ --recursive
      - echo TODO Creating an invalidation on Cloud Front distribution
      - aws cloudfront create-invalidation --distribution-id E2F2CUZTU8CGSB --paths "/index.html" --query Invalidation.Status
      - echo Build completed on `date`
