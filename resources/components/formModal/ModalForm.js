import React, { useCallback, useState } from "react";
import PropTypes from "prop-types";
import { Modal, Button, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { toast } from "react-toastify";

const ModalForm = ({ show, onHide }) => {
  const [validated, setValidated] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const onSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      if (e.target.checkValidity()) {
        try {
          setIsCreating(true);
          // MAIN LOGIC HERE
          setIsCreating(false);
          toast.success("SUCCESS msg");
          onHide();
        } catch (e) {
          toast.error("ERROR msg");
        }
      } else {
        setValidated(true);
      }
    },
    [setValidated, onHide, setIsCreating]
  );

  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>{/**{TITLE HERE} */}</Modal.Title>
      </Modal.Header>
      <Form onSubmit={onSubmit} validated={validated} noValidate>
        <Modal.Body>{/* {BODY HERE} */}</Modal.Body>
        <Modal.Footer>
          <Button
            size="sm"
            variant="outline-primary"
            onClick={onHide}
            type="button"
            disabled={isCreating}
          >
            Cancel
          </Button>
          <Button
            size="sm"
            variant="primary"
            type="submit"
            disabled={isCreating}
          >
            {/* {ACTION BUTTON TEXT} */}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

ModalForm.propTypes = {
  /**
   * Show edit view
   */
  show: PropTypes.bool.isRequired,

  /**
   * Callback on close
   */
  onHide: PropTypes.func.isRequired,
};

export default ModalForm;
