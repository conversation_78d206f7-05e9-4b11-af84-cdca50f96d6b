import React, { PureComponent } from 'react'
import { <PERSON><PERSON>, FileUploader } from '@shoutout-labs/shoutout-themes-enterprise';
import { toast } from "react-toastify";
import { uploadImage } from '../../../Services';
import { LoadingComponent } from '../../utils/UtilComponents';

class ImageUploader extends PureComponent {
    constructor() {
        super();
        this.state = { imageUploadingIndicator: false, imageLoadingIndicator: true, src: '' };
     

        this._onDrop = this._onDrop.bind(this);
        this._imageLoadError = this._imageLoadError.bind(this);
        this._imageOnLoad = this._imageOnLoad.bind(this);
    }


    _changeSrc(src) {
        if (src) {
            this.setState({ imageLoadingIndicator: true, src });
        } else {
            this.setState({ imageLoadingIndicator: false, src });
        }
    }
    _onDrop(files) {
        this._changeSrc('');
        this.setState({
            dropzoneActive: false
        });

        const file = files[0];

        this.setState({ imageUploadingIndicator: true, imageLoadingIndicator: true });
     

        uploadImage(file).then(res => {
            this.setState({ src: res.url, imageUploadingIndicator: false });
            this.props.onChange(res.url);
        }, err => {
            toast.error(
                'Cannot upload file'
              );
        });

    }

    onDropRejected = ({ type }) => {
        if (type === "large file") {
            toast.error(
                'Selected file is too large. Maximum allowed size is 3MB.'
              );
        }
        else if (type === "invalid file"){
            toast.error(
                'Invalid file type. Please select an image'
              );
        }
        else {
            toast.error(
                'Invalid file selected.'
              );
        }
    }

    _imageOnLoad() {
        this.setState({ imageLoadingIndicator: false });
    }
    _imageLoadError() {
        toast.error(
            'Can not load the image. Please retry'
          );
        this.setState({ imageLoadingIndicator: false, src: '' });
    }

    componentDidMount() {
        if (this.props.defaultSrc && !this.state.src) {
            this.setState({ src: this.props.defaultSrc });
        }
    }
    render() {
        const { imageUploadingIndicator, src, imageLoadingIndicator } = this.state;

        return (
            !!src ? <>
                <div className="text-center">
                    <img src={src} width={200} onLoad={this._imageOnLoad} onError={this._imageLoadError} role="presentation" alt="sample view" />
                </div>
                {(imageLoadingIndicator) ?
                    <LoadingComponent /> :
                    <div>
                        <div className="text-center">
                            <br />
                            <Button variant="danger" type="button" size="sm" onClick={(e) => { e.stopPropagation(); this._changeSrc('') }}>Change Image</Button>
                        </div>
                    </div>}
            </>
                : <div className="text-center">
                    {imageUploadingIndicator ? <LoadingComponent /> :
                        <FileUploader
                            // accept="image/*"
                            accept="*"
                            onDropRejected={this.onDropRejected}
                            maxSize={3145728}
                            multiple
                            onDropAccepted={this._onDrop}
                        />
                    }
                </div>


        );
    }
}
export default ImageUploader;


// const ImageUploader = (props) => {

//     const [imageUploadingIndicator, setImageUploadingIndicator] = useState(false);
//     const [imageLoadingIndicator, setImageLoadingIndicator] = useState(true)
//     const [src, setSrc] = useState('')
//     const [dropzoneActive, setDropzoneActive] = useState(false)
//     const reader = new FileReader()
//     const ctx = props
//     const fileType = ''

//   props.reader.addEventListner = ("load", ()=> {
//         uploadImage(ctx.reader.result, ctx.fileType).then(res => {
//             ctx.setSrc(res.fileUrl)
//             ctx.setImageUploadingIndicator(false)
//             ctx.props.onChange(res.fileUrl)
//         }, err => {
//             notify.show('Cannot upload file', 'error');
//         })
//     }, false)

//     const changeSrc = () => {
//         if (src) {
//             setImageLoadingIndicator(true)
//             setSrc(src)
//         } else {
//             setImageLoadingIndicator(false);
//             setSrc(src)
//         }
//     }

//     const onDrop = (files) => {
//         changeSrc('')
//         setDropzoneActive(false)

//         const file = files[0];

//         setImageUploadingIndicator(true)
//         setImageLoadingIndicator(true)
//         fileType = file.type;
//         reader.readAsDataURL(file);
//     }

//     const onDropRejected = ({ type }) => {
//         if (type === "large file") {
//             notify.show("Selected file is too large. Maximum allowed size is 3MB.", "error");
//         }
//         else if (type === "invalid file")
//             notify.show("Invalid file type. Please select an image", "error");
//         else {
//             notify.show("Invalid file selected.", "error");
//         }
//     }

//     const onimageOnLoad = () => {
//         setImageLoadingIndicator(false)
//     }

//     const imageLoadError = () =>  {
//         notify.show("Can not load the image. Please retry", "error");
//         setImageLoadingIndicator(false)
//         setSrc('')
//     }

//     useEffect(() => {

//         if (props.defaultSrc && !src) {
//             setSrc(props.defaultSrc)
//         }

//     },[]) 

//     return (
//         !!src ? <div>
//             <div className="text-center">
//                     <img src={src} width={200} onLoad={this._imageOnLoad} onError={this._imageLoadError} role="presentation" alt="sample view" />
//                 </div>
//                 {(imageLoadingIndicator) ?
//                     <LoadingComponent /> :
//                     <div>
//                         <div className="text-center">
//                             <br />
//                             <Button variant="danger" type="button" size="sm" onClick={(e) => { e.stopPropagation(); changeSrc('') }}>Change Image</Button>
//                         </div>
//                     </div>}
//             </div>
//             : <div className="text-center">
//                 {imageUploadingIndicator ? <LoadingComponent /> :
//                         <FileUploader
//                             accept="image/*"
//                             onDropRejected={onDropRejected}
//                             maxSize={3145728}
//                             multiple={false}
//                             onDropAccepted={onDrop}
//                         />

//                     }
//             </div>
//     )
// }

// export default ImageUploader
