import React from "react";
import PropTypes from "prop-types";
import {
    OverlayTrigger,
    Tooltip,
} from "@shoutout-labs/shoutout-themes-enterprise";

const CustomOverlayTrigger = ({ component, overlayText, placement }) => {
    return component ? (
        <OverlayTrigger
            placement={placement}
            overlay={
                <Tooltip id="tooltip-custom-overlay">{overlayText}</Tooltip>
            }
        >
            {component}
        </OverlayTrigger>
    ) : null;
};

CustomOverlayTrigger.defaultProps = {
    component: null,
    overlayText: "",
    placement: "bottom",
};

CustomOverlayTrigger.propTypes = {
    component: PropTypes.any,
    overlayText: PropTypes.string,
    placement: PropTypes.string,
};

export default CustomOverlayTrigger;
