import React from "react";
import propTypes from "prop-types";

import "./PreviewSms.scss";

const PreviewSms = ({ senderId, smsContent }) => {
    return (
        <div className="preview-sms-view">
            <div className="phone mx-auto">
                <div className="the-sender-id d-flex justify-content-center align-items-center">
                    <p className="sender-id mb-0">{senderId || "Unknown"}</p>
                </div>
                <div className="message-bubble-container">
                    <div className="message-bubble mt-3 p-2 ml-2">
                        <span className="like-pre">{smsContent}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

PreviewSms.defaultProps = { senderId: "", smsContent: "" };

PreviewSms.propTypes = {
    senderId: propTypes.string.isRequired,
    smsContent: propTypes.string.isRequired,
};

export default React.memo(PreviewSms);
