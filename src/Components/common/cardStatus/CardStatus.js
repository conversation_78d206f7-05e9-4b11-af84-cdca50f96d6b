import { CardStatus, CardTypes } from "Data";

const getCardTypeVariant = (type) => {
    switch (type) {
        case CardTypes.DIGITAL_CARD:
            return "purple";
        case CardTypes.INSTANT_CARD:
            return "teal";
        case CardTypes.EMBOSSED_CARD:
            return "warning";
        case CardTypes.KEY_TAG:
            return "info";
        case CardTypes.REGULAR_CARD:
            return "primary";
        case CardTypes.REGULAR_CARD_AND_KEY_TAG:
            return "secondary";
        default:
            return "default";
    }
};

const getCardStatusVariant = (status) => {
    switch (status) {
        case CardStatus.ACTIVE:
            return "success";
        case CardStatus.PENDING:
            return "warning";
        case CardStatus.ASSIGNED:
            return "secondary";
        case CardStatus.DEACTIVATED:
            return "danger";
        case CardStatus.READY:
            return "info";
        case CardStatus.SUSPENDED:
            return "orange";
        default:
            return "default";
    }
};

export { getCardTypeVariant, getCardStatusVariant };
