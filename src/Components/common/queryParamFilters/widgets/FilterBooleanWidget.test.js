import React from "react";
import { create } from "react-test-renderer";
import FilterBooleanWidget from "./FilterBooleanWidget";

const checkValues = [
    { label: "True", value: true },
    { label: "False", value: false },
];

describe("FilterBooleanWidget component snapshot", () => {
    const onChange = jest.fn();

    const props = {
        name: "test_case_form_check_widget",
        checkValues,
        disabled: false,
        onChange,
    };

    test("Matches the snapshot", () => {
        const component = create(<FilterBooleanWidget props={props} />);
        expect(component.toJSON()).toMatchSnapshot();
    });
});
