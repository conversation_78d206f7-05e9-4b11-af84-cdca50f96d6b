import React from "react";
import { create } from "react-test-renderer";
import FilterDropdownWidget from "./FilterDropdownWidget";

describe("FilterDropdownWidget component snapshot", () => {
    const onChangeSelect = jest.fn();

    const props = {
        id: "test_case_dropdown_widget",
        labelKey: "label",
        placeholder: "Select filter...",
        selectOptions: [
            { lable: "A Filter", value: "A" },
            { lable: "B Filter", value: "B" },
            { lable: "C Filter", value: "C" },
        ],
        selectedValue: [{ lable: "B Filter", value: "B" }],
        groupBy: "",
        disabled: false,
        onChangeSelect,
    };

    test("Matches the snapshot", () => {
        const component = create(<FilterDropdownWidget props={props} />);
        expect(component.toJSON()).toMatchSnapshot();
    });
});
