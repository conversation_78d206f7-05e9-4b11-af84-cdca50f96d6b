import React from "react";
import {
    Button,
    IcIcon,
    Form,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faTimes } from "FaICIconMap";

const EmailsInput = ({
    index: emailIndex,
    item,
    emailsLength,
    disabled,
    removeEmailItem,
    setEmailValue,
}) => (
    <div
        className={`d-flex align-items-center ${
            emailIndex !== 0 ? "mt-3" : ""
        }`}
    >
        <Form.Control
            type="email"
            id={emailIndex}
            name={`email-${emailIndex}`}
            value={item}
            disabled={disabled}
            onChange={setEmailValue}
            placeholder="Enter an email address"
            required
        />
        <div className="ml-2">
            <Button
                className="text-danger btn shadow-none"
                variant="link"
                size="sm"
                data-index={emailIndex}
                disabled={disabled || emailsLength === 1}
                onClick={removeEmailItem}
            >
                {emailsLength !== 1 && <IcIcon size="lg" icon={faTimes} />}
            </Button>
        </div>
    </div>
);

export default EmailsInput;
