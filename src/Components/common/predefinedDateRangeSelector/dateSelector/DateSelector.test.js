import React from "react";
import { create } from "react-test-renderer";
import DateSelector from "./DateSelector";

describe("Date selector component snapshot", () => {
    test("Matches the snapshot", () => {
        const setSelectedPeriod = jest.fn();

        const props = {
            selectedPeriod: "7 Days",
            setSelectedPeriod,
            isLoading: false,
            isCustomEnabled: false,
        };

        const component = create(<DateSelector props={props} />);
        expect(component.toJSON()).toMatchSnapshot();
    });
});
