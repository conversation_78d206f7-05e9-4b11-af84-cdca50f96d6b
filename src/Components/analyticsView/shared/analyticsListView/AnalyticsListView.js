import React, { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { Col, FormSearch, IcIcon, Row } from "@shoutout-labs/shoutout-themes-enterprise";
import AnalyticsListTableView from "./AnalyticsListTableView";
import { DataContext, UserContext } from "../../../../Contexts";
import { exportAnalyticsReport, getTopListAnalytics } from "../../../../Services";
import { toast } from "react-toastify";
import { convertToLocaleString, downloadLink, toTitleCase } from "../../../../Utils";
import {
    AccessPermissionModuleNames, AccessPermissionModules,
    AnalyticsTable,
    AnalyticsTableColumns,
    AnalyticsTopListURL,
} from "../../../../Data";
import { faImport } from "../../../../FaICIconMap";
import { AnalyticsExportURL } from "../../../../Data/AnalyticsData";
import AnalyticsAccessControl from "../../AnalyticsAccessControl";
import { AnalyticsFunctionalAccessControl } from "../../AnalyticsFunctionalAccessControl";
import SizePerPageRenderer from "../../../utils/table/sizePerPageRenderer/SizePerPageRenderer";

const defaultLimit = 10, defaultSkip = 1;
const AnalyticsListView = ({ toDate, fromDate, activeTable, activeView }) => {
    const { affinityGroups, tiers } = useContext(DataContext);
    const { selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [isLoading, setIsLoading] = useState(false);
    const [searchText, setSearchText] = useState("");
    const [data, setData] = useState([]);

    const loadTableList = useCallback(async () => {
        try {
            setIsLoading(true);
            setData([]);

            const tableData = await getTopListAnalytics(AnalyticsTopListURL[activeTable], {
                regionId: selectedRegion._id,
                dateFrom: fromDate,
                dateTo: toDate,
                ...(activeTable === AnalyticsTable.REWARD_LIST ? { groupBy: "REWARD" } : {})
            });

            let affinityGroupsMap = {};
            let tiersMap = {};
            if (activeTable !== AnalyticsTable.REWARD_LIST) {
                affinityGroupsMap = affinityGroups.reduce((map, obj) => {
                    map[obj._id] = obj.name || "-";
                    return map;
                }, {});
                tiersMap = tiers.reduce((map, obj) => {
                    map[obj._id] = obj.name || "-";
                    return map;
                }, {});
            }

            setData(tableData.data.length !== 0 ?
                tableData.data.map((item) => {
                    switch (activeTable) {
                        case AnalyticsTable.REWARD_LIST:
                            return {
                                _id: item._id,
                                name: item.rewardName,
                                redeemedCount: convertToLocaleString(item.redeemedCount),
                                claimedCount: convertToLocaleString(item.claimedCount),
                            }
                        case AnalyticsTable.AFFINITY_GROUPS:
                            return {
                                _id: item.affinityGroupId,
                                name: affinityGroupsMap[`${item.affinityGroupId}`] || "-",
                                earnedPoints: convertToLocaleString(item.earnedPoints),
                                redeemedPoints: convertToLocaleString(item.redeemedPoints),
                                expiredPoints: convertToLocaleString(item.expiredPoints),
                            }
                        case AnalyticsTable.TIERS:
                            return {
                                tierId: item.tierId,
                                name: tiersMap[`${item.tierId}`] || "-",
                                earnedPoints: convertToLocaleString(item.earnedPoints),
                                redeemedPoints: convertToLocaleString(item.redeemedPoints),
                                expiredPoints: convertToLocaleString(item.expiredPoints),
                            }
                        default:
                            return {}
                    }
                })
                : []);
        } catch (e) {
            console.error(e);
            toast.error(e.message || `${toTitleCase(activeTable)} loading failed`);
        } finally {
            setIsLoading(false);
        }
    }, [setIsLoading, activeTable, selectedRegion, fromDate, toDate, affinityGroups, tiers, setData]);

    const onExportClick = useCallback(async () => {
        try {
            const url = await exportAnalyticsReport(AnalyticsExportURL[activeTable], {
                regionId: selectedRegion._id,
                dateFrom: fromDate,
                ...(activeTable === AnalyticsTable.REWARD_LIST ? { groupBy: "REWARD" } : {}),
                dateTo: toDate,
            });
            downloadLink(url.url);
        } catch (e) {
            console.error(e)
            toast.error(e.message || "Analytics report export failed");
        }
    }, [activeTable, selectedRegion, fromDate, toDate]);

    const onExportExpiredPointsClick = useCallback(() => {
        // Export the totalExpiredPoints as a CSV file
        const csvContent = `Total Expired Points\n${convertToLocaleString(0)}`; // Total expired points removed
        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
        const url = URL.createObjectURL(blob);
        downloadLink(url, "TotalExpiredPoints.csv");
    }, []);

    const tableListData = useMemo(() => {
        if (searchText) {
            return data.filter(item => item?.name?.toLowerCase() === searchText.toLowerCase());
        } else {
            return data;
        }
    }, [searchText, data]);

    const options = {
        page: skip,
        sizePerPage: limit,
        totalSize: data.length || 0,
        sizePerPageRenderer: SizePerPageRenderer,
        paginationSize: 5,
        pageStartIndex: 1,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageList: [
            {
                text: "10",
                value: 10,
            },
            {
                text: "25",
                value: 25,
            },
            {
                text: "50",
                value: 50,
            },
        ],
        onPageChange: (page) => {
            setSkip(page);
        },
        onSizePerPageChange: (sizePerPage) => {
            setLimit(sizePerPage);
        },
    };

    useEffect(() => {
        if (AnalyticsFunctionalAccessControl({
            isAuthorizedForAction: isAuthorizedForAction,
            moduleName: AccessPermissionModuleNames[activeView],
            actionNames: [
                AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ViewRedeemedRewardsCount,
                AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ViewAffinityGroupPointCounts,
                AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ViewTierPointCounts,
            ],
            Logic: "OR"
        })) {
            loadTableList();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fromDate, toDate]);

    return (
        <div className="mr-2 w-lg-100 w-md-50 pb-4 h-100">
            <Row>
                <AnalyticsAccessControl
                    moduleName={AccessPermissionModuleNames[activeView]}
                    actionNames={[
                        AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ViewRedeemedRewardsCount,
                        AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ViewAffinityGroupPointCounts,
                        AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ViewTierPointCounts,
                    ]}
                    Logic={"OR"}
                    renderEmpty={true}
                >
                    <Col lg={7} md={7} sm={7} xs={7}>
                        <Row>
                            <Col lg={12} md={12} sm={12} xs={12}>
                                <h3 className="mt-1 mx-3">
                                    {toTitleCase(activeTable)}
                                </h3>
                            </Col>
                            {(fromDate && toDate) && <Col lg={12} md={12} sm={12} xs={12}>
                                <h5 className="mt-1 mx-3">Processed Between {fromDate} to {toDate}</h5>
                            </Col>}
                        </Row>
                    </Col>
                </AnalyticsAccessControl>
                <AnalyticsAccessControl
                    moduleName={AccessPermissionModuleNames[activeView]}
                    actionNames={[
                        AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ExportRedeemedRewardsCount,
                        AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ExportAffinityGroupPointCounts,
                        AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ExportTierPointCounts,
                    ]}
                    Logic={"OR"}
                    renderEmpty={true}
                >
                    <Col lg={5} md={5} sm={5} xs={5}>
                        <div
                            className={`my-4 d-flex  
                                justify-content-end
                                ${isLoading ?
                                    "is-loading text-muted" :
                                    "calender-selector"
                                }`}
                            onClick={isLoading ? () => { } : onExportClick}
                        >
                            <IcIcon className="mt-1 mx-3" icon={faImport} size="lg" />
                        </div>
                        <div
                            className="my-4 d-flex justify-content-end"
                            onClick={isLoading ? () => { } : onExportExpiredPointsClick}
                        >
                            <IcIcon className="mt-1 mx-3" icon={faImport} size="lg" />
                            <span>Export Expired Points</span>
                        </div>
                    </Col>
                </AnalyticsAccessControl>
            </Row>

            <AnalyticsAccessControl
                moduleName={AccessPermissionModuleNames[activeView]}
                actionNames={[
                    AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ViewRedeemedRewardsCount,
                    AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ViewAffinityGroupPointCounts,
                    AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ViewTierPointCounts,
                ]}
                Logic={"OR"}
            >
                <>
                    <Row>
                        <Col lg={6} md={6} sm={12} xs={12}>
                            <FormSearch
                                placeholder="Search"
                                selected={searchText}
                                onChange={setSearchText}
                                id="search-rewards"
                                size="sm"
                                className="mt-1 mx-3"
                            />
                        </Col>
                    </Row>
                    <br />
                    <AnalyticsListTableView
                        data={tableListData}
                        tableOptions={options}
                        isLoading={isLoading}
                        columns={AnalyticsTableColumns[activeTable]}
                    />
                </>
            </AnalyticsAccessControl>

        </div>
    )
}

export default AnalyticsListView;
