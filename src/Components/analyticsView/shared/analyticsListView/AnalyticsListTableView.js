import React from "react";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import { BootstrapTable } from "@shoutout-labs/shoutout-themes-enterprise";
import paginationFactory from "react-bootstrap-table2-paginator";
import TableNoData from "../../../utils/table/NoDataComponent";
import { BootstrapTableOverlay } from "../../../utils";

const AnalyticsListTableView=({isLoading,tableOptions,data,columns})=>{
    return(
            <div className="h-100 px-4 ">
                <ToolkitProvider
                    keyField="_id"
                    data={data}
                    columns={columns}
                    columnToggle
                >
                    {(props) => (
                        <BootstrapTable
                            {...props.baseProps}
                            pagination={paginationFactory(tableOptions)}
                            loading={isLoading}
                            noDataIndication={() => <TableNoData loading={isLoading} />}
                            overlay={BootstrapTableOverlay}
                        />
                    )}
                </ToolkitProvider>
            </div>
    )
}

export default AnalyticsListTableView
