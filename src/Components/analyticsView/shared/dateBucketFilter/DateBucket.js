import React, { useCallback, useState, useEffect } from "react";

import "./DateBucketFilter.scss";

const DateBucket = ({
    isLoading,
    dateBuckets,
    setDateBucket,
    isDataAvailable,
}) => {
    const [dateBucketIndex, setDateBucketIndex] = useState(0);

    const onClickDateBucket = useCallback(
        (event) => {
            event.stopPropagation();
            setDateBucketIndex(
                parseInt(event.currentTarget.dataset.index || 0)
            );
            setDateBucket(event.currentTarget.dataset.value);
        },
        [setDateBucketIndex, setDateBucket]
    );

    useEffect(() => {
        setDateBucketIndex(0);
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateBuckets]);

    return (
        <div className="date-bucket-filter-view">
            <div
                className={`date-bucket-filter-view ${
                    !isLoading && isDataAvailable
                        ? "date-bucket"
                        : "date-bucket-disabled"
                } rounded`}
            >
                {dateBuckets &&
                    dateBuckets.map((dateBucket, index) => (
                        <div
                            key={`date-bucket${index}`}
                            className={`date-bucket-button ${
                                dateBucketIndex === index &&
                                "date-bucket-button-active"
                            }`}
                            onClick={
                                isDataAvailable ? onClickDateBucket : () => {}
                            }
                            data-value={dateBucket.value}
                            data-index={index}
                        >
                            {dateBucket.label}
                        </div>
                    ))}
            </div>
        </div>
    );
};

export default DateBucket;
