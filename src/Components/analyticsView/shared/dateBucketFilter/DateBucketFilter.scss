.date-bucket-filter-view {
    .date-range-selector {
        background-color: #6ab3e3;
        width: 100%;
        height: 100%;
    }

    .date-bucket {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        width: auto;
        border: 0.125rem solid #11355c;
        padding: 0.25rem 0.5rem;

        .date-bucket-button {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            font-size: 0.9rem;
            border-radius: 0.1875rem;
            font-weight: 400;
            color: #11355c;
            &:hover,
            &:active,
            &:focus {
                background-color: #11355c;
                color: #ffffff;
            }
        }
        .date-bucket-button-active {
            background-color: #11355c;
            color: #ffffff;
        }
    }

    .date-bucket-disabled {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        width: auto;
        border: 0.125rem solid #a0a6ab;
        padding: 0.25rem 0.5rem;

        .date-bucket-button {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            font-size: 0.9rem;
            border-radius: 0.1875rem;
            font-weight: 400;
            color: #a0a6ab;
        }
        .date-bucket-button-active {
            background-color: #a0a6ab;
            color: #ffffff;
        }
    }

    .modal-content {
        display: flex;
        width: auto;
        .modal-body {
            .rdrDateRangePickerWrapper {
                width: 100%;
            }
        }
    }
}
