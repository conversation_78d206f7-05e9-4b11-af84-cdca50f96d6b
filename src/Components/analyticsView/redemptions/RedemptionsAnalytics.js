import React, { useState, useCallback, useEffect, useContext } from 'react';
import { Row, Col, Card } from '@shoutout-labs/shoutout-themes-enterprise';
import moment from 'moment';
import { getTopRewardAnalytics, getRedemptionSeries, getRedemptionCount} from "Services";
import { toast } from "react-toastify";
import VerticalBarChartCard from "../common/charts/barcharts/verticalBarChart/VerticalBarChartCard";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    AnalyticsActiveExport, AnalyticsActiveView,
    BarChartDataProperties,
    ChartColorCodes,
} from "Data";
import AnalyticsListView from "../shared/analyticsListView/AnalyticsListView";
import {UserContext} from "Contexts";
import CommonAnalyticsPage from "../layouts/CommonAnalyticsPage";
import { sortAscendingOrder } from "../utils/AnalyticsUtility";
import { AnalyticsTable } from "../../../Data";
import AnalyticsAccessControl from "../AnalyticsAccessControl";
import { AnalyticsFunctionalAccessControl } from "../AnalyticsFunctionalAccessControl";
import { convertToLocaleString } from "../../../Utils";
import "./Redemptions.scss";



const isVisibleDateFilter = false
const defaultLimit = 25, defaultSkip = 0;

const RedemptionsAnalytics = () => {
    const { selectedRegion,isAuthorizedForAction } = useContext(UserContext);
    const [toDate, setToDate] = useState(moment().format("YYYY-MM-DD"));
    const [fromDate, setFromDate] = useState(moment().subtract(1, 'M').format("YYYY-MM-DD"));
    const [isLoading, setIsLoading] = useState(false);
    const [rewardSeriesData, setRewardSeriesData] = useState({});
    const [count, setCount] = useState(0);
    const [dateBucket,setDateBucket]=useState("DAY")
    const [selectedOptionOne,setSelectedOptionOne]=useState([{  label: "All Rewards", value: "ALL_REWARDS"}])
    const [optionsOne,setOptionsOne]=useState([])
    const [isLoadingFilters,setIsLoadingFilters]=useState(false)
    const [dateBuckets,setDateBuckets]=useState([
        {  label: "Day", value: "DAY"},
        {  label: "Week", value: "WEEK"},
        {  label: "Month", value: "MONTH"},
    ]);


    const loadRewardsList = useCallback(async () => {
        try {
                setIsLoadingFilters(true)
                setSelectedOptionOne([{  label: "All Rewards", value: "ALL_REWARDS"}])
                const topRewardsList = await getTopRewardAnalytics( {
                    regionId : selectedRegion._id,
                    limit: defaultLimit,
                    skip: defaultSkip,
                  /*  dateFrom: fromDate,
                    dateTo: toDate*/
                });
                const rewardsList = (Array.isArray(topRewardsList.data) && topRewardsList.data.length !== 0) 
                    ? topRewardsList.data.map(reward=>  ({  label: reward?.name, value: reward?._id }))
                    :[];
                rewardsList.unshift({  label: "All Rewards", value: "ALL_REWARDS"})
                setOptionsOne(rewardsList);

                setIsLoadingFilters(false)
        } catch (e) {
                console.error(e);
                setIsLoadingFilters(false);
                toast.error(e.message || "Top rewards list loading failed");

        }
    },
        [/*fromDate, toDate, */selectedRegion]
    );

    const loadRedemptionsAnalyticsData = useCallback(async()=> {
        try{
            setIsLoading(true);
            const queryObjectRedemptionSeries = {
                regionId : selectedRegion._id,
                dateFrom: fromDate,
                dateTo: toDate,
                dateBucket,
                ...(selectedOptionOne[0].value!=="ALL_REWARDS" ? { rewardId:selectedOptionOne[0].value}: {}),
            }
            const rewardsSeriesData = await getRedemptionSeries(queryObjectRedemptionSeries);
            const rewardsCount = await getRedemptionCount(queryObjectRedemptionSeries);
            const barChartData =rewardsSeriesData.data.length!==0&&sortAscendingOrder({
                dataset:rewardsSeriesData.data,
                fromDate:fromDate,
                toDate:toDate,
                properties: BarChartDataProperties["REDEMPTIONS_ANALYTICS"],
                dateBucket
            })
            setRewardSeriesData(barChartData)
            setCount(rewardsCount.data[0]?.count || 0);
            setIsLoading(false);

        }catch (e){
            console.error(e);
            toast.error(
                e.message || "Redemptions analytics data loading failed"
              );
            setIsLoading(false);
        }

    },[setIsLoading, selectedRegion, fromDate, toDate, setCount, dateBucket,selectedOptionOne]);

      useEffect(() => {
          if(AnalyticsFunctionalAccessControl({
              isAuthorizedForAction:isAuthorizedForAction,
              moduleName:AccessPermissionModuleNames.REWARD_ANALYTICS,
              actionNames:[
                  AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ViewTopRewards,
              ],
              Logic:"AND"
          })){
              loadRewardsList();
          }
        //eslint-disable-next-line react-hooks/exhaustive-deps
      }, [fromDate,toDate]);

      useEffect(() => {
          if(AnalyticsFunctionalAccessControl({
              isAuthorizedForAction:isAuthorizedForAction,
              moduleName:AccessPermissionModuleNames.REWARD_ANALYTICS,
              actionNames:[
                  AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ViewRedeemedRewardsCount,
                  AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ViewRedeemedRewardsSeries,
              ],
              Logic:"AND"
          })){
              loadRedemptionsAnalyticsData();
          }
        //eslint-disable-next-line react-hooks/exhaustive-deps
      }, [fromDate,toDate,dateBucket,selectedOptionOne]);

    return (
        <CommonAnalyticsPage
            isLoading={isLoading||isLoadingFilters}
            fromDate={fromDate}
            setFromDate = {setFromDate}
            toDate = {toDate}
            setToDate ={setToDate}
            setDateBuckets={setDateBuckets}
            setDateBucket={setDateBucket}
            pageTitle="Analytics - Rewards"
            activeView={AnalyticsActiveView.REWARD_ANALYTICS}
            pageContent={
                <>
                    <Row className='my-3'>
                        <Card  className='h-100'>
                            <Card.Body  className='h-100'>
                                <AnalyticsAccessControl
                                    moduleName={AccessPermissionModuleNames.REWARD_ANALYTICS}
                                    actionNames={[
                                        AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ViewRedeemedRewardsCount,
                                        AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ViewRedeemedRewardsSeries,
                                        AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ExportRedeemedRewardsSeries
                                    ]}
                                    Logic={"OR"}
                                >
                                    <Col lg={12} md={12} sm={12} xs={12}>
                                        <VerticalBarChartCard
                                            title="Reward Redemption"
                                            isLoading = {isLoading}
                                            yAxisData = {rewardSeriesData.count}
                                            xAxisData = {rewardSeriesData.dateBucketKey}
                                            colors = {ChartColorCodes.DARK_BLUE}
                                            count = {convertToLocaleString(count)}
                                            dateBuckets ={dateBuckets}
                                            fromDate ={fromDate}
                                            setFromDate={setFromDate}
                                            toDate={toDate}
                                            setToDate={setToDate}
                                            setDateBucket={setDateBucket}
                                            setDateBuckets={setDateBuckets}
                                            isVisibleDateFilter={isVisibleDateFilter}
                                            filterIsVisible={true}
                                            secondFilterVisible={false}
                                            optionsOne={optionsOne}
                                            selectedOptionOne ={selectedOptionOne}
                                            isLoadingFilters={isLoadingFilters}
                                            setSelectedOptionOne={setSelectedOptionOne}
                                            activeExport={AnalyticsActiveExport.REWARD_REDEMPTION}
                                            selectedRegion={selectedRegion}
                                            dateBucket={dateBucket}
                                            activeView={AnalyticsActiveView.REWARD_ANALYTICS}
                                        />
                                    </Col>
                                </AnalyticsAccessControl>
                                <hr/>
                                <AnalyticsAccessControl
                                    moduleName={AccessPermissionModuleNames.REWARD_ANALYTICS}
                                    actionNames={[
                                        AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ViewRedeemedRewardsCount,
                                        AccessPermissionModules[AccessPermissionModuleNames.REWARD_ANALYTICS].actions.ExportRedeemedRewardsCount,
                                    ]}
                                    Logic={"OR"}
                                >
                                    <Col lg={12} md={12} sm={12} xs={12}>
                                        <AnalyticsListView
                                            fromDate = {fromDate}
                                            toDate = {toDate}
                                            activeTable={AnalyticsTable.REWARD_LIST}
                                            activeView={AnalyticsActiveView.REWARD_ANALYTICS}
                                        />

                                    </Col>
                                </AnalyticsAccessControl>
                            </Card.Body>
                        </Card>
                    </Row>
                </>
            }
        />
    )
}

export default RedemptionsAnalytics
