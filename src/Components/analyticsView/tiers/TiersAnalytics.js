import React, { useCallback, useContext, useState,useEffect } from "react";
import CommonAnalyticsPage from "../layouts/CommonAnalyticsPage";
import { Card, Col, Row } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "../../../Contexts";
import moment from "moment";
import PieChartCard from "../common/charts/piecharts/PieChartCard";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules, AnalyticsActiveView,
    AnalyticsTable,
    PieChartsColorCodes,
} from "../../../Data";
import { getMemberTiersCount } from "../../../Services";
import { toast } from "react-toastify";
import {convertDataToPieChartFormat} from 'Components/analyticsView/utils/AnalyticsUtility';
import AnalyticsListView from "../shared/analyticsListView/AnalyticsListView";
import AnalyticsAccessControl from "../AnalyticsAccessControl";


const TiersAnalytics=()=>{
    const { selectedRegion ,isAuthorizedForAction} = useContext(UserContext);
    const [tierData, setTierData] = useState([]);
    const [isLoadingTier, setIsLoadingTier] = useState(false);

    const [toDate, setToDate] = useState(moment().format("YYYY-MM-DD"));
    const [fromDate, setFromDate] = useState(moment().subtract(1, 'M').format("YYYY-MM-DD"));

    const loadMemberAnalyticsDataTier = useCallback(async()=> {

        try{
            setIsLoadingTier(true);
            const memberTierCountData = await getMemberTiersCount({ regionId : selectedRegion._id, });
            setTierData(convertDataToPieChartFormat(memberTierCountData?.data));
            setIsLoadingTier(false);

        }catch (e){
            console.error(e);
            toast.error(
                e.message || "Tier wise member analytics data loading failed"
            );
            setIsLoadingTier(false);
        }

    },[
        setIsLoadingTier,
        selectedRegion,
        setTierData
    ]);

    useEffect(() => {
        if(isAuthorizedForAction(AccessPermissionModuleNames.TIER_ANALYTICS,AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ViewTierDistribution)){
            loadMemberAnalyticsDataTier()
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return(
        <>
            <Card>
                <Card.Body>
                    <CommonAnalyticsPage
                        isLoading={isLoadingTier}
                        fromDate={fromDate}
                        setFromDate = {setFromDate}
                        toDate = {toDate}
                        setToDate ={setToDate}
                        pageTitle="Analytics - Tiers"
                        activeView={AnalyticsActiveView.TIER_ANALYTICS}
                        pageContent={
                            <>
                                <Row className="my-3 pb-2">
                                    <Col lg={6} md={6} sm={6} xs={6}>
                                        <Card className='h-100'>
                                            <Card.Body className='h-100'>
                                                <AnalyticsAccessControl
                                                    moduleName={AccessPermissionModuleNames.TIER_ANALYTICS}
                                                    actionNames={[
                                                        AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ViewAffinityGroupPointCounts,
                                                        AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ExportAffinityGroupPointCounts,
                                                    ]}
                                                    Logic={"OR"}
                                                >
                                                    <AnalyticsListView
                                                        fromDate = {fromDate}
                                                        toDate = {toDate}
                                                        activeTable={AnalyticsTable.AFFINITY_GROUPS}
                                                        activeView={AnalyticsActiveView.TIER_ANALYTICS}
                                                    />
                                                </AnalyticsAccessControl>
                                            </Card.Body>
                                        </Card>
                                    </Col>
                                    <Col lg={6} md={6} sm={6} xs={6}>
                                        <Card className='h-100'>
                                            <Card.Body className='h-100'>

                                                <AnalyticsAccessControl
                                                    moduleName={AccessPermissionModuleNames.TIER_ANALYTICS}
                                                    actionNames={[
                                                        AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ViewTierPointCounts,
                                                        AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ExportTierPointCounts,
                                                    ]}
                                                    Logic={"OR"}
                                                >
                                                    <AnalyticsListView
                                                        fromDate = {fromDate}
                                                        toDate = {toDate}
                                                        activeTable={AnalyticsTable.TIERS}
                                                        activeView={AnalyticsActiveView.TIER_ANALYTICS}
                                                    />
                                                </AnalyticsAccessControl>
                                            </Card.Body>
                                        </Card>
                                    </Col>
                                </Row>
                            </>
                        }
                    />
                </Card.Body>
            </Card>
            <Row className="my-3 pb-2">
                <Col lg={6} md={6} sm={6} xs={6}>
                    <Card>
                        <Card.Body>
                            <AnalyticsAccessControl
                                moduleName={AccessPermissionModuleNames.TIER_ANALYTICS}
                                actionNames={[
                                    AccessPermissionModules[AccessPermissionModuleNames.TIER_ANALYTICS].actions.ViewTierDistribution,
                                ]}
                                Logic={"OR"}
                            >
                                <PieChartCard
                                    title={"Tier Distribution"}
                                    colors={PieChartsColorCodes.TIER}
                                    data={tierData}
                                    width={400}
                                    isLoading={isLoadingTier}
                                />
                            </AnalyticsAccessControl>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </>
    )
}

export default TiersAnalytics
