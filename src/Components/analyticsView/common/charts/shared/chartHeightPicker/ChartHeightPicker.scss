.chart-height-picker-view {
    .chart-height {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        width: auto;
        border: 0.125rem solid #11355c;
        padding: 0.25rem 0.5rem;

        .chart-height-button {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            font-size: 0.9rem;
            border-radius: 0.1875rem;
            font-weight: 400;
            color: #11355c;
            &:hover,
            &:active,
            &:focus {
                background-color: #11355c;
                color: #ffffff;
            }
        }
        .chart-height-button-active {
            background-color: #11355c;
            color: #ffffff;
        }
    }

    .chart-height-disabled {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        width: auto;
        border: 0.125rem solid #a0a6ab;
        padding: 0.25rem 0.5rem;

        .chart-height-button {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            font-size: 0.9rem;
            border-radius: 0.1875rem;
            font-weight: 400;
            color: #a0a6ab;
        }
        .chart-height-button-active {
            background-color: #a0a6ab;
            color: #ffffff;
        }
    }
}
