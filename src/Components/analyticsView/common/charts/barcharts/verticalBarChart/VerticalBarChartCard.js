import React, { useEffect, useState } from "react";
import {
    <PERSON><PERSON>,
    Col,
    Form,
    Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import DefaultGraphImage from "assets/images/defaultImages/Graph 01.svg";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { LoadingComponent } from "Components/utils/UtilComponents";
import DateBucketFilter from "../../../../shared/dateBucketFilter/DateBucketFilter";
import VerticalBarChart from "./VerticalBarChart";
import AnalyticsAccessControl from "../../../../AnalyticsAccessControl";

import "./VerticalBarChartCard.scss";

const VerticalBarChartCard = ({
    isLoading,
    title,
    yAxisData,
    yAxisData2,
    xAxisData,
    colors,
    isLoadingFilters,
    selectedOptionOne,
    setSelectedOptionOne,
    selectedOptionTwo,
    setSelectedOptionTwo,
    count,
    legend,
    filterIsVisible = false,
    setDateBucket,
    fromDate,
    setFromDate,
    toDate,
    setToDate,
    setDateBuckets,
    dateBuckets,
    isVisibleDateFilter,
    optionsOne,
    optionsTwo = [],
    secondFilterVisible,
    selectedRegion,
    activeExport,
    dateBucket,
    clearFilter,
    applyFilter,
    filterButtonVisibility,
    activeView,
    groupBy = "",
    placeholderOptionTwo = "",
    isVisibleDateBucketFilter=true
}) => {
    const [isDataAvailable, setIsDataAvailable] = useState(false);

    useEffect(() => {
        setIsDataAvailable(
            (yAxisData && yAxisData.length > 0) ||
                (yAxisData2 && yAxisData2.length > 0)
        );
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [yAxisData, yAxisData2]);

    return (
        <div className="vertical-bar-chart px-4">
            <h3 className="mt-1 mx-3">{title}</h3>
            <Row>
                <AnalyticsAccessControl
                    moduleName={AccessPermissionModuleNames[activeView]}
                    actionNames={[
                        AccessPermissionModules[
                            AccessPermissionModuleNames.CARD_ANALYTICS
                        ].actions.ViewCardActivationReport,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.MEMBER_ANALYTICS
                        ].actions.ViewNewReturnMemberSeries,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.MERCHANT_ANALYTICS
                        ].actions.ViewMerchantsTransactionCollectionSeries,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.MERCHANT_ANALYTICS
                        ].actions.ViewMerchantsTransactionRedemptionSeries,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.MERCHANT_ANALYTICS
                        ].actions.ViewMerchantsTransactionAdjustmentSeries,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.REWARD_ANALYTICS
                        ].actions.ViewRedeemedRewardsSeries,
                    ]}
                    Logic={"OR"}
                    renderEmpty={true}
                >
                    <Col lg={9} md={9} sm={9} xs={9}>
                        <Row>
                            <Col lg={12} md={12} sm={12} xs={12}>
                                <h5 className="mt-1 mx-3">
                                    Processed Between {fromDate} to {toDate}
                                </h5>
                            </Col>
                        </Row>
                        {filterIsVisible && (
                            <div className="d-flex align-items-center mt-5 mx-3">
                                <div className="w-25">
                                    <Form.Select
                                        labelKey="label"
                                        onChange={setSelectedOptionOne}
                                        options={optionsOne}
                                        disabled={isLoading || isLoadingFilters}
                                        placeholder="Filter By"
                                        selected={selectedOptionOne}
                                        size="sm"
                                    />
                                </div>
                                {secondFilterVisible && (
                                    <div className="ml-3 w-50">
                                        <Form.Select
                                            labelKey="label"
                                            onChange={setSelectedOptionTwo}
                                            options={optionsTwo}
                                            disabled={
                                                isLoading ||
                                                isLoadingFilters ||
                                                optionsTwo.length === 0
                                            }
                                            placeholder={
                                                placeholderOptionTwo ||
                                                "Filter By"
                                            }
                                            selected={selectedOptionTwo}
                                            size="sm"
                                            groupBy={groupBy}
                                        />
                                    </div>
                                )}
                                {filterButtonVisibility &&
                                    selectedOptionOne &&
                                    selectedOptionOne.length > 0 && (
                                        <>
                                            <div className="text-center">
                                                <Button
                                                    variant="outline-primary"
                                                    size="sm"
                                                    className="ml-3 mr-2"
                                                    disabled={isLoading}
                                                    onClick={applyFilter}
                                                >
                                                    Apply Filter
                                                </Button>
                                                <Button
                                                    variant="outline-danger"
                                                    size="sm"
                                                    disabled={isLoading}
                                                    onClick={clearFilter}
                                                >
                                                    Clear Filter
                                                </Button>
                                            </div>
                                        </>
                                    )}
                            </div>
                        )}
                    </Col>
                </AnalyticsAccessControl>
                {isVisibleDateBucketFilter&& <Col lg={3} md={3} sm={3} xs={3}>
                    <DateBucketFilter
                        isLoading={isLoading}
                        setDateBucket={setDateBucket}
                        fromDate={fromDate}
                        setFromDate={setFromDate}
                        toDate={toDate}
                        setToDate={setToDate}
                        setDateBuckets={setDateBuckets}
                        dateBuckets={dateBuckets}
                        isVisibleDateFilter={isVisibleDateFilter}
                        selectedRegion={selectedRegion}
                        activeExport={activeExport}
                        dateBucket={dateBucket}
                        selectedOptionOne={selectedOptionOne}
                        selectedOptionTwo={selectedOptionTwo}
                        isDataAvailable={isDataAvailable}
                        activeView={activeView}
                    />
                </Col>}
            </Row>
            <AnalyticsAccessControl
                moduleName={AccessPermissionModuleNames[activeView]}
                actionNames={[
                    AccessPermissionModules[
                        AccessPermissionModuleNames.CARD_ANALYTICS
                    ].actions.ViewCardActivationReport,
                    AccessPermissionModules[
                        AccessPermissionModuleNames.MEMBER_ANALYTICS
                    ].actions.ViewNewReturnMemberSeries,
                    AccessPermissionModules[
                        AccessPermissionModuleNames.MERCHANT_ANALYTICS
                    ].actions.ViewMerchantsTransactionCollectionSeries,
                    AccessPermissionModules[
                        AccessPermissionModuleNames.MERCHANT_ANALYTICS
                    ].actions.ViewMerchantsTransactionRedemptionSeries,
                    AccessPermissionModules[
                        AccessPermissionModuleNames.REWARD_ANALYTICS
                    ].actions.ViewRedeemedRewardsCount,
                ]}
                Logic={"OR"}
                renderEmpty={true}
            >
                <>{count && <h2 className="mt-1 mx-3">{count}</h2>}</>
            </AnalyticsAccessControl>
            {isLoading ? (
                <LoadingComponent />
            ) : (
                <>
                    <AnalyticsAccessControl
                        moduleName={AccessPermissionModuleNames[activeView]}
                        actionNames={[
                            AccessPermissionModules[
                                AccessPermissionModuleNames.CARD_ANALYTICS
                            ].actions.ViewCardActivationReport,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.MEMBER_ANALYTICS
                            ].actions.ViewNewReturnMemberSeries,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.MERCHANT_ANALYTICS
                            ].actions.ViewMerchantsTransactionCollectionSeries,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.MERCHANT_ANALYTICS
                            ].actions.ViewMerchantsTransactionRedemptionSeries,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.MERCHANT_ANALYTICS
                            ].actions.ViewMerchantsTransactionAdjustmentSeries,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.REWARD_ANALYTICS
                            ].actions.ViewRedeemedRewardsSeries,
                        ]}
                        Logic={"OR"}
                    >
                        <Col lg={12} md={12} sm={12} xs={12}>
                            {isDataAvailable ? (
                                <VerticalBarChart
                                    yAxisData={yAxisData}
                                    yAxisData2={yAxisData2}
                                    xAxisData={xAxisData}
                                    colors={colors}
                                    legend={legend}
                                />
                            ) : (
                                <img
                                    src={DefaultGraphImage}
                                    className="img-fluid p-4"
                                    alt="default graph"
                                />
                            )}
                        </Col>
                    </AnalyticsAccessControl>
                </>
            )}
        </div>
    );
};

export default VerticalBarChartCard;
