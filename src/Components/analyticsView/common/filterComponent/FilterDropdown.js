import React from 'react';
import { Form } from '@shoutout-labs/shoutout-themes-enterprise';
import PropTypes from 'prop-types';

const FilterDropdown = ({
    onChangeSelect,
    selectOptions,
    placeHolder,
    selectedValue,
    labelKey="label"
}) => {
    return (
        <Form.Select
            id="basic-typeahead-single"
            labelKey={labelKey}
            onChange={onChangeSelect}
            options={selectOptions}
            placeholder={placeHolder}
            selected={selectedValue}
            size="sm"
        />
    );
};

FilterDropdown.propTypes = {

    onChangeSelect : PropTypes.func.isRequired,
    selectOptions : PropTypes.array.isRequired,
    placeHolder : PropTypes.string,
    selectedValue : PropTypes.array.isRequired,
    labelKey:PropTypes.string
}

export default FilterDropdown;
