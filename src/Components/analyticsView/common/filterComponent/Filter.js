import React, {useCallback, useMemo,useEffect,useState } from "react";
import {Button} from "@shoutout-labs/shoutout-themes-enterprise";
import FilterDropdown from './FilterDropdown';
import PropTypes from "prop-types";
import { AgeGroupFilter, GenderFilter} from "Data";


const Filter = ({
    selectedOption,
    setSelectedOption,
    applyFilter,
    skip,
    limit,
    options,
    setSelectedAge,
    setSelectedGender,
    selectedAge,
    selectedGender
}) => {
    const [resetFilter, setResetFilter] = useState(false);
    const onSelectOption = useCallback((e) => {
        setSelectedOption(e)

    },[setSelectedOption]);

    const onSelectAge = useCallback((e) => {
        setSelectedAge(e)

    },[setSelectedAge]);

    const onSelectGender = useCallback((e) => {
        setSelectedGender(e)

    },[setSelectedGender]);

    const onclickFilter = useCallback(async()=> {

        await applyFilter({ skip: skip , limit : limit });

    },[
        applyFilter,
        skip,
         limit
    ]);

    const clearFilter = useCallback(()=> {
        setSelectedAge([]);
        setSelectedGender([]);
        setSelectedOption([]);
        setResetFilter(true)
    },[setSelectedAge, setSelectedGender, setSelectedOption]);

    const selectedFilter = useMemo(() => {

        switch(selectedOption[0]?.value){
           case "Age" : {
               return (
                    <FilterDropdown
                        onChangeSelect={onSelectAge}
                        selectOptions={AgeGroupFilter}
                        placeHolder={"Select age group"}
                        selectedValue={selectedAge}
                    />
               );
           }
           case "Gender" : {
               return (
                    <FilterDropdown
                        onChangeSelect={onSelectGender}
                        selectOptions={GenderFilter}
                        placeHolder={"Select gender"}
                        selectedValue={selectedGender}
                    />
               );
           }

           default : {
               return (
                    <FilterDropdown
                        onChangeSelect={onSelectAge}
                        selectOptions={options}
                        placeHolder={"Select age group"}
                        selectedValue={selectedAge}
                    />
               );
           }
       }


      }, [
         onSelectAge,
         onSelectGender,
         options,
         selectedAge,
         selectedGender,
         selectedOption
        ]);

    useEffect(() => {
        if(resetFilter){
            applyFilter({ skip: skip , limit : limit });
            setResetFilter(false)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [resetFilter]);
    return (
        <div className="mt-3">
                    <div className="pr-2 pt-2 d-flex flex-row text-center">
                        <div className="mb-3 mr-2">
                            <FilterDropdown
                                onChangeSelect={onSelectOption}
                                selectOptions={options}
                                placeHolder={"Filter By"}
                                selectedValue={selectedOption}
                            />
                                </div>
                                {selectedOption && selectedOption.length > 0 &&
                                    <>
                                        <p className="mr-2 mt-2"> is </p>
                                        <div className="mr-2">
                                            {selectedFilter}
                                        </div>
                                        <div className="text-center">
                                            <Button
                                                variant="outline-primary"
                                                size="sm"
                                                className="ml-3 mr-2"
                                                onClick={onclickFilter}
                                            >
                                                Apply Filter
                                            </Button>
                                            <Button
                                                variant="outline-danger"
                                                size="sm"
                                                className=""
                                                onClick={clearFilter}
                                            >
                                                Clear Filter
                                            </Button>
                                        </div>
                                    </>
                                }

                            </div>
                    </div>
        )
}

Filter.propTypes = {

    selectedOption : PropTypes.arrayOf(PropTypes.object),
    setSelectedOption : PropTypes.func,
    applyFilter : PropTypes.func,
    options : PropTypes.arrayOf(PropTypes.object),
    setSelectedAge :  PropTypes.func,
    setSelectedGender :  PropTypes.func,
    selectedGender :  PropTypes.arrayOf(PropTypes.object),
    selectedAge : PropTypes.arrayOf(PropTypes.object)
  };

export default Filter;
