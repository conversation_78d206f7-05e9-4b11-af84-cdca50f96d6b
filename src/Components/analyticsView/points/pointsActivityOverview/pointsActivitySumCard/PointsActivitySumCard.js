import React from "react";
import { Card } from "@shoutout-labs/shoutout-themes-enterprise";
import { getAbbreviateNumberWithOverlay, roundOffToTwoDecimals } from "Utils";

import "./PointsActivitySumCard.scss";

const PointsActivitySumCard = ({
    statName,
    statData,
    isLoading,
    customClassName = "",
    cardKey = "",
}) => {
    return (
        <div
            data-id={cardKey}
            className={`poinst-activity-sum-card sum-card ${
                customClassName ||
                "w-25 d-flex justify-content-around align-items-center"
            }`}
        >
            <Card.Body className="d-flex flex-column align-items-center">
                <div className="font-weight-bold">{statName}</div>
                <div>
                    {isLoading ? (
                        <div className="py-3">
                            <small className="font-weight-bold">
                                <div className="d-flex align-items-center">
                                    Loading...
                                </div>
                            </small>
                        </div>
                    ) : typeof statData === "number" ? (
                        getAbbreviateNumberWithOverlay({
                            number: roundOffToTwoDecimals(statData || 0),
                            sumValueClassName: "sum-value",
                        })
                    ) : (
                        <span className="sum-value">{statData || "N/A"}</span>
                    )}
                </div>
            </Card.Body>
        </div>
    );
};

export default PointsActivitySumCard;
