import React, { useContext } from "react";
import PropTypes from "prop-types";
import { Button, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faExport, faFilter, faFilterSlash, faSync } from "FaICIconMap";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { useToggle } from "Hooks";
import QueryParamFilters from "Components/common/queryParamFilters/QueryParamFilters";
import { AnalyticsFunctionalAccessControl } from "Components/analyticsView/AnalyticsFunctionalAccessControl";

const PointsAnalyticsV2Header = ({
    analyticsHeaderName,
    fromDate,
    toDate,
    tabQueryFilterData,
    appliedFilters,
    appliedFilterRows,
    disabled,
    isDataAvailable,
    isReloading,
    isExporting,
    reloadBtnName,
    setAppliedFilters,
    setAppliedFilterRows,
    onReload,
    onExport,
}) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [showFilters, toggleShowFilters] = useToggle(false);

    return (
        <div>
            <h3 className="mb-0">
                {analyticsHeaderName || "Points Analytics"}
            </h3>
            <h5>
                Processed Between {fromDate} to {toDate}
            </h5>
            <div className="mt-3 d-flex justify-content-between align-items-center">
                <div>
                    {isReloading ? (
                        <div className="text-primary">
                            <small>Reloading...</small>
                        </div>
                    ) : (
                        <Button
                            className="p-0 shadow-none"
                            size="sm"
                            variant="link"
                            disabled={disabled || isExporting}
                            onClick={onReload}
                        >
                            <>
                                <IcIcon
                                    className="mr-2"
                                    size="md"
                                    icon={faSync}
                                />
                                {reloadBtnName || "Reload Data"}
                            </>
                        </Button>
                    )}
                </div>
                <div className="d-flex align-items-center">
                    {!showFilters && appliedFilters.length !== 0 && (
                        <div className="mr-2 d-flex align-items-center">
                            <h3 className="mb-0 mr-2">
                                {appliedFilters.length === 1
                                    ? "A filter is "
                                    : appliedFilters.length + " filters are "}
                                applied.
                            </h3>
                            <Button
                                variant="info"
                                size="sm"
                                disabled={disabled || isExporting}
                                onClick={toggleShowFilters}
                            >
                                Show Applied Filters
                            </Button>
                        </div>
                    )}
                    <Button
                        variant="outline-primary"
                        size="sm"
                        disabled={disabled || isExporting}
                        onClick={toggleShowFilters}
                    >
                        <IcIcon
                            className="mr-2"
                            size="lg"
                            icon={showFilters ? faFilterSlash : faFilter}
                        />
                        {showFilters ? "Hide Filters" : "Filter By"}
                    </Button>
                    {AnalyticsFunctionalAccessControl({
                        isAuthorizedForAction,
                        moduleName: AccessPermissionModuleNames.POINT_ANALYTICS,
                        actionNames: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.POINT_ANALYTICS
                            ].actions.ExportPointsOverviewSeries,
                        ],
                        Logic: "AND",
                    }) ? (
                        <Button
                            className="ml-2"
                            variant="dark"
                            size="sm"
                            disabled={
                                disabled || isExporting || !isDataAvailable
                            }
                            onClick={onExport}
                        >
                            <IcIcon
                                className="mr-2"
                                size="md"
                                icon={faExport}
                            />
                            {isExporting ? "Downloading..." : "Export Report"}
                        </Button>
                    ) : null}
                </div>
            </div>
            <div className="mt-3">
                {showFilters ? (
                    <QueryParamFilters
                        multipleFilters
                        queryParamFilterMetadata={
                            tabQueryFilterData?.queryParamFilterMetadata
                        }
                        queryParamFilterOptions={
                            tabQueryFilterData?.queryParamFilterOptions
                        }
                        isLoading={disabled || isExporting}
                        appliedFilters={appliedFilters}
                        appliedFilterRows={appliedFilterRows}
                        setAppliedFilters={setAppliedFilters}
                        setAppliedFilterRows={setAppliedFilterRows}
                    />
                ) : (
                    <hr />
                )}
            </div>
        </div>
    );
};

PointsAnalyticsV2Header.defaultProps = {
    analyticsHeaderName: "",
    fromDate: "",
    toDate: "",
    tabQueryFilterData: {},
    appliedFilters: [],
    appliedFilterRows: [],
    disabled: false,
    isDataAvailable: false,
    isReloading: false,
    isExporting: false,
    reloadBtnName: "",
    setAppliedFilters: () => {},
    setAppliedFilterRows: () => {},
    onReload: () => {},
    onExport: () => {},
};

PointsAnalyticsV2Header.propTypes = {
    analyticsHeaderName: PropTypes.string,
    fromDate: PropTypes.string,
    toDate: PropTypes.string,
    tabQueryFilterData: PropTypes.object,
    appliedFilters: PropTypes.array,
    appliedFilterRows: PropTypes.array,
    disabled: PropTypes.bool,
    isDataAvailable: PropTypes.bool,
    isReloading: PropTypes.bool,
    isExporting: PropTypes.bool,
    reloadBtnName: PropTypes.string,
    setAppliedFilters: PropTypes.func,
    setAppliedFilterRows: PropTypes.func,
    onReload: PropTypes.func,
    onExport: PropTypes.func,
};

export default PointsAnalyticsV2Header;
