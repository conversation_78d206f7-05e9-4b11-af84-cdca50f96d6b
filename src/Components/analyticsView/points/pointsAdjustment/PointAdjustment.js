import React, { useCallback, useContext, useEffect, useState } from "react";
import { Card, Col, Row } from "react-bootstrap";
import { toast } from "react-toastify";
import { UserContext } from "Contexts";
import { ChartColorCodes, MerchantGroupByTypes, PieChartsColorCodes } from "Data";
import { getAllMerchantLocations, getMerchantAdjustmentAsCount, getMerchantAdjustmentAsSeries, getMerchantList } from "Services";
import { toTitleCase } from "Utils";
import CommonAnalyticsPage from "Components/analyticsView/layouts/CommonAnalyticsPage";
import VerticalBarChartCard from "Components/analyticsView/common/charts/barcharts/verticalBarChart/VerticalBarChartCard";
import HorizontalBarChartCard from "Components/analyticsView/common/charts/barcharts/horizontalBarChart/HorizontalBarChartCard";
import PieChartCard from "Components/analyticsView/common/charts/piecharts/PieChartCard";
import {
    convertDataToPieChartFormat,
    getSumOfData,
    sortAscendingOrder
} from "Components/analyticsView/utils/AnalyticsUtility";

const defaultLimit = 50, defaultSkip = 0;
const defaultMerchantFilter = { "All Merchants" : { id : "" } };
const defaultSelectedMerchant = "All Merchants";
const defaultLocationFilter = { "All Locations" : { id : "" } };
const defaultSelectedLocation = "All Locations";

const PointsAdjustment = ({ setTabIsLoading }) => {
    const { selectedRegion } = useContext(UserContext);
    const [pointAdjustmentData, setPointAdjustmentData] = useState();
    const [pointAdjustmentSum, setPointAdjustmentSum] = useState(0);
    const [locationsPointAdjustmentData, setLocationsPointAdjustmentData] = useState();
    const [locationsPointAdjustmentSum, setLocationsPointAdjustmentSum] = useState(0);
    const [pointAdjustmentBySubTransactionsData, setPointAdjustmentBySubTransactionsData] = useState([]);
    const [pointAdjustmentByAccountData, setPointAdjustmentByAccountData] = useState([]);
    const [pointAdjustmentByAgeData, setPointAdjustmentByAgeData] = useState([]);
    const [pointAdjustmentByGenderData, setPointAdjustmentByGenderData] = useState([]);
    const [isLoadingAdjustmentData, setIsLoadingAdjustmentData] = useState(false);
    const [isLoadingLocationsData, setIsLoadingLocationsData] = useState(false);
    const [isLoadingSubTransactionData, setIsLoadingSubTransactionData] = useState(false);
    const [isLoadingByAccount, setIsLoadingByAccount] = useState(false);
    const [isLoadingByAge, setIsLoadingByAge] = useState(false);
    const [isLoadingByGender, setIsLoadingByGender] = useState(false);
    const [fromDate, setFromDate] = useState("");
    const [toDate, setToDate] = useState("");
    const [merchantFilters, setMerchantFilters] = useState(defaultMerchantFilter);
    const [selectedMerchantFilter, setSelectedMerchantFilter] = useState(defaultSelectedMerchant);
    const [isLoadingMerchantFilters, setIsLoadingMerchantFilters] = useState(false);
    const [isReloadingMerchantFilters, setIsReloadingMerchantFilters] = useState(false);
    const [merchantLocationFilters, setMerchantLocationFilters] = useState(defaultLocationFilter);
    const [selectedLocationFilter, setSelectedLocationFilter] = useState(defaultSelectedLocation);
    const [isLoadingLoactionFilters, setIsLoadingLoactionFilters] = useState(false);
    const [isReloadingLoactionFilters, setIsReloadingLoactionFilters] = useState(false);

    const loadPointAdjustmentData = useCallback(async ({ filter }) => {
        let pointsPayload = {
            regionId : selectedRegion._id,
            groupBy: MerchantGroupByTypes.MERCHANT,
            dateFrom: fromDate,
            dateTo: toDate
        };
        const { id } = merchantFilters[filter];

        if(id) {
          pointsPayload = { ...pointsPayload, merchantId: id }
        }

        try {
            setTabIsLoading(true);
            setIsLoadingAdjustmentData(true);

            const pointsAdjustmentResponse = await getMerchantAdjustmentAsSeries(pointsPayload);
            const barChartData =pointsAdjustmentResponse.data.length!==0&&sortAscendingOrder({
                dataset:pointsAdjustmentResponse.data,
                fromDate:fromDate,
                toDate:toDate,
                properties:["addedPoints", "subtractedPoints", "date"]
            })
            const datasetForSum = pointsAdjustmentResponse.data.length!==0&&barChartData?.addedPoints.concat(barChartData?.subtractedPoints);

            setPointAdjustmentData(barChartData);
            setPointAdjustmentSum(getSumOfData(datasetForSum));
        } catch (e) {
            console.error(e);
            toast.error(e.message || "Failed to load point adjustment data!");
        } finally {
            setTabIsLoading(false);
            setIsLoadingAdjustmentData(false);
        }
    }, [
        selectedRegion._id,
        fromDate,
        toDate,
        merchantFilters,
        setTabIsLoading,
        setIsLoadingAdjustmentData,
        setPointAdjustmentData
    ]);

    const loadLocationsPointAdjustmentData = useCallback(async ({ filter }) => {
        let locationPointPayload = {
            regionId : selectedRegion._id,
            groupBy: MerchantGroupByTypes.MERCHANT_LOCATION,
            dateFrom: fromDate,
            dateTo: toDate
        };
        const { id } = merchantLocationFilters[filter];

        if(id) {
          locationPointPayload = { ...locationPointPayload, merchantLocationId: id }
        }

        try {
            setTabIsLoading(true);
            setIsLoadingLocationsData(true);

            const pointsAdjustmentResponse = await getMerchantAdjustmentAsSeries(locationPointPayload);
            const barChartData =pointsAdjustmentResponse.data.length!==0&&sortAscendingOrder({
                dataset:pointsAdjustmentResponse.data,
                fromDate:fromDate,
                toDate:toDate,
                properties:["addedPoints", "subtractedPoints", "date"]
            })
            const datasetForSum = pointsAdjustmentResponse.data.length!==0&&barChartData?.addedPoints.concat(barChartData?.subtractedPoints);

            setLocationsPointAdjustmentData(barChartData);
            setLocationsPointAdjustmentSum(getSumOfData(datasetForSum));
        } catch (e) {
            console.error(e);
            toast.error(e.message || "Failed to load locations point adjustment data!");
        } finally {
            setTabIsLoading(false);
            setIsLoadingLocationsData(false);
        }
    }, [
        selectedRegion._id,
        fromDate,
        toDate,
        merchantLocationFilters,
        setTabIsLoading,
        setIsLoadingLocationsData,
        setLocationsPointAdjustmentData,
        setLocationsPointAdjustmentSum
    ]);

    const loadPointAdjustmentBySubTransactionsData = useCallback(async () => {
        const payload = {
            regionId : selectedRegion._id,
            groupBy: MerchantGroupByTypes.SUB_TRANSACTION_TYPE,
            dateFrom: fromDate,
            dateTo: toDate
        };

        try {
            setTabIsLoading(true);
            setIsLoadingSubTransactionData(true);
            const pointsAdjustmentResponse = await getMerchantAdjustmentAsCount(payload);
            const dataForBarChart = pointsAdjustmentResponse.data.map(
              dataItem => ({
                name: dataItem?.subTransactionType && toTitleCase(dataItem?.subTransactionType),
                additions: dataItem?.addedPoints,
                subtractions: dataItem?.subtractedPoints
              })
            );
            setPointAdjustmentBySubTransactionsData(dataForBarChart);
        } catch (e) {
            console.error(e);
            toast.error(e.message || "Failed to load point adjustment sub transaction types data!");
        } finally {
            setTabIsLoading(false);
            setIsLoadingSubTransactionData(false);
        }
    }, [
        selectedRegion._id,
        fromDate,
        toDate,
        setTabIsLoading,
        setIsLoadingSubTransactionData,
        setPointAdjustmentBySubTransactionsData
    ]);

    const loadPointAdjustmentsByAccountData = useCallback(async () => {
        const payload = {
            regionId : selectedRegion._id,
            groupBy: MerchantGroupByTypes.MEMBER_TYPE,
            dateFrom: fromDate,
            dateTo: toDate
        };

        try {
            setTabIsLoading(true);
            setIsLoadingByAccount(true);
            const pointsAdjustmentResponse = await getMerchantAdjustmentAsCount(payload);
            setPointAdjustmentByAccountData(
                convertDataToPieChartFormat(pointsAdjustmentResponse.data, "memberType")
            );
        } catch (e) {
            console.error(e);
            toast.error(e.message || "Failed to load point adjustment by account type data!");
        } finally {
            setTabIsLoading(false);
            setIsLoadingByAccount(false);
        }
    }, [
        selectedRegion._id,
        fromDate,
        toDate,
        setTabIsLoading,
        setIsLoadingByAccount,
        setPointAdjustmentByAccountData
    ]);

    const loadPointsAdjustmentByAgeData = useCallback(async () => {
        const payload = {
            regionId : selectedRegion._id,
            groupBy: MerchantGroupByTypes.AGE,
            dateFrom: fromDate,
            dateTo: toDate
        };

        try {
            setTabIsLoading(true);
            setIsLoadingByAge(true);
            const pointsAdjustmentResponse = await getMerchantAdjustmentAsCount(payload);
            setPointAdjustmentByAgeData(
                convertDataToPieChartFormat(pointsAdjustmentResponse.data, "ageGroup")
            );
        } catch (e) {
            console.error(e);
            toast.error(e.message || "Failed to load point adjustment by age data!");
        } finally {
            setTabIsLoading(false);
            setIsLoadingByAge(false);
        }
    }, [
        selectedRegion._id,
        fromDate,
        toDate,
        setTabIsLoading,
        setIsLoadingByAge,
        setPointAdjustmentByAgeData
    ]);

    const loadPointsAdjustmentByGenderData = useCallback(async () => {
      const payload = {
        regionId : selectedRegion._id,
        groupBy: MerchantGroupByTypes.GENDER,
        dateFrom: fromDate,
        dateTo: toDate
      };

      try {
        setTabIsLoading(true);
        setIsLoadingByGender(true);
        const pointsAdjustmentResponse = await getMerchantAdjustmentAsCount(payload);
        setPointAdjustmentByGenderData(
          convertDataToPieChartFormat(pointsAdjustmentResponse.data, "gender")
        );
      } catch (e) {
        console.error(e);
        toast.error(e.message || "Failed to load point redemption by gender data!");
      } finally {
        setTabIsLoading(false);
        setIsLoadingByGender(false);
      }
    }, [
      selectedRegion._id,
      fromDate,
      toDate,
      setTabIsLoading,
      setIsLoadingByGender,
      setPointAdjustmentByGenderData
    ]);

    const loadMerchantsList = useCallback(async () => {
        const payload = {
            limit: defaultLimit,
            skip: defaultSkip,
            regionId : selectedRegion._id
        };
        const merchantFilterList = merchantFilters;

        try {
            setTabIsLoading(true);
            setIsLoadingMerchantFilters(true);
            const merchantsResponse = await getMerchantList(payload);
                merchantsResponse.items.forEach(merchant =>
                merchantFilterList[merchant?.merchantName] = { id: merchant?._id }
            );

            if(merchantsResponse.items.length !== 0) {
                setMerchantFilters(merchantFilterList);
            }
            else {
                setMerchantFilters({});
                setSelectedMerchantFilter("No Merchants Found");
            }
        } catch (e) {
            console.error(e);
            toast.error(`
                ${e.message || "Could not load merchant list"}! 
                Please refresh the filters to try again.`
            );
        } finally {
            setTabIsLoading(false);
            setIsLoadingMerchantFilters(false);
        }
    }, [
        selectedRegion._id,
        merchantFilters,
        setTabIsLoading,
        setIsLoadingMerchantFilters,
        setMerchantFilters,
        setSelectedMerchantFilter
    ]);

    const loadMerchantLocations = useCallback(async () => {
        const merchantLocationsPayload = { regionId: selectedRegion._id };
        const merchantLocationList = merchantLocationFilters;

        try {
            setTabIsLoading(true);
            setIsLoadingLoactionFilters(true);
            const merchantLocationsResponse = await getAllMerchantLocations(merchantLocationsPayload);
                merchantLocationsResponse.forEach(location =>
                merchantLocationList[location?.locationName] = { id: location?._id }
            );

            if(merchantLocationsResponse.length !== 0) {
                setMerchantLocationFilters(merchantLocationList);
            }
            else {
                setMerchantLocationFilters({});
                setSelectedLocationFilter("No Locations Found");
            }

        } catch (e) {
            console.error(e);
            toast.error(`
                ${e.message || "Could not load merchant locations list"}! 
                Please refresh the filters to try again.`
            );
        } finally {
            setTabIsLoading(false);
            setIsLoadingLoactionFilters(false);
        }
    }, [
        selectedRegion._id,
        merchantLocationFilters,
        setTabIsLoading,
        setIsLoadingLoactionFilters,
        setMerchantLocationFilters,
        setSelectedLocationFilter
    ]);

    const reloadMerchantFilters = useCallback(async () => {
        setIsReloadingMerchantFilters(true);
        await loadMerchantsList();
        setIsReloadingMerchantFilters(false);
    }, [setIsReloadingMerchantFilters, loadMerchantsList]);

    const reloadLocationFilters = useCallback(async () => {
        setIsReloadingLoactionFilters(true);
        await loadMerchantLocations();
        setIsReloadingLoactionFilters(false);
    }, [setIsReloadingLoactionFilters, loadMerchantLocations]);

    const onSetAnalyticsDateRange = useCallback((fromDate, toDate) => {
        setFromDate(fromDate);
        setToDate(toDate);
        setMerchantFilters({...defaultMerchantFilter});
        setSelectedMerchantFilter(defaultSelectedMerchant);
        setMerchantLocationFilters({...defaultLocationFilter});
        setSelectedLocationFilter(defaultSelectedLocation);
    }, [
        setFromDate,
        setToDate,
        setMerchantFilters,
        setSelectedMerchantFilter,
        setMerchantLocationFilters,
        setSelectedLocationFilter
    ]);

    useEffect(() => {
        if(fromDate && toDate) {
            loadPointAdjustmentBySubTransactionsData();
            loadPointAdjustmentsByAccountData();
            loadPointsAdjustmentByAgeData();
            loadPointsAdjustmentByGenderData();
            loadMerchantsList();
            loadMerchantLocations();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fromDate, toDate]);

    useEffect(() => {
      if(fromDate && toDate) {
        loadPointAdjustmentData({ filter: selectedMerchantFilter });
      }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fromDate, toDate, selectedMerchantFilter]);

    useEffect(() => {
      if(fromDate && toDate) {
        loadLocationsPointAdjustmentData({ filter: selectedLocationFilter });
      }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fromDate, toDate, selectedLocationFilter]);

    return (
        <CommonAnalyticsPage
        isLoading={
            isLoadingAdjustmentData ||
            isLoadingLocationsData ||
            isLoadingSubTransactionData ||
            isLoadingByAccount ||
            isLoadingByAge
        }
            pageContent={
                <>
                    <Row className="my-3 pb-2">
                    <Col lg={6} md={6} sm={12} xs={12}>
                            <Card>
                            <VerticalBarChartCard
                                title="Merchant Point Adjustment"
                                isLoading={isLoadingAdjustmentData}
                                yAxisData={pointAdjustmentData?.addedPoints || []}
                                yAxisData2={pointAdjustmentData?.subtractedPoints || []}
                                xAxisData={pointAdjustmentData?.date || []}
                                colors={[ChartColorCodes.DARK_BLUE, ChartColorCodes.ORANGE]}
                                count={isLoadingAdjustmentData ? "Loading..." : pointAdjustmentSum}
                                legend={['Added Points', 'Subtracted Points']}
                                isLoadingFilters={isLoadingMerchantFilters}
                                selectedOption={selectedMerchantFilter}
                                setSelectedOption={setSelectedMerchantFilter}
                                options={merchantFilters}
                                reloadButton
                                isReloading={isReloadingMerchantFilters}
                                reloadFilters={reloadMerchantFilters}
                            />
                            </Card>
                        </Col>
                        <Col lg={6} md={6} sm={12} xs={12}>
                            <Card>
                            <VerticalBarChartCard
                                title="Merchant Location Point Adjustment"
                                isLoading={isLoadingLocationsData}
                                yAxisData={locationsPointAdjustmentData?.addedPoints || []}
                                yAxisData2={locationsPointAdjustmentData?.subtractedPoints || []}
                                xAxisData={locationsPointAdjustmentData?.date || []}
                                legend={['Added Points', 'Subtracted Points']}
                                colors={[ChartColorCodes.PURPLE, ChartColorCodes.PINK]}
                                count={isLoadingLocationsData ? "Loading..." : locationsPointAdjustmentSum}
                                isLoadingFilters={isLoadingLoactionFilters}
                                selectedOption={selectedLocationFilter}
                                setSelectedOption={setSelectedLocationFilter}
                                options={merchantLocationFilters}
                                reloadButton
                                isReloading={isReloadingLoactionFilters}
                                reloadFilters={reloadLocationFilters}
                            />
                            </Card>
                        </Col>
                    </Row>
                    <Row className="my-3 pb-2">
                        <Col lg={12} md={12} sm={12} xs={12}>
                            <HorizontalBarChartCard
                                isLoading={isLoadingSubTransactionData}
                                barchartTitle="Point Adjustment by Sub Transaction Types"
                                data={pointAdjustmentBySubTransactionsData}
                                xKey1="addedPoints"
                                xKey2="subtractions"
                                yKey="name"
                                barColors={ChartColorCodes.GREEN}
                            />
                        </Col>
                    </Row>
                    <Row className="my-3 pb-2">
                        <Col>
                            <Card>
                                <Card.Body>
                                <Row>
                                    <Col lg={4} md={4} sm={12} xs={12}>
                                        <PieChartCard
                                            title="Point Adjustment by Account Type"
                                            colors={PieChartsColorCodes.POINT_ADJUSTMENT_BY_ACC_TYPES}
                                            data={pointAdjustmentByAccountData}
                                            width={420}
                                            isLoading={isLoadingByAccount}
                                        />
                                    </Col>
                                    <Col lg={4} md={4} sm={12} xs={12}>
                                        <PieChartCard
                                            title="Point Adjustment by Age"
                                            colors={PieChartsColorCodes.POINT_ADJUSTMENT_BY_ACC_AGE}
                                            data={pointAdjustmentByAgeData}
                                            width={400}
                                            isLoading={isLoadingByAge}
                                        />
                                    </Col>
                                    <Col lg={4} md={4} sm={12} xs={12}>
                                    <PieChartCard
                                        title="Point Adjustment by Gender"
                                        colors={PieChartsColorCodes.POINT_ADJUSTMENT_BY_ACC_GENDER}
                                        data={pointAdjustmentByGenderData}
                                        width={400}
                                        isLoading={isLoadingByGender}
                                    />
                                    </Col>
                                </Row>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </>
            }
            setDateFilter={onSetAnalyticsDateRange}
        />
    );
};

export default PointsAdjustment;
