import React, { useCallback, useContext, useState } from "react";
import { toast } from "react-toastify";
import moment from "moment";
import { Heading } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { formatToCommonFormat } from "Utils";
import BaseLayout from "Layout/BaseLayout";
import {
    DateBucketData,
    DaysDB as Days,
    DefaultDateBuckets,
    MonthsDB as Months,
    QuatersDB as Quater,
    WeeksDB as Weeks,
    YearsDB as Years,
} from "Components/analyticsView/data";
import FilterDateRangeWidget from "Components/common/queryParamFilters/widgets/filterDateRangeWidget/FilterDateRangeWidget";
import { subtractTwoDays } from "../utils/AnalyticsUtility";
import PointView from "./pointView/PointView";
import PointsActivityOverview from "./pointsActivityOverview/PointsActivityOverview";
import PointsActivityAccount from "./pointsActivityAccount/PointsActivityAccount";
import AnalyticsAccessControl from "../AnalyticsAccessControl";
import { AnalyticsFunctionalAccessControl } from "../AnalyticsFunctionalAccessControl";

import "./PointsAnalyticsV2.scss";

const PointsAnalyticsV2 = () => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [disableParentComponents, setDisableParentComponents] =
        useState(false);
    const [isCalenderShown, setIsCalenderShown] = useState(false);
    const [fromDate, setFromDate] = useState(
        moment().subtract(1, "M").format("YYYY-MM-DD")
    );
    const [toDate, setToDate] = useState(moment().format("YYYY-MM-DD"));
    const [dateBucketForDateRange, setDateBucketForDateRange] = useState(
        DateBucketData.DAY
    );
    const [dateBucketsForDateRange, setDateBucketsForDateRange] =
        useState(DefaultDateBuckets);

    const onSelectDatePeriod = useCallback(
        (dateRange) => {
            try {
                const { days, weeks, months, years } = subtractTwoDays({
                    selection: dateRange,
                });

                setDateBucketsForDateRange([
                    ...(days !== 0 ? Days : []),
                    ...(weeks !== 0 ? Weeks : []),
                    ...(months !== 0 ? Months : []),
                    ...(months > 4 ? Quater : []),
                    ...(years !== 0 ? Years : []),
                ]);
                setDateBucketForDateRange(
                    years !== 0 ? DateBucketData.MONTH : DateBucketData.DAY
                );

                setFromDate(
                    dateRange?.startDate
                        ? formatToCommonFormat(dateRange.startDate)
                        : ""
                );
                setToDate(
                    dateRange?.endDate
                        ? formatToCommonFormat(dateRange.endDate)
                        : ""
                );
                setIsCalenderShown(false);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to set date filters!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            setDateBucketForDateRange,
            setFromDate,
            setIsCalenderShown,
            setToDate,
            setDateBucketsForDateRange,
        ]
    );

    return (
        <div className="w-100 points-analytics-v2">
            <BaseLayout
                topLeft={<Heading text="Analytics &gt; Points" />}
                bottom={
                    <div>
                        {AnalyticsFunctionalAccessControl({
                            isAuthorizedForAction,
                            moduleName:
                                AccessPermissionModuleNames.MERCHANT_ANALYTICS,
                            actionNames: [
                                AccessPermissionModules[
                                    AccessPermissionModuleNames
                                        .MERCHANT_ANALYTICS
                                ].actions
                                    .ViewMerchantsTransactionAdjustmentCount,
                                AccessPermissionModules[
                                    AccessPermissionModuleNames
                                        .MERCHANT_ANALYTICS
                                ].actions
                                    .ViewMerchantsTransactionCollectionCount,
                                AccessPermissionModules[
                                    AccessPermissionModuleNames
                                        .MERCHANT_ANALYTICS
                                ].actions
                                    .ViewMerchantsTransactionRedemptionCount,
                            ],
                            Logic: "OR",
                        }) ||
                        AnalyticsFunctionalAccessControl({
                            isAuthorizedForAction,
                            moduleName:
                                AccessPermissionModuleNames.POINT_ANALYTICS,
                            actionNames: [
                                AccessPermissionModules[
                                    AccessPermissionModuleNames.POINT_ANALYTICS
                                ].actions.ViewPointsSummaryByTypeCount,
                                AccessPermissionModules[
                                    AccessPermissionModuleNames.POINT_ANALYTICS
                                ].actions.ViewPointsOverviewCount,
                                AccessPermissionModules[
                                    AccessPermissionModuleNames.POINT_ANALYTICS
                                ].actions.ViewPointsOverviewSeries,
                                AccessPermissionModules[
                                    AccessPermissionModuleNames.POINT_ANALYTICS
                                ].actions.ExportPointsOverviewSeries,
                            ],
                            Logic: "OR",
                        }) ? (
                            <>
                                {AnalyticsFunctionalAccessControl({
                                    isAuthorizedForAction,
                                    moduleName:
                                        AccessPermissionModuleNames.POINT_ANALYTICS,
                                    actionNames: [
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames
                                                .POINT_ANALYTICS
                                        ].actions.ViewPointsSummaryByTypeCount,
                                    ],
                                    Logic: "AND",
                                }) ? (
                                    <PointView />
                                ) : (
                                    <h4 className="text-danger text-center rounded grey-bg p-3">
                                        You are not authorized to view this
                                        content
                                    </h4>
                                )}
                                <hr />
                                <div className="h-100">
                                    {AnalyticsFunctionalAccessControl({
                                        isAuthorizedForAction,
                                        moduleName:
                                            AccessPermissionModuleNames.MERCHANT_ANALYTICS,
                                        actionNames: [
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames
                                                    .MERCHANT_ANALYTICS
                                            ].actions
                                                .ViewMerchantsTransactionAdjustmentCount,
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames
                                                    .MERCHANT_ANALYTICS
                                            ].actions
                                                .ViewMerchantsTransactionCollectionCount,
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames
                                                    .MERCHANT_ANALYTICS
                                            ].actions
                                                .ViewMerchantsTransactionRedemptionCount,
                                        ],
                                        Logic: "OR",
                                    }) ||
                                    AnalyticsFunctionalAccessControl({
                                        isAuthorizedForAction,
                                        moduleName:
                                            AccessPermissionModuleNames.POINT_ANALYTICS,
                                        actionNames: [
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames
                                                    .POINT_ANALYTICS
                                            ].actions.ViewPointsOverviewCount,
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames
                                                    .POINT_ANALYTICS
                                            ].actions.ViewPointsOverviewSeries,
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames
                                                    .POINT_ANALYTICS
                                            ].actions
                                                .ExportPointsOverviewSeries,
                                        ],
                                        Logic: "OR",
                                    }) ? (
                                        <FilterDateRangeWidget
                                            isCalenderShown={isCalenderShown}
                                            fromDate={fromDate}
                                            toDate={toDate}
                                            disabled={disableParentComponents}
                                            onSelectDateRange={
                                                onSelectDatePeriod
                                            }
                                        />
                                    ) : null}
                                    <div>
                                        <AnalyticsAccessControl
                                            moduleName={
                                                AccessPermissionModuleNames.POINT_ANALYTICS
                                            }
                                            actionNames={[
                                                AccessPermissionModules[
                                                    AccessPermissionModuleNames
                                                        .POINT_ANALYTICS
                                                ].actions
                                                    .ViewPointsOverviewCount,
                                                AccessPermissionModules[
                                                    AccessPermissionModuleNames
                                                        .POINT_ANALYTICS
                                                ].actions
                                                    .ViewPointsOverviewSeries,
                                            ]}
                                            Logic="OR"
                                            renderEmpty
                                        >
                                            <PointsActivityOverview
                                                fromDate={fromDate}
                                                toDate={toDate}
                                                dateBucketForDateRange={
                                                    dateBucketForDateRange
                                                }
                                                dateBucketsForDateRange={
                                                    dateBucketsForDateRange
                                                }
                                                disableParentComponents={
                                                    disableParentComponents
                                                }
                                                setDisableParentComponents={
                                                    setDisableParentComponents
                                                }
                                                setDateBucketForDateRange={
                                                    setDateBucketForDateRange
                                                }
                                            />
                                            <hr />
                                        </AnalyticsAccessControl>
                                        {AnalyticsFunctionalAccessControl({
                                            isAuthorizedForAction,
                                            moduleName:
                                                AccessPermissionModuleNames.MERCHANT_ANALYTICS,
                                            actionNames: [
                                                AccessPermissionModules[
                                                    AccessPermissionModuleNames
                                                        .MERCHANT_ANALYTICS
                                                ].actions
                                                    .ViewMerchantsTransactionAdjustmentCount,
                                                AccessPermissionModules[
                                                    AccessPermissionModuleNames
                                                        .MERCHANT_ANALYTICS
                                                ].actions
                                                    .ViewMerchantsTransactionCollectionCount,
                                                AccessPermissionModules[
                                                    AccessPermissionModuleNames
                                                        .MERCHANT_ANALYTICS
                                                ].actions
                                                    .ViewMerchantsTransactionRedemptionCount,
                                            ],
                                            Logic: "OR",
                                        }) ? (
                                            <div className="mt-3">
                                                <PointsActivityAccount
                                                    fromDate={fromDate}
                                                    toDate={toDate}
                                                    setTabIsLoading={
                                                        setDisableParentComponents
                                                    }
                                                />
                                            </div>
                                        ) : (
                                            <h4 className="text-danger text-center">
                                                You are not authorized to view
                                                this content
                                            </h4>
                                        )}
                                    </div>
                                </div>
                            </>
                        ) : (
                            <h4 className="text-danger text-center">
                                You are not authorized to view this content
                            </h4>
                        )}
                    </div>
                }
            />
        </div>
    );
};

export default PointsAnalyticsV2;
