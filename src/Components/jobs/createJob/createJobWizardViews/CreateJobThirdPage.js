import React, { use<PERSON><PERSON>back, useContext, useImperativeHandle, useMemo, useRef, useState } from "react";
import JSONSchemaForm from "react-jsonschema-form";
import { DataContext } from "Contexts";
import { ProcessType, RecurrenceMethod, RecurrenceType, ScheduleType, } from "Data";
import { formatToCommonReadableFormatDateOnly, getNextExecutionDate, isEmptyObject, toTitleCase, } from "Utils";
import { CreateJobContext } from "../context/CreateJobContext";
import DateInputWidget from "Components/utils/widgets/dateInputWidgets/DateInputWidget";
import DateRangeInputWidget from "Components/utils/widgets/dateInputWidgets/DateRangeInputWidget";
import ValidMerchantsWidget from "Components/utils/widgets/validMerchantsWidget/ValidMerchantsWidget";
import TransactionTypesWidget from "Components/utils/widgets/transactionTypesWidgets/TransactionTypesWidget";
import SubTransactionTypesWidget from "Components/utils/widgets/transactionTypesWidgets/SubTransactionTypesWidget";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";
import { Button, Form, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import {faTimes } from "../../../../FaICIconMap";

const DefaultUiOrderWithAllProperties = ["merchants", "transactionTypes", "subTransactionTypes", "asAtDate","cardNumbers", "fromDate", "toDate",];

const CreateJobThirdPage = (props, ref) => {
    const { jsonSchemaForJobTypes } = useContext(DataContext);
    const { jobType, processType, scheduleType, scheduledDate, recurringMethod, recurringFrequency, executionDetails, hours, minutes, timeOfDay, selectedDayOfMonth, formData, setFormData,setMetaData } = useContext(CreateJobContext);
    const [validated, setValidated] = useState(false);
    const formRef = useRef();


    const jsonSchema = useMemo(() => jobType.length !== 0 &&
        jsonSchemaForJobTypes.find(
                (schema) => schema?.value === jobType[0]?.id

        ), [jobType, jsonSchemaForJobTypes]);


    const validMetadata = useMemo(() => {
        if (jsonSchema && jsonSchema?.metadata && typeof jsonSchema?.metadata === "object" && !isEmptyObject(jsonSchema.metadata)) {
            setMetaData(jsonSchema.metadata);
            if (
                (processType === ProcessType.SCHEDULED && scheduleType === ScheduleType.RECURRING && jobType.length !== 0 && jobType[0]?.name === "Movements Summary")||
                (processType === ProcessType.SCHEDULED && scheduleType === ScheduleType.RECURRING && jobType.length !== 0 && jobType[0]?.name === "Movements Detailed")
            ) {
                const propsToFilterOut = ["fromDate", "toDate"];
                let modifiedSchemaProperties;
                let modifiedSchemaRequired;

                if (jsonSchema.metadata?.properties) {
                    modifiedSchemaProperties = Object.entries(jsonSchema.metadata.properties).filter(
                        ([property, value]) =>
                                !propsToFilterOut.includes(property)
                        )
                        .reduce((obj, [key, value]) => {
                            obj[key] = value;
                            return obj;
                        }, {});
                }
                if (
                    jsonSchema.metadata?.required &&
                    Array.isArray(jsonSchema.metadata?.required) &&
                    jsonSchema.metadata?.required.length !== 0
                ) {
                    modifiedSchemaRequired =
                        jsonSchema.metadata.required.filter(
                            (item) => !propsToFilterOut.includes(item)
                        );
                }
                return {
                    ...jsonSchema.metadata,
                    ...(modifiedSchemaProperties
                        ? { properties: modifiedSchemaProperties }
                        : {}),
                    ...(modifiedSchemaRequired
                        ? { required: modifiedSchemaRequired }
                        : {}),
                };
            } else {
                return jsonSchema.metadata;
            }
        }

    }, [jobType, jsonSchema, processType, scheduleType,setMetaData]);


    const onChangeFormData = useCallback((data) =>{
            setValidated(true);
            setFormData({
                formData: {
                    ...data.formData,
                    ...(formData?.transactionTypes !==
                    data.formData?.transactionTypes
                        ? { subTransactionTypes: "" }
                        : {}),
                },
            })},
        [formData, setFormData,setValidated]
    );


    const arrayFieldTemplate = useCallback((props)=>{
        switch(props?.title) {
            case "Card Numbers":
                return <>
                        <Form.Group controlId="notification-emails">
                            <Form.Label className="mb-3">Card Numbers</Form.Label>
                             <>
                                 {props.items.map(
                                     element =><div className="d-flex">
                                                  <div className="w-100">
                                                      {element.children}
                                                  </div>
                                                  <div>
                                                      {element.hasRemove &&<div className="ml-2 mt-1 d-flex justify-content-end w-100">
                                                          <Button
                                                              className="text-danger"
                                                              variant="link"
                                                              size="sm"
                                                              onClick={element.onDropIndexClick(element.index)}
                                                          >
                                                              <IcIcon size="md" icon={faTimes} />
                                                          </Button>
                                                      </div>}
                                                  </div>
                                         </div>
                                     )
                                 }
                             </>
                        </Form.Group>
                        {props.canAdd &&<div className="d-flex justify-content-end w-100">
                            <Button
                                size="sm"
                                variant="link"
                                onClick={props.onAddClick}
                            >
                                +  Add new card.
                            </Button>
                        </div>}
                </>
            default:
            return <div>Array field template not found</div>
        }
    },[]);


    const configurationsContent = useMemo(() => {
        if (
            processType === ProcessType.SCHEDULED &&
            scheduleType === ScheduleType.RECURRING &&
            jobType.length !== 0 &&
            jobType[0]?.name !== "Movements Summary"&&
            jobType[0]?.name !== "Movements Detailed"
        ) {
            let nextScheduledDate = {};

            switch (recurringMethod) {
                case RecurrenceMethod.DAILY: {
                    if (hours && minutes && timeOfDay) {
                        const executionTime = `${hours}:${minutes} ${timeOfDay}`;
                        nextScheduledDate = getNextExecutionDate({
                            daily: executionTime,
                        });
                    } else {
                        nextScheduledDate = {};
                    }
                    break;
                }
                case RecurrenceMethod.MONTHLY: {
                    if (
                        (recurringFrequency === RecurrenceType.DAY_OF_MONTH &&
                            selectedDayOfMonth) ||
                        recurringFrequency === RecurrenceType.END_OF_MONTH
                    ) {
                        nextScheduledDate = getNextExecutionDate({
                            isEndOfMonth:
                                recurringFrequency ===
                                RecurrenceType.END_OF_MONTH,
                            dayOfMonth: selectedDayOfMonth,
                        });
                    } else {
                        nextScheduledDate = {};
                    }
                    break;
                }
                default:
                    nextScheduledDate = {};
            }

            return (
                <div>
                    <div>
                        <DetailsAsLabelValue
                            label="Schedule Type"
                            value={toTitleCase(scheduleType || "~ unknown")}
                        />
                    </div>
                    <div className="my-3">
                        <DetailsAsLabelValue
                            label="Execution Details"
                            value={executionDetails}
                        />
                    </div>
                    <div>
                        <DetailsAsLabelValue
                            label="Next Execution Date"
                            value={
                                nextScheduledDate?.nextDate || (
                                    <span className="text-orange">
                                        * No execution scheduled.
                                    </span>
                                )
                            }
                        />
                    </div>
                </div>
            );
        } else {
            let filteredUiOrder;

            if (validMetadata?.properties) {
                const propertiesInMetadata = Object.keys(
                    validMetadata.properties
                );
                filteredUiOrder = DefaultUiOrderWithAllProperties.filter(
                    (item) => propertiesInMetadata.includes(item)
                );
            }

            const uiSchema = {
                asAtDate: {
                    "ui:widget": "asAtDateSelector",
                    "ui:options": {
                        property: "asAtDate",
                        label: false,
                        isValidated: validated,
                        date: formData?.asAtDate,
                        inputLabel: "As at Date",
                        placeHolderText: "Select as at date",
                        warningText: "As at date cannot be empty.",
                    },
                },
                cardNumbers: {
                    "ui:widget": "cardNumbers",
                },
                fromDate: {
                    "ui:widget": "startDateSelector",
                    "ui:options": {
                        property: "fromDate",
                        label: false,
                        isValidated: validated,
                        limitedDateValue: scheduledDate,
                        isFromDate: true,
                        fromDate: formData?.fromDate,
                        toDate: formData?.toDate,
                    },
                },
                toDate: {
                    "ui:widget": "endDateSelector",
                    "ui:options": {
                        property: "toDate",
                        label: false,
                        isValidated: validated,
                        limitedDateValue: scheduledDate,
                        isToDate: true,
                        fromDate: formData?.fromDate,
                        toDate: formData?.toDate,
                    },
                },
                merchants: {
                    "ui:widget": "merchantSelector",
                    "ui:options": {
                        property: "merchants",
                        label: false,
                        isValidated: validated,
                    },
                },
                transactionTypes: {
                    "ui:widget": "transactionTypeSelector",
                    "ui:options": {
                        property: "transactionTypes",
                        label: false,
                        isValidated: validated,
                    },
                },
                subTransactionTypes: {
                    "ui:widget": "subTransactionTypeSelector",
                    "ui:options": {
                        property: "subTransactionTypes",
                        label: false,
                        isValidated: validated,
                        transactionTypes: formData?.transactionTypes,
                    },
                },
                ...(filteredUiOrder ? { "ui:order": filteredUiOrder } : {}),
            };

            return (
                <div className="container">
                    <div className="row">
                        <div className="col-md-12">
                            {validMetadata ? (
                                <JSONSchemaForm
                                    onChange={onChangeFormData}
                                    schema={validMetadata}
                                    ref={formRef}
                                    formData={formData}
                                    children={true}
                                    liveValidate={validated}
                                    uiSchema={uiSchema}
                                    showErrorList={false}
                                    ArrayFieldTemplate={arrayFieldTemplate}
                                    widgets={{
                                        asAtDateSelector: DateInputWidget,
                                        startDateSelector: DateRangeInputWidget,
                                        endDateSelector: DateRangeInputWidget,
                                        merchantSelector: ValidMerchantsWidget,
                                        transactionTypeSelector: TransactionTypesWidget,
                                        subTransactionTypeSelector: SubTransactionTypesWidget,
                                    }}
                                />
                            ) : (
                                <div>
                                    <div>
                                        <DetailsAsLabelValue
                                            label="Job Type"
                                            value={toTitleCase(processType || "~ unknown")}
                                        />
                                    </div>
                                    {scheduleType && (
                                        <>
                                            <div className="my-3">
                                                <DetailsAsLabelValue
                                                    label="Schedule Type"
                                                    value="One Time"
                                                />
                                            </div>
                                            {scheduledDate && (
                                                <div className="mt-3">
                                                    <DetailsAsLabelValue
                                                        label="Scheduled Date"
                                                        value={formatToCommonReadableFormatDateOnly(scheduledDate)}
                                                    />
                                                </div>
                                            )}
                                        </>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            );
        }
    }, [
        jobType,
        validMetadata,
        processType,
        scheduleType,
        scheduledDate,
        recurringMethod,
        recurringFrequency,
        executionDetails,
        hours,
        minutes,
        timeOfDay,
        selectedDayOfMonth,
        formData,
        validated,
        onChangeFormData,
        arrayFieldTemplate
    ]);


    useImperativeHandle(ref, () => ({
        isValidated() {
            try {
                let formValid = formRef?.current?.state?.errors.length === 0;
                const liveValidate = formRef?.current?.props?.liveValidate;

                if (formValid || liveValidate) {
                    const requiredValues =
                        formRef?.current?.props?.schema?.required || [];

                    if (requiredValues.length !== 0) {
                        requiredValues.forEach((value) => {
                            if (formData[value] === "[]") {
                                formValid = false;
                            }
                        });
                    }
                    if (!liveValidate) {
                        setValidated(true);
                    }
                    return formValid && liveValidate;
                } else {
                    return true;
                }
            } catch (e) {
                console.error(e);
            }
        },
        async onClickNext() {
            return new Promise(async (resolve, reject) => resolve());
        },
    }));


    return (
        <div ref={ref} className="mt-5">
            {configurationsContent}
        </div>
    );
};

export default CreateJobThirdPage;
