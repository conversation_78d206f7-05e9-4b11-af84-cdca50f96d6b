import React from "react";
import { Button } from "@shoutout-labs/shoutout-themes-enterprise";

import "./DayOfMonthPicker.scss";

const daysOfMonthArray = [
    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21,
    22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
];

const arrayChunksByWeek = (array, chunk_size) =>
    Array(Math.ceil(array.length / chunk_size))
        .fill()
        .map((_, index) => index * chunk_size)
        .map((begin) => array.slice(begin, begin + chunk_size));

const DayOfMonthPicker = ({ selectedDay, onSelectDay }) => (
    <div className="day-of-month-picker-view">
        {arrayChunksByWeek(daysOfMonthArray, 7).map((weekChunk, chunkIndex) => (
            <div key={`chunk-${chunkIndex}`} className="my-2">
                {weekChunk.map((day, index) => (
                    <Button
                        key={`${index}:day-${day}`}
                        className="day-button mx-2"
                        name="selectedDayOfMonth"
                        value={day}
                        size="sm"
                        variant={
                            selectedDay && Number(selectedDay) === day
                                ? "primary"
                                : "outline-primary"
                        }
                        onClick={onSelectDay}
                    >
                        <div className="m-0 d-flex justify-content-center align-items-center">
                            {day}
                        </div>
                    </Button>
                ))}
            </div>
        ))}
    </div>
);

export default DayOfMonthPicker;
