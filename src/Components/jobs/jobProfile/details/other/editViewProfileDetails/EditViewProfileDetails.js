import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import {
    Badge,
    Button,
    Form,
    Modal,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext, UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    JobStatus,
    NotificationsStatus,
    ProcessType,
    ScheduleType,
} from "Data";
import { useToggle } from "Hooks";
import { updateJobById } from "Services";
import {
    areArraysEquals,
    convertTimeFrom24HTo12H,
    formatToCommonReadableFormatDateOnly,
    getNextExecutionDate,
    getSuffixForDayOfMonth,
    isEmptyObject,
    objectToLabelValueArray,
    toTitleCase,
    toTitleCaseFromCamelCase,
} from "Utils";
import EmailsInput from "Components/common/emailInput/EmailsInput";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";

import "./EditViewProfileDetails.scss";

const NotificationsStatusOptions = objectToLabelValueArray(NotificationsStatus);

const getDetailsAsLabelValueList = (
    name,
    list = [],
    noDataMsg = "data",
    idPropName = "",
    itemPropName = "",
    customNoDataMsg = ""
) => (
    <>
        <span className="font-weight-bold">{name}</span>
        <div
            className={`scrollable-view ${
                list.length > 1 && "border rounded p-1"
            }`}
        >
            {list.length !== 0 ? (
                list.map((item, index) => {
                    let key;
                    let listItem;
                    let itemNumber = list.length > 1 ? `${index + 1}. ` : "";

                    if (
                        typeof item === "object" &&
                        !Array.isArray(item) &&
                        item !== null
                    ) {
                        key = `${index}-${
                            item[idPropName] || "~ unknown property"
                        }`;
                        listItem = `${itemNumber}${
                            item[itemPropName]
                                ? toTitleCase(item[itemPropName])
                                : "~ unknown"
                        }`;
                    } else {
                        key = `${index}-${item}`;
                        listItem = `${itemNumber}${item}`;
                    }

                    return (
                        <div
                            key={key}
                            className={`d-flex align-items-center rounded view-details ${
                                list.length > 1 && "mb-1"
                            } p-2`}
                        >
                            {listItem}
                        </div>
                    );
                })
            ) : (
                <div className="rounded view-details p-2">
                    {customNoDataMsg || `No ${noDataMsg} found.`}
                </div>
            )}
        </div>
    </>
);

const EditViewProfileDetails = ({ show, property, jobDetails, onHide }) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const { merchants, subTransactionTypes } = useContext(DataContext);
    const [validated, setValidated] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);
    const [jobName, setJobName] = useState("");
    const [jobDescription, setJobDescription] = useState("");
    const [emailsForNotifications, setEmailsForNotifications] = useState([]);
    const [notificationStatus, setNotificationStatus] = useState();
    const [isEditMode, toggelIsEditMode] = useToggle(false);

    const currentNotificationStatus = useMemo(
        () =>
            property === "notifications" &&
            jobDetails?.notificationEmails &&
            Array.isArray(jobDetails?.notificationEmails) &&
            jobDetails?.notificationEmails.length !== 0
                ? NotificationsStatus.ENABLED
                : NotificationsStatus.DISABLED,
        [property, jobDetails?.notificationEmails]
    );

    const onToggleEditMode = useCallback(() => {
        if (isEditMode) {
            if (jobDetails?.description && property === "description") {
                setJobDescription(jobDetails?.description);
            }

            if (
                jobDetails?.notificationEmails &&
                property === "notifications"
            ) {
                setEmailsForNotifications(jobDetails?.notificationEmails || []);
                setNotificationStatus(currentNotificationStatus);
            }
        }
        toggelIsEditMode();
    }, [
        property,
        jobDetails?.description,
        jobDetails?.notificationEmails,
        isEditMode,
        toggelIsEditMode,
        currentNotificationStatus,
        setJobDescription,
        setEmailsForNotifications,
        setNotificationStatus,
    ]);

    const onChange = useCallback(
        (e) => {
            const value = e.currentTarget.value;
            switch (property) {
                case "name": {
                    setJobName(value);
                    break;
                }
                case "description": {
                    setJobDescription(value);
                    break;
                }
                case "notifications": {
                    setNotificationStatus(value);

                    if (
                        value === NotificationsStatus.ENABLED &&
                        emailsForNotifications.length === 0
                    ) {
                        setEmailsForNotifications([""]);
                    }
                    break;
                }
                default:
                    break;
            }
        },
        [property, emailsForNotifications, setJobName, setJobDescription]
    );

    const addEmailItem = useCallback(() => {
        if (property === "notifications") {
            setEmailsForNotifications([...emailsForNotifications, ""]);
        }
    }, [property, emailsForNotifications, setEmailsForNotifications]);

    const removeEmailItem = useCallback(
        (e) => {
            if (property === "notifications") {
                const index = e.currentTarget.dataset.index;
                const newEmails = emailsForNotifications.filter(
                    (_, i) => i !== Number(index)
                );

                setEmailsForNotifications(newEmails);
            }
        },
        [property, emailsForNotifications, setEmailsForNotifications]
    );

    const setEmailValue = useCallback(
        (e) => {
            if (property === "notifications") {
                const emailIndex = Number(e.currentTarget.id);
                const email = e.currentTarget.value;

                setEmailsForNotifications((prevEmails) =>
                    prevEmails.map((prevEmail, i) =>
                        i === emailIndex ? email : prevEmail
                    )
                );
            }
        },
        [property, setEmailsForNotifications]
    );

    const onUpdateJob = useCallback(
        async (e) => {
            e.preventDefault();

            let payload = {};
            if (e.target.checkValidity()) {
                try {
                    switch (property) {
                        case "name": {
                            payload = {
                                name: jobName,
                            };
                            break;
                        }
                        case "description": {
                            payload = {
                                description: jobDescription,
                            };
                            break;
                        }
                        case "notifications": {
                            payload = {
                                notificationEmails:
                                    notificationStatus ===
                                    NotificationsStatus.ENABLED
                                        ? emailsForNotifications
                                        : [],
                            };
                            break;
                        }
                        default: {
                            payload = {};
                            break;
                        }
                    }
                    setIsUpdating(true);
                    const updatedJobRes = await updateJobById(
                        jobDetails?._id,
                        payload
                    );
                    setIsUpdating(false);
                    toast.success(`Successfully updated job ${property}.`);
                    onHide(null, updatedJobRes);
                } catch (e) {
                    console.error(e);
                    toast.error(
                        <div>
                            Failed to update job {property}!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                    setIsUpdating(false);
                }
            } else {
                setValidated(true);
            }
        },
        [
            property,
            jobDetails?._id,
            jobName,
            jobDescription,
            notificationStatus,
            emailsForNotifications,
            onHide,
            setIsUpdating,
            setValidated,
        ]
    );

    const editViewContent = useMemo(() => {
        let recurringFrequencyInfo = {
            dailyRecurringFrequency: "",
            selectedDayOfMonth: "",
            wordingForShceduledDate: "",
        };

        if ((property === "jobRequirement" || property === "configurations") && jobDetails?.scheduleType === ScheduleType.RECURRING) {
            let dailyRecurringFrequency = "";
            let selectedDayOfMonth = "";
            let wordingForShceduledDate = "";

            if (jobDetails?.recurringFrequency) {
                const recurringFrequencyIntoSegments =
                    jobDetails.recurringFrequency?.split(" ");

                if (jobDetails.recurringFrequency?.endsWith("* * *")) {
                    const hours = recurringFrequencyIntoSegments[2];
                    const minutes = recurringFrequencyIntoSegments[1];

                    dailyRecurringFrequency =
                        hours && minutes
                            ? convertTimeFrom24HTo12H(`${hours}:${minutes}`)
                            : "";
                    wordingForShceduledDate = `Daily at ${dailyRecurringFrequency}.`;
                } else {
                    selectedDayOfMonth = recurringFrequencyIntoSegments[3];
                    wordingForShceduledDate = selectedDayOfMonth
                        ? selectedDayOfMonth > 28
                            ? " day."
                            : " day of every month."
                        : "";
                }
            }

            recurringFrequencyInfo = {
                dailyRecurringFrequency,
                selectedDayOfMonth,
                wordingForShceduledDate,
            };
        }

        switch (property) {
            case "name": {
                return {
                    modalBody: (
                        <Form.Group>
                            <Form.Label className="d-flex align-items-center">
                                Job Name
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            <Form.Control
                                type="text"
                                name="jobName"
                                value={jobName}
                                onChange={onChange}
                                placeholder="Enter a job name"
                                required
                            />
                        </Form.Group>
                    ),
                    modalButton: (
                        <Button
                            size="sm"
                            variant="primary"
                            type="submit"
                            disabled={
                                isUpdating ||
                                jobName === jobDetails?.name ||
                                jobName === ""
                            }
                        >
                            {isUpdating ? "Updating..." : "Update"}
                        </Button>
                    ),
                };
            }
            case "description": {
                let modalButton = null;

                if (
                    jobDetails?.status !== JobStatus.COMPLETED &&
                    isAuthorizedForAction(
                        AccessPermissionModuleNames.JOBS,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.JOBS
                        ].actions.UpdateJob
                    )
                ) {
                    modalButton = isEditMode ? (
                        <>
                            <Button
                                className="mr-2"
                                size="sm"
                                variant="outline-danger"
                                disabled={isUpdating}
                                onClick={onToggleEditMode}
                            >
                                Cancel Edit
                            </Button>
                            <Button
                                size="sm"
                                variant="primary"
                                type="submit"
                                disabled={
                                    isUpdating ||
                                    jobDescription ===
                                        jobDetails?.description ||
                                    jobDescription === ""
                                }
                            >
                                {isUpdating ? "Updating..." : "Update"}
                            </Button>
                        </>
                    ) : (
                        <Button
                            size="sm"
                            variant="primary"
                            onClick={onToggleEditMode}
                        >
                            Edit {toTitleCase(property)}
                        </Button>
                    );
                }
                return {
                    modalBody: (
                        <Form.Group>
                            <Form.Label
                                className={`d-flex align-items-center ${
                                    !isEditMode && "font-weight-bold"
                                }`}
                            >
                                Job Description
                                {isEditMode && (
                                    <div className="ml-1 text-danger">*</div>
                                )}
                            </Form.Label>
                            {isEditMode ? (
                                <Form.Control
                                    as="textarea"
                                    rows={3}
                                    name="description"
                                    value={jobDescription}
                                    onChange={onChange}
                                    placeholder="Enter a description"
                                    required
                                />
                            ) : (
                                <div className="px-2 py-2 rounded view-details scrollable-view">
                                    {jobDetails?.description || "~ unknown"}
                                </div>
                            )}
                        </Form.Group>
                    ),
                    modalButton,
                };
            }
            case "jobRequirement": {
                const scheduleType =
                    jobDetails?.scheduleType === ScheduleType.ONCE
                        ? "One Time"
                        : jobDetails?.scheduleType;
                let recurringFrequencyWording = "";

                if (jobDetails?.scheduleType === ScheduleType.RECURRING) {
                    recurringFrequencyWording =
                        recurringFrequencyInfo.selectedDayOfMonth
                            ? `${
                                recurringFrequencyInfo.selectedDayOfMonth !==
                                "L"
                                    ? getSuffixForDayOfMonth(
                                            recurringFrequencyInfo.selectedDayOfMonth
                                        ) +
                                        recurringFrequencyInfo.wordingForShceduledDate
                                    : "Last day of every month."
                            }`
                            : recurringFrequencyInfo.wordingForShceduledDate;
                }

                return {
                    modalBody: (
                        <div className="mb-3">
                            <DetailsAsLabelValue
                                label="Job Requirement"
                                value={
                                    jobDetails?.processType
                                        ? `${toTitleCase(
                                            jobDetails?.processType
                                        )} Job`
                                        : "~ unknown"
                                }
                            />
                            {jobDetails?.processType ===
                                ProcessType.SCHEDULED && (
                                <>
                                    <div className="my-3">
                                        <DetailsAsLabelValue
                                            label="Schedule Type"
                                            value={
                                                scheduleType
                                                    ? toTitleCase(scheduleType)
                                                    : "~ unknown"
                                            }
                                        />
                                    </div>
                                    {jobDetails?.scheduleType ===
                                        ScheduleType.ONCE && (
                                        <div className="mt-3">
                                            <DetailsAsLabelValue
                                                label="Scheduled Date"
                                                value={
                                                    jobDetails?.scheduledDate
                                                        ? formatToCommonReadableFormatDateOnly(
                                                            jobDetails?.scheduledDate
                                                        )
                                                        : "~ unknown"
                                                }
                                            />
                                        </div>
                                    )}
                                    {jobDetails?.scheduleType ===
                                        ScheduleType.RECURRING && (
                                        <div className="mt-3">
                                            <DetailsAsLabelValue
                                                label="Recurring Frequency"
                                                value={
                                                    recurringFrequencyWording ||
                                                    "~ unknown"
                                                }
                                            />
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    ),
                    modalButton: null,
                };
            }
            case "configurations": {
                let modalBody = null;
                let dynaminMetadataContent = [];
                let dynamicConfigData = null;

                if (jobDetails?.metadata && !isEmptyObject(jobDetails?.metadata)) {
                    Object.entries(jobDetails?.metadata).forEach(([key, value]) => {
                            switch (key) {
                                case "asAtDate":
                                case "fromDate":
                                case "toDate": {
                                    const dateContent = (
                                        <div className="mt-3">
                                            <DetailsAsLabelValue label={toTitleCaseFromCamelCase(key)} value={value ? formatToCommonReadableFormatDateOnly(value) : "~ unknown"} />
                                        </div>
                                    );
                                    dynaminMetadataContent = [
                                        ...dynaminMetadataContent,
                                        dateContent,
                                    ];
                                    break;
                                }
                                case "merchants": {
                                    const currentMerchants =
                                        value && Array.isArray(value)
                                            ? merchants.filter((merchant) =>
                                                value.includes(merchant?._id)
                                            )
                                            : [
                                                {
                                                    merchantName:
                                                        "All Merchants",
                                                    _id: "ALL_MERCHANTS",
                                                },
                                            ];

                                    const merchantContent = (
                                        <div className="mt-3">
                                            <div className="details-as-label-value-view">
                                                <span className="font-weight-bold">
                                                    Merchants
                                                </span>
                                                {currentMerchants.length !==
                                                    0 &&
                                                    currentMerchants.map(
                                                        (currentMerchant) => (
                                                            <div
                                                                className="p-2 mt-2 rounded details-view"
                                                                key={`${currentMerchant._id}`}
                                                            >
                                                                {
                                                                    currentMerchant?.merchantName
                                                                }
                                                            </div>
                                                        )
                                                    )}
                                            </div>
                                        </div>
                                    );
                                    dynaminMetadataContent = [
                                        ...dynaminMetadataContent,
                                        merchantContent,
                                    ];
                                    break;
                                }
                                case "transactionTypes": {
                                    const transactionTypeContent = (
                                        <div className="mt-3">
                                            <div className="details-as-label-value-view">
                                                <span className="font-weight-bold">
                                                    Transaction Types
                                                </span>
                                                {value && value !== 0 ? (
                                                    value.map(
                                                        (transactionType) => (
                                                            <div
                                                                className="p-2 mt-2 rounded details-view"
                                                                key={`${transactionType}`}
                                                            >
                                                                {toTitleCase(
                                                                    transactionType
                                                                )}
                                                            </div>
                                                        )
                                                    )
                                                ) : (
                                                    <div className="p-2 mt-2 rounded details-view">
                                                        All Transaction Types
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    );
                                    dynaminMetadataContent = [
                                        ...dynaminMetadataContent,
                                        transactionTypeContent,
                                    ];
                                    break;
                                }
                                case "subTransactionTypes": {
                                    const filteredSubTransactionTypes =
                                        value && Array.isArray(value)
                                            ? subTransactionTypes.filter(
                                                (subTransaction) =>
                                                    value.includes(
                                                        subTransaction?._id
                                                    )
                                            )
                                            : [
                                                {
                                                    name: "All Sub Transaction Types",
                                                    _id: "ALL_SUB_TRANSACTION_TYPES",
                                                },
                                            ];

                                    const subTransactionTypesContent = (
                                        <div className="mt-3">
                                            <div className="details-as-label-value-view">
                                                <span className="font-weight-bold">
                                                     Sub Transaction Type
                                                </span>
                                                {filteredSubTransactionTypes.length !==
                                                    0 &&
                                                    filteredSubTransactionTypes.map(
                                                        (
                                                            filteredSubTransactionType
                                                        ) => (
                                                            <div
                                                                key={`${filteredSubTransactionType._id}`}
                                                                className="p-2 mt-2 rounded details-view"
                                                            >
                                                                {
                                                                    filteredSubTransactionType?.name
                                                                }
                                                            </div>
                                                        )
                                                    )}
                                            </div>
                                        </div>
                                    );
                                    dynaminMetadataContent = [
                                        ...dynaminMetadataContent,
                                        subTransactionTypesContent,
                                    ];
                                    break;
                                }
                                case "cardNumbers": {
                                    dynaminMetadataContent = [...dynaminMetadataContent, ...(value?[
                                        <div className="mt-3"  key={`all-${key}`}>
                                            <div className="details-as-label-value-view">
                                                <span className="font-weight-bold">
                                                        Card Numbers
                                                 </span>
                                                <div className="overflow-auto my-2" style={{ height: 100}}>
                                                    { value.map((card,index)=><div className="p-2 mt-2 rounded details-view" key={`card-${index}-${card}`}>{card}</div>)}
                                                </div>
                                            </div>
                                        </div>
                                    ]:[
                                        <div className="mt-3" key={`all-${key}`}>
                                            <div className="details-as-label-value-view">
                                                <span className="font-weight-bold">
                                                        Card Numbers
                                                 </span>
                                                <div className="p-2 mt-2 rounded details-view">
                                                    All Cards
                                                </div>
                                            </div>
                                       </div>
                                   ])];
                                    break;
                                }

                                default:
                                    break;
                            }
                        }
                    );
                }
                dynamicConfigData = (
                    <>
                        {dynaminMetadataContent.length !== 0 ? (
                            <div className="mb-3">
                                {dynaminMetadataContent.map(
                                    (content) => content
                                )}
                            </div>
                        ) : null}
                    </>
                );

                if (jobDetails?.scheduleType === ScheduleType.RECURRING) {
                    let executionDetails = "";

                    if (recurringFrequencyInfo.dailyRecurringFrequency) {
                        executionDetails = `The job will be executed daily at ${recurringFrequencyInfo.dailyRecurringFrequency}.`;
                    }
                    if (recurringFrequencyInfo.selectedDayOfMonth) {
                        executionDetails = `The job will be executed on the ${
                            recurringFrequencyInfo.selectedDayOfMonth !== "L"
                                ? getSuffixForDayOfMonth(
                                        recurringFrequencyInfo.selectedDayOfMonth
                                    ) +
                                    recurringFrequencyInfo.wordingForShceduledDate
                                : "last day of every month."
                        }`;
                    }

                    const nextScheduledDate =
                        getNextExecutionDate({
                            daily: recurringFrequencyInfo.dailyRecurringFrequency,
                            isEndOfMonth:
                                recurringFrequencyInfo.selectedDayOfMonth ===
                                "L",
                            dayOfMonth:
                                recurringFrequencyInfo.selectedDayOfMonth,
                        }) || {};

                    modalBody = (
                        <>
                            {dynamicConfigData}
                            <DetailsAsLabelValue
                                label="Schedule Type"
                                value={
                                    jobDetails?.scheduleType
                                        ? toTitleCase(jobDetails?.scheduleType)
                                        : "~ unknown"
                                }
                            />
                            <div className="my-3">
                                <DetailsAsLabelValue
                                    label="Execution Details"
                                    value={executionDetails || "~ unknown"}
                                />
                            </div>
                            <div className="mb-3">
                                <DetailsAsLabelValue
                                    label="Next Execution Date"
                                    value={
                                        nextScheduledDate?.nextDate || (
                                            <span className="text-orange">
                                                * No execution scheduled.
                                            </span>
                                        )
                                    }
                                />
                            </div>
                        </>
                    );
                } else {
                    modalBody = dynamicConfigData;
                }

                return {
                    modalBody,
                    modalButton: null,
                };
            }
            case "notifications": {
                let modalButton = null;

                if (
                    jobDetails?.status !== JobStatus.COMPLETED &&
                    isAuthorizedForAction(
                        AccessPermissionModuleNames.JOBS,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.JOBS
                        ].actions.UpdateJob
                    )
                ) {
                    modalButton = isEditMode ? (
                        <>
                            <Button
                                className="mr-2"
                                size="sm"
                                variant="outline-danger"
                                disabled={isUpdating}
                                onClick={onToggleEditMode}
                            >
                                Cancel Edit
                            </Button>
                            <Button
                                size="sm"
                                variant="primary"
                                type="submit"
                                disabled={
                                    isUpdating ||
                                    (notificationStatus ===
                                        NotificationsStatus.DISABLED &&
                                        currentNotificationStatus ===
                                            notificationStatus) ||
                                    (notificationStatus ===
                                        NotificationsStatus.ENABLED &&
                                        ((jobDetails?.notificationEmails &&
                                            areArraysEquals(
                                                jobDetails?.notificationEmails,
                                                emailsForNotifications
                                            )) ||
                                            emailsForNotifications.includes(
                                                ""
                                            )))
                                }
                            >
                                {isUpdating ? "Updating..." : "Update"}
                            </Button>
                        </>
                    ) : (
                        <Button
                            size="sm"
                            variant="primary"
                            onClick={onToggleEditMode}
                        >
                            Edit {toTitleCase(property)}
                        </Button>
                    );
                }
                return {
                    modalBody: (
                        <Form.Group>
                            <Form.Label
                                className={`d-flex align-items-center ${
                                    !isEditMode && "font-weight-bold"
                                }`}
                            >
                                Notifications
                                {isEditMode && (
                                    <div className="ml-1 text-danger">*</div>
                                )}
                            </Form.Label>
                            {isEditMode ? (
                                <>
                                    <Form.Group className="input-group mt-3">
                                        {NotificationsStatusOptions.map(
                                            (option, index) => (
                                                <Form.Check
                                                    key={`${index}-${option}`}
                                                    className="rounded-0 input-check mr-3"
                                                    custom
                                                    checked={
                                                        notificationStatus ===
                                                        option.value
                                                    }
                                                    disabled={isUpdating}
                                                    onChange={onChange}
                                                    value={option.value}
                                                    id={option.value}
                                                    label={option.label}
                                                    name="notificationStatus"
                                                    type="radio"
                                                    required
                                                />
                                            )
                                        )}
                                    </Form.Group>
                                    {notificationStatus ===
                                        NotificationsStatus.ENABLED && (
                                        <>
                                            <Form.Group>
                                                <div>
                                                    <Form.Label className="d-flex align-items-center">
                                                        {`Email ${
                                                            emailsForNotifications.length ===
                                                            1
                                                                ? "Address"
                                                                : "Addresses"
                                                        }`}
                                                        <div className="ml-1 text-danger">
                                                            *
                                                        </div>
                                                    </Form.Label>
                                                    <div className="text-center">
                                                        {emailsForNotifications.length <
                                                            1 &&
                                                            "No emails specified."}
                                                    </div>
                                                </div>
                                                {emailsForNotifications.map(
                                                    (item, index) => (
                                                        <EmailsInput
                                                            key={index}
                                                            index={index}
                                                            item={item}
                                                            emailsLength={
                                                                emailsForNotifications.length
                                                            }
                                                            disabled={
                                                                isUpdating
                                                            }
                                                            removeEmailItem={
                                                                removeEmailItem
                                                            }
                                                            setEmailValue={
                                                                setEmailValue
                                                            }
                                                        />
                                                    )
                                                )}
                                            </Form.Group>
                                            <div className="text-left">
                                                <Button
                                                    className="btn shadow-none"
                                                    size="sm"
                                                    name="addEmail"
                                                    variant="link"
                                                    disabled={isUpdating}
                                                    onClick={addEmailItem}
                                                >
                                                    + Add Email
                                                </Button>
                                            </div>
                                        </>
                                    )}
                                </>
                            ) : (
                                <>
                                    <div className="px-2 py-2 rounded view-details">
                                        <Badge
                                            className="px-3 py-2"
                                            variant={
                                                notificationStatus || "default"
                                            }
                                        >
                                            {notificationStatus
                                                ? toTitleCase(
                                                    notificationStatus
                                                )
                                                : "Notification status not found."}
                                        </Badge>
                                    </div>
                                    {notificationStatus ===
                                        NotificationsStatus.ENABLED && (
                                        <div className="my-3">
                                            {getDetailsAsLabelValueList(
                                                `Email ${
                                                    emailsForNotifications.length ===
                                                    1
                                                        ? "Address"
                                                        : "Addresses"
                                                }`,
                                                emailsForNotifications,
                                                "emails"
                                            )}
                                        </div>
                                    )}
                                </>
                            )}
                        </Form.Group>
                    ),
                    modalButton,
                };
            }
            default:
                return null;
        }
    }, [
        property,
        jobDetails?.status,
        jobDetails?.name,
        jobDetails?.description,
        jobDetails?.processType,
        jobDetails?.scheduleType,
        jobDetails?.scheduledDate,
        jobDetails?.recurringFrequency,
        jobDetails?.metadata,
        jobDetails?.notificationEmails,
        merchants,
        subTransactionTypes,
        jobName,
        jobDescription,
        notificationStatus,
        emailsForNotifications,
        currentNotificationStatus,
        isEditMode,
        isUpdating,
        onToggleEditMode,
        onChange,
        addEmailItem,
        removeEmailItem,
        setEmailValue,
        isAuthorizedForAction,
    ]);

    useEffect(() => {
        if (jobDetails) {
            if (property === "name") {
                setJobName(jobDetails?.name);

                return () => {
                    setJobName("");
                };
            }
            if (property === "description") {
                setJobDescription(jobDetails?.description);

                return () => {
                    setJobDescription("");
                };
            }
            if (property === "notifications") {
                setEmailsForNotifications(jobDetails?.notificationEmails || []);
                setNotificationStatus(currentNotificationStatus);

                return () => {
                    setEmailsForNotifications([]);
                    setNotificationStatus();
                };
            }
        }
    }, [property, jobDetails, currentNotificationStatus]);

    return (
        <Modal
            className="edit-view-details-modal"
            show={show}
            onHide={
                isUpdating
                    ? () => {
                        /* Placeholder for empty arrow function body. */
                    }
                    : onHide
            }
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!isUpdating}>
                <Modal.Title>{`${
                    isEditMode || property === "name" ? "Edit" : "View"
                } ${toTitleCaseFromCamelCase(property)}`}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form onSubmit={onUpdateJob} validated={validated} noValidate>
                    {editViewContent?.modalBody}
                    <div className="d-flex align-items-center justify-content-end">
                        <Button
                            className="mr-2"
                            size="sm"
                            variant="outline-primary"
                            disabled={isEditMode && isUpdating}
                            onClick={onHide}
                        >
                            Close
                        </Button>
                        {editViewContent?.modalButton}
                    </div>
                </Form>
            </Modal.Body>
        </Modal>
    );
};

export default EditViewProfileDetails;
