import React, { useContext } from "react";
import { toTitleCaseFromCamelCase } from "Utils";
import { AffinityGroupProfileContext } from "../context/AffinityGroupProfileContext";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";

const MemberDetails = () => {
    const { popUpState } = useContext(AffinityGroupProfileContext);
    return (
        <>
            {popUpState.hasOwnProperty("memberDetails") &&
                Object.keys(popUpState.memberDetails).map(
                    (key) =>
                        !~[
                            "nameToDisplay",
                            "emailToDisplay",
                            "loyaltyCardNumberToDisplay",
                            "removeMember",
                        ].indexOf(key) && (
                            <div key={key} className="mb-3">
                                <DetailsAsLabelValue
                                    label={toTitleCaseFromCamelCase(key)}
                                    value={
                                        popUpState.memberDetails[key] ||
                                        "~ unknown"
                                    }
                                />
                            </div>
                        )
                )}
        </>
    );
};

export default MemberDetails;
