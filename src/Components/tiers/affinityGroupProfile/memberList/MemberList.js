import React, {
    useContext,
    useState,
    useCallback,
    useEffect,
    useMemo,
} from "react";
import { toast } from "react-toastify";
import {
    Button,
    FormSearch,
    Badge,
    IcIcon,
    OverlayTrigger,
    Tooltip,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faCalendar,
    faCreditCard,
    faEnvelope,
    faFilter,
    faFilterSlash,
    faMedal,
    faObjectGroup,
    faUser,
} from "FaICIconMap";
import { DataContext, UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { useToggle } from "Hooks";
import { getMembers, getMembersCount } from "Services";
import {
    formatToCommonFormat,
    formatToCommonReadableFormat,
    toTitleCase,
    truncateLongString,
} from "Utils";
import BaseLayout from "Layout/BaseLayout";
import SharedTable from "Components/tiers/shared/sharedTableView/SharedTableView";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import { AffinityGroupProfileContext } from "../context/AffinityGroupProfileContext";
import {
    DropDownDateFilters,
    MemberAccountTypes,
    MemberListFilterTypes,
} from "./data";
import Filters from "./filters/Filters";

const maxCharLength = 14;

const nameIconTemplate = ({ name, icon }) => (
    <div className="d-flex">
        {icon && <IcIcon size="lg" icon={icon} className="mr-2 text-black" />}
        {toTitleCase(name)}
    </div>
);

const convertDateToReadable = (date) =>
    date ? formatToCommonReadableFormat(date) : null;

const truncateData = ({ key = "", value = "" }) =>
    value.length > maxCharLength + 1 ? (
        <OverlayTrigger
            placement="right"
            overlay={
                <Tooltip id={`tooltip-table-display-${key}`}>{value}</Tooltip>
            }
        >
            <span>{truncateLongString(value, maxCharLength)}</span>
        </OverlayTrigger>
    ) : (
        <span>{value || "~ unknown"}</span>
    );

const renderTableDisplayData = ({ value = null, notFoundText = "" }) =>
    value ||
    applyBadgeStyling({
        text: notFoundText || "Not Found",
        variant: "default",
    });

const columns = [
    {
        dataField: "nameToDisplay",
        text: nameIconTemplate({ name: "Name", icon: faUser }),
    },
    {
        dataField: "loyaltyCardNumberToDisplay",
        text: nameIconTemplate({
            name: "Loyalty Card No",
            icon: faCreditCard,
        }),
    },
    {
        dataField: "emailToDisplay",
        text: nameIconTemplate({ name: "Email ", icon: faEnvelope }),
    },
    {
        dataField: "tier",
        text: nameIconTemplate({ name: "Tier ", icon: faMedal }),
        headerStyle: { width: "10%" },
    },
    {
        dataField: "joinDate",
        text: nameIconTemplate({
            name: " Join Date",
            icon: faCalendar,
        }),
    },
    {
        dataField: "expirationDate",
        text: nameIconTemplate({
            name: "  Expiration Date",
            icon: faCalendar,
        }),
    },
    {
        dataField: "type",
        text: nameIconTemplate({
            name: "Type",
            icon: faObjectGroup,
        }),
        headerStyle: { width: "10%" },
    },
    {
        dataField: "removeMember",
        headerStyle: { width: "9%" },
    },
    {
        dataField: "name",
        hidden: true,
    },
    {
        dataField: "email",
        hidden: true,
    },
];

const accountBadgeVariant = (type) => {
    switch (type) {
        case "PRIMARY":
            return "primary";
        case "SECONDARY":
            return "secondary";
        default:
            return "default";
    }
};

const defaultLimit = 25,
    defaultSkip = 1;
let searchStateUpdateTimeout;

const MemberList = () => {
    const { selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const { isLoadingTiers, tiers, loadTiers } = useContext(DataContext);
    const {
        affinityGroupDetails,
        isLoading: isLoadingAffinityProifle,
        isLoadingMemberList: isLoading,
        setStates,
        reloadMemberList,
        setIsLoadingMemberList,
        setIsMembersAvailable,
    } = useContext(AffinityGroupProfileContext);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [searchText, setSearchText] = useState("");
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [getFromDate, setGetFromDate] = useState("");
    const [getToDate, setGetToDate] = useState("");
    const [selectFilter, setSelectFilter] = useState([]);
    const [selectedFilter, setSelectedFilter] = useState([]);
    const [filterValues, setFilterValues] = useState();
    const [isSet, setIsSet] = useState(false);
    const [appliedFilters, setAppliedFilters] = useState();
    const [isApplied, setIsApplied] = useState(false);

    const onClickRemoveMember = useCallback(
        (event) => {
            event.stopPropagation();
            setStates({
                popUpState: {
                    submitButtonName: "Remove",
                    stateChaningBtnName: "Removing...",
                    modalHeaderName: "Remove Affinity Group member",
                    renderingComponentName: "REMOVE_AFFINITY_GROUP_MEMBER",
                    modalFooterVisibility: true,
                    memberId: event.currentTarget.dataset.memberid || "-",
                    hasOtherAction: true,
                },
                show: true,
            });
        },
        [setStates]
    );

    const memberData = useMemo(() => {
        if (data.length === 0) return [];

        return data.map((item) => {
            const nameToShow =
                item?.firstName || item?.lastName
                    ? `${item?.firstName} ${item?.lastName}`
                    : "~ unknown";
            const loyaltyCardNumberToDisplay = item?.cardNumber
                ? truncateData({ key: "card", value: item.cardNumber })
                : null;
            let removeMemberBtn = {};

            if (
                selectedRegion?.memberConfiguration.defaultAffinityGroupId !==
                affinityGroupDetails._id
            ) {
                removeMemberBtn = {
                    removeMember: (
                        <Button
                            size="sm"
                            variant="outline-danger"
                            data-memberId={item?._id}
                            disabled={item?.type === "SECONDARY"}
                            onClick={onClickRemoveMember}
                        >
                            Remove
                        </Button>
                    ),
                };
            }

            return {
                name: nameToShow,
                nameToDisplay: truncateData({
                    key: "name",
                    value: nameToShow,
                }),
                loyaltyCardNumber: item?.cardNumber,
                loyaltyCardNumberToDisplay: renderTableDisplayData({
                    value: loyaltyCardNumberToDisplay,
                    notFoundText: "Card Not Found",
                }),
                email: item?.email,
                emailToDisplay: truncateData({
                    key: "email",
                    value: item?.email,
                }),
                mobileNumber: item?.mobileNumber || "~ unknown",
                tier: renderTableDisplayData({
                    value: item?.tier?.details?.name ? (
                        <Badge className="px-3 py-2" variant="warning">
                            {item.tier.details.name}
                        </Badge>
                    ) : null,
                    notFoundText: "Tier Not Found",
                }),
                joinDate: renderTableDisplayData({
                    value: convertDateToReadable(
                        item?.affinityGroup?.joinedDate
                    ),
                    notFoundText: "Joined Date Not Found",
                }),
                ...(item?.affinityGroup?.membershipId
                    ? { membershipId: item.affinityGroup.membershipId }
                    : {}),
                expirationDate: renderTableDisplayData({
                    value: convertDateToReadable(
                        item?.affinityGroup?.expiryDate
                    ),
                    notFoundText: "Expiry Date Not Found",
                }),
                type: renderTableDisplayData({
                    value: item?.type ? (
                        <Badge
                            className="px-3 py-2"
                            variant={accountBadgeVariant(item.type)}
                        >
                            {toTitleCase(item.type)}
                        </Badge>
                    ) : null,
                    notFoundText: "Type Not Found",
                }),
                ...removeMemberBtn,
            };
        });
    }, [
        selectedRegion?.memberConfiguration.defaultAffinityGroupId,
        affinityGroupDetails._id,
        data,
        onClickRemoveMember,
    ]);

    const tierFilters = useMemo(
        () => tiers.map((tier) => ({ value: tier?._id, name: tier?.name })),
        [tiers]
    );

    const loadMemberList = useCallback(
        async (
            { skip, limit, searchKey, countMembers = false },
            filters = null
        ) => {
            let queryObj = {
                limit,
                skip: (skip - 1) * limit,
                regionId: selectedRegion._id,
                affinityGroupId: affinityGroupDetails._id,
                searchKey,
            };

            try {
                setData([]);
                setIsLoadingMemberList(true);

                if (filters) {
                    queryObj = { ...queryObj, ...filters };
                }

                const promises = [
                    (async () => {
                        const memberListData = await getMembers(queryObj);
                        setData(memberListData.data.items);
                        setIsMembersAvailable(memberListData.data.total !== 0);
                    })(),
                ];

                if (countMembers) {
                    promises.push(
                        (async () => {
                            const countResponse = await getMembersCount({
                                regionId: selectedRegion._id,
                                searchKey: searchKey,
                                affinityGroupId: affinityGroupDetails._id,
                            });
                            setTotalCount(countResponse.count);
                        })()
                    );
                }

                await Promise.all(promises);
                setIsLoadingMemberList(false);
            } catch (e) {
                setIsLoadingMemberList(false);
                console.error(e);
                toast.error(
                    <div>
                        Failed to load members list!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            affinityGroupDetails,
            selectedRegion,
            setIsLoadingMemberList,
            setIsMembersAvailable,
            setTotalCount,
            setData,
        ]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadMemberList(
                { skip: newSkip, limit, searchKey: searchText },
                appliedFilters
            );
        },
        [limit, searchText, appliedFilters, loadMemberList, setSkip]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadMemberList(
                {
                    skip: defaultSkip,
                    limit: newLimit,
                    searchKey: searchText,
                },
                appliedFilters
            );
        },
        [searchText, appliedFilters, loadMemberList, setLimit]
    );

    const setSearch = useCallback(
        (text) => {
            if (searchStateUpdateTimeout) {
                clearTimeout(searchStateUpdateTimeout);
            }
            setSearchText(text);
            searchStateUpdateTimeout = setTimeout(() => {
                (async () => {
                    await loadMemberList(
                        {
                            skip: defaultSkip,
                            limit,
                            searchKey: text,
                            countMembers: true,
                        },
                        appliedFilters
                    );
                })();
            }, 2000);
        },
        [limit, appliedFilters, loadMemberList, setSearchText]
    );

    const onShowDetails = useCallback(
        (row) => {
            delete row["remove"];
            setStates({
                popUpState: {
                    modalHeaderName: "Member Details",
                    renderingComponentName: "SHOW_DETAILS",
                    modalFooterVisibility: true,
                    modalCloseBtnName: "Close",
                    hasOtherAction: false,
                    memberDetails: row,
                },
                show: true,
            });
        },
        [setStates]
    );

    const onFilterBy = useCallback(async () => {
        toggleShowFilters();
        if (
            (selectFilter.length !== 0 || selectedFilter.length !== 0) &&
            !isApplied &&
            !appliedFilters
        ) {
            setGetFromDate("");
            setGetToDate("");
            setSelectFilter([]);
            setSelectedFilter([]);
            setIsSet(false);
        }
    }, [
        toggleShowFilters,
        selectFilter,
        selectedFilter,
        isApplied,
        appliedFilters,
    ]);

    const onReloadTiers = useCallback(() => {
        loadTiers(selectedRegion._id);
    }, [selectedRegion._id, loadTiers]);

    const onSetFilterType = useCallback(
        (e) => {
            setSelectFilter(e);
            if (e[0].value === MemberListFilterTypes[0].value) {
                setSelectedFilter([]);
                setFilterValues(tierFilters);
            }
            if (e[0].value === MemberListFilterTypes[1].value) {
                setSelectedFilter([]);
                setFilterValues(MemberAccountTypes);
            }
            if (~DropDownDateFilters.indexOf(e[0]?.value)) {
                setSelectedFilter([]);
            }
            setIsApplied(false);
            setSelectedFilter([]);
            setIsSet(false);
        },
        [
            tierFilters,
            setSelectFilter,
            setSelectedFilter,
            setFilterValues,
            setIsApplied,
            setIsSet,
        ]
    );

    const setFromDateFiltersValues = useCallback(
        (selectedDate) => {
            if (selectFilter[0]?.value === MemberListFilterTypes[2].value) {
                setIsApplied(
                    formatToCommonFormat(selectedDate) ===
                        appliedFilters?.affinityGroupJoinedDateFrom
                );
            } else {
                setIsApplied(
                    formatToCommonFormat(selectedDate) ===
                        appliedFilters?.affinityGroupExpirationDateFrom
                );
            }
        },
        [
            appliedFilters?.affinityGroupExpirationDateFrom,
            appliedFilters?.affinityGroupJoinedDateFrom,
            selectFilter,
        ]
    );

    const setToDateFiltersValues = useCallback(
        (selectedDate) => {
            if (selectFilter[0].value === MemberListFilterTypes[2].value) {
                setIsApplied(
                    formatToCommonFormat(selectedDate) ===
                        appliedFilters?.affinityGroupJoinedDateTo
                );
            } else {
                setIsApplied(
                    formatToCommonFormat(selectedDate) ===
                        appliedFilters?.affinityGroupExpirationDateTo
                );
            }
        },
        [
            appliedFilters?.affinityGroupExpirationDateTo,
            appliedFilters?.affinityGroupJoinedDateTo,
            selectFilter,
        ]
    );

    const onSetFilter = useCallback(
        (e, dateType) => {
            setSelectedFilter(e);
            setIsSet(true);
            if (
                appliedFilters &&
                selectFilter[0].value === MemberListFilterTypes[0].value
            ) {
                switch (selectFilter[0].value) {
                    case MemberListFilterTypes[0].value:
                        setIsApplied(e[0].value === appliedFilters?.tierId);
                        break;
                    case MemberListFilterTypes[1].value:
                        setIsApplied(e[0].value === appliedFilters?.type);
                        break;
                    case MemberListFilterTypes[2].value:
                    case MemberListFilterTypes[3].value:
                        switch (dateType) {
                            case "FROM_DATE":
                                setFromDateFiltersValues(e);
                                break;
                            case "TO_DATE":
                                setToDateFiltersValues(e);
                                break;
                            default:
                                break;
                        }
                        break;
                    default:
                        break;
                }
            }
        },
        [
            selectFilter,
            appliedFilters,
            setSelectedFilter,
            setIsSet,
            setFromDateFiltersValues,
            setToDateFiltersValues,
        ]
    );

    const onChangeFromDate = useCallback(
        (date) => {
            setGetFromDate(date);
            onSetFilter(date, "FROM_DATE");
        },
        [onSetFilter, setGetFromDate]
    );

    const onChangeToDate = useCallback(
        (date) => {
            setGetToDate(date);
            onSetFilter(date, "TO_DATE");
        },
        [onSetFilter, setGetToDate]
    );

    const applyFilter = useCallback(async () => {
        if (selectFilter[0].value === MemberListFilterTypes[0].value) {
            setAppliedFilters({ tierId: selectedFilter[0]?.value });
        } else if (selectFilter[0].value === MemberListFilterTypes[1].value) {
            setAppliedFilters({ type: selectedFilter[0]?.value });
        } else if (selectFilter[0].value === MemberListFilterTypes[2].value) {
            if (getToDate === "") {
                setAppliedFilters({
                    affinityGroupJoinedDateFrom:
                        formatToCommonFormat(getFromDate),
                });
            } else if (getFromDate === "") {
                setAppliedFilters({
                    affinityGroupJoinedDateTo: formatToCommonFormat(getToDate),
                });
            } else {
                setAppliedFilters({
                    affinityGroupJoinedDateFrom:
                        formatToCommonFormat(getFromDate),
                    affinityGroupJoinedDateTo: formatToCommonFormat(getToDate),
                });
            }
        } else if (selectFilter[0].value === MemberListFilterTypes[3].value) {
            if (getToDate === "") {
                setAppliedFilters({
                    affinityGroupExpirationDateFrom:
                        formatToCommonFormat(getFromDate),
                });
            } else if (getFromDate === "") {
                setAppliedFilters({
                    affinityGroupExpirationDateTo:
                        formatToCommonFormat(getToDate),
                });
            } else {
                setAppliedFilters({
                    affinityGroupExpirationDateFrom:
                        formatToCommonFormat(getFromDate),
                    affinityGroupExpirationDateTo:
                        formatToCommonFormat(getToDate),
                });
            }
        }
        setIsApplied(true);
        setSkip(defaultSkip);
    }, [
        selectFilter,
        selectedFilter,
        getFromDate,
        getToDate,
        setIsApplied,
        setSkip,
    ]);

    const removeFromDate = useCallback(() => {
        setGetFromDate("");
        setAppliedFilters();
        if (selectFilter[0].value === MemberListFilterTypes[2].value) {
            if (appliedFilters?.affinityGroupJoinedDateTo) {
                setAppliedFilters({
                    affinityGroupJoinedDateTo: formatToCommonFormat(getToDate),
                });
            } else {
                setIsSet(false);
                setIsApplied(false);
                setSelectedFilter([]);
                setSelectFilter([]);
            }
        } else if (appliedFilters?.affinityGroupExpirationDateTo) {
            setAppliedFilters({
                affinityGroupExpirationDateTo: formatToCommonFormat(getToDate),
            });
        } else {
            setIsSet(false);
            setIsApplied(false);
            setSelectedFilter([]);
            setSelectFilter([]);
        }
        setSkip(defaultSkip);
    }, [
        selectFilter,
        appliedFilters?.affinityGroupJoinedDateTo,
        appliedFilters?.affinityGroupExpirationDateTo,
        getToDate,
        setGetFromDate,
        setAppliedFilters,
        setIsSet,
        setIsApplied,
        setSelectedFilter,
        setSelectFilter,
        setSkip,
    ]);

    const removeToDate = useCallback(() => {
        setGetToDate("");
        setAppliedFilters();
        if (selectFilter[0].value === MemberListFilterTypes[2].value) {
            if (appliedFilters?.affinityGroupJoinedDateFrom) {
                setAppliedFilters({
                    affinityGroupJoinedDateFrom:
                        formatToCommonFormat(getFromDate),
                });
            } else {
                setIsSet(false);
                setIsApplied(false);
                setSelectedFilter([]);
                setSelectFilter([]);
            }
        } else if (appliedFilters?.affinityGroupExpirationDateFrom) {
            setAppliedFilters({
                affinityGroupExpirationDateFrom:
                    formatToCommonFormat(getFromDate),
            });
        } else {
            setIsSet(false);
            setIsApplied(false);
            setSelectedFilter([]);
            setSelectFilter([]);
        }
        setSkip(defaultSkip);
    }, [
        selectFilter,
        appliedFilters?.affinityGroupJoinedDateFrom,
        appliedFilters?.affinityGroupExpirationDateFrom,
        getFromDate,
        setGetToDate,
        setAppliedFilters,
        setIsSet,
        setIsApplied,
        setSelectedFilter,
        setSelectFilter,
        setSkip,
    ]);

    const resetFilter = useCallback(() => {
        setSelectFilter([]);
        setSelectedFilter([]);
        setGetFromDate("");
        setGetToDate("");
        setAppliedFilters();
        setIsSet(false);
        setIsApplied(false);
        setSkip(defaultSkip);
    }, [
        setSelectFilter,
        setSelectedFilter,
        setGetFromDate,
        setGetToDate,
        setAppliedFilters,
        setIsSet,
        setIsApplied,
        setSkip,
    ]);

    useEffect(() => {
        if (
            affinityGroupDetails.hasOwnProperty("_id") &&
            affinityGroupDetails._id &&
            isAuthorizedForAction(
                AccessPermissionModuleNames.MEMBER,
                AccessPermissionModules[AccessPermissionModuleNames.MEMBER]
                    .actions.ListMembers
            )
        ) {
            loadMemberList(
                { skip: defaultSkip, limit, countMembers: true },
                appliedFilters
            );
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [affinityGroupDetails, appliedFilters]);

    useEffect(() => {
        if (
            reloadMemberList &&
            isAuthorizedForAction(
                AccessPermissionModuleNames.MEMBER,
                AccessPermissionModules[AccessPermissionModuleNames.MEMBER]
                    .actions.ListMembers
            )
        ) {
            loadMemberList(
                { skip: defaultSkip, limit, countMembers: true },
                appliedFilters
            );
            setStates({ reloadMemberList: false });
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [reloadMemberList, appliedFilters]);

    return (
        <BaseLayout
            containerClassName="affinity-group-profile p-3"
            bottom={
                <>
                    {isAuthorizedForAction(
                        AccessPermissionModuleNames.MEMBER,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.MEMBER
                        ].actions.ListMembers
                    ) ? (
                        <>
                            <div className="mt-3 d-flex justify-content-between">
                                <div className="w-50">
                                    <FormSearch
                                        placeholder="Search by name, loyalty card, email..."
                                        selected={searchText}
                                        disabled={
                                            isLoading ||
                                            isLoadingAffinityProifle
                                        }
                                        onChange={setSearch}
                                        id="search-cards"
                                    />
                                </div>
                                <div>
                                    <Button
                                        variant={`${
                                            !showFilters ? "outline-" : ""
                                        }primary`}
                                        size="sm"
                                        disabled={
                                            isLoading ||
                                            isLoadingAffinityProifle
                                        }
                                        onClick={onFilterBy}
                                    >
                                        <IcIcon
                                            className="mr-2"
                                            size="lg"
                                            icon={
                                                showFilters
                                                    ? faFilterSlash
                                                    : faFilter
                                            }
                                        />
                                        {showFilters
                                            ? "Hide Filters"
                                            : "Filter By"}
                                    </Button>
                                </div>
                            </div>
                            {showFilters && (
                                <Filters
                                    appliedFilters={appliedFilters}
                                    isLoading={
                                        isLoading || isLoadingAffinityProifle
                                    }
                                    isLoadingTiers={isLoadingTiers}
                                    isSet={isSet}
                                    isApplied={isApplied}
                                    selectFilter={selectFilter}
                                    selectedFilter={selectedFilter}
                                    filterValues={filterValues}
                                    getFromDate={getFromDate}
                                    getToDate={getToDate}
                                    onSetFilterType={onSetFilterType}
                                    onSetFilter={onSetFilter}
                                    onChangeFromDate={onChangeFromDate}
                                    onChangeToDate={onChangeToDate}
                                    applyFilter={applyFilter}
                                    reloadTiers={onReloadTiers}
                                    removeFromDate={removeFromDate}
                                    removeToDate={removeToDate}
                                    resetFilter={resetFilter}
                                />
                            )}
                            <SharedTable
                                columns={columns}
                                data={memberData}
                                sizePerPage={limit}
                                page={skip}
                                onChangePagination={onChangePagination}
                                onChangePageSize={onChangePageSize}
                                isLoading={
                                    isLoading || isLoadingAffinityProifle
                                }
                                onShowDetails={onShowDetails}
                                totalCount={totalCount}
                            />
                        </>
                    ) : (
                        <h4 className="text-danger text-center">
                            You are not authorized to view affinity group's
                            members list!
                        </h4>
                    )}
                </>
            }
        />
    );
};

export default MemberList;
