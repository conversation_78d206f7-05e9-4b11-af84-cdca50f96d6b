import React ,{ use<PERSON><PERSON>back, useContext, useEffect, useMemo, useState }from "react";
import { Accordion } from "react-bootstrap";
import { toast } from "react-toastify";
import {
    Col,
    FormSearch,
    Row,
    Card,
    Tabs,
    Tab,
    Button,
    FormDate,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { useToggle } from "Hooks";
import { getImportedJobLis } from "Services";
import BaseLayout from "Layout/BaseLayout";
import FilterDropdown from "Components/cards/cardGenerate/shared/topPanel/FilterDropdown";
import { LoadingComponent } from "Components/utils";
import { AffinityGroupProfileContext } from "../context/AffinityGroupProfileContext";
import ContextAwareToggle from "./ContextAwareToggle";
import ImportJobLogs from "./ImportJobLogs";
import { faFilter, faFilterSlash } from "FaICIconMap";

const defaultSkip = 1, defaultLimit = 1000;
let searchStateUpdateTimeout;

const selectOptions = [ { label: "Created Date", value: "CREATED_DATE" },
];

//TODO: Future pagination supported implementation.
// const NoData = ({ loading }) => {
//     if (loading) return null;
//     return <div>No import jobs found.</div>;
// };

const ImportedJobList = () => {
    const {selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const {
        affinityGroupDetails,
        currentEventKey,
        reloadImportedJobList,
        setStates,
        isLoadingImportJobs: isLoading,
        isLoadingImportJobLogs,
        setIsLoadingImportJobs
    } = useContext(AffinityGroupProfileContext);
    // eslint-disable-next-line no-unused-vars
    const [limit, setLimit] = useState(defaultLimit);
    //TODO: Future pagination supported implementation.
    // const [skip, setSkip] = useState(defaultSkip); 
    //TODO: Future pagination supported implementation.
    // const [totalCount, setTotalCount] = useState(0); 
    const [searchText, setSearchText] = useState("");
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [data, setData] = useState([]);
    const [activeTab, setActiveTab] = useState("ALL");
    const [selectedOption, setSelectedOption] = useState( [{ label: " ", value: "" }]);
    const [fromDate, setFromDate] = useState("");
    const [toDate, setToDate] = useState("");
    const [clearFilter, setClearFilter] = useState(false);

    const importJobList = useMemo(() => {
        let importList;

        if (data.length > 0) {
            importList = (
                <Accordion>
                    {data.length !== 0 && data.map(item => (
                        <Card className="mt-3" key={item._id}>
                            <Card.Header>
                                <ContextAwareToggle
                                    eventKey={item._id}
                                    item={item}
                                />
                            </Card.Header>
                            <Accordion.Collapse eventKey={item._id}>
                                <Card.Body>
                                    <Tabs
                                        defaultActiveKey={activeTab}
                                        transition={false}
                                        id="noanim-tab-example"
                                        activeKey={activeTab}
                                        onSelect={setActiveTab}
                                        className="mt-3"
                                    >
                                        <Tab
                                            eventKey={"ALL"}
                                            title={
                                                <div className="d-flex flex-row align-items-center">
                                                    <span className="mr-2">All</span>
                                                </div>
                                            }
                                            disabled={isLoading || isLoadingImportJobLogs}
                                        />
                                        <Tab
                                            eventKey={"COMPLETED"}
                                            title={
                                                <div className="d-flex flex-row align-items-center">
                                                    <span className="mr-2">Completed</span>
                                                </div>
                                            }
                                            disabled={isLoading || isLoadingImportJobLogs}
                                        />
                                        <Tab
                                            eventKey={"FAILED"}
                                            title={
                                                <div className="d-flex flex-row align-items-center">
                                                    <span className="mr-2">Failed</span>
                                                </div>
                                            }
                                            disabled={isLoading || isLoadingImportJobLogs}
                                        />
                                    </Tabs>
                                    {currentEventKey === item._id &&
                                        <ImportJobLogs
                                            activeTab={activeTab}
                                            affinityGroupId={affinityGroupDetails._id}
                                            importJobId={item._id}
                                            currentEventKey={currentEventKey}
                                        />
                                    }
                                </Card.Body>
                            </Accordion.Collapse>
                        </Card>
                    ))}
                </Accordion>
            );
        }
        else {
            importList = <div className="text-center">No import data found.</div>
        }
        return (
            <>
                {isLoading ?
                    <LoadingComponent />
                    :
                    <div className="mt-5">
                        {importList}
                    </div>
                }
            </>
        );
    }, [
        activeTab,
        affinityGroupDetails._id,
        currentEventKey,
        data,
        isLoading,
        isLoadingImportJobLogs
    ]);

    //TODO: Future pagination supported implementation.
    // const columns = [{ dataField: "importedJobs" }, { dataField: "_id", hidden: true }];
    // const onRowClick = useCallback(rowData => {
    // }, []);
    // const tableData = useMemo(() =>
    //     data.map((item, index) => ({
    //         importedJobs: (
    //             <Accordion key={item._id}>
    //                 <Card className="mt-3" key={index}>
    //                     <Card.Header>
    //                         <ContextAwareToggle
    //                             eventKey={item._id}
    //                             item={item}
    //                         />
    //                     </Card.Header>
    //                     <Accordion.Collapse eventKey={item._id}>
    //                         <Card.Body>
    //                             <Tabs
    //                                 defaultActiveKey={activeTab}
    //                                 transition={false}
    //                                 id="noanim-tab-example"
    //                                 activeKey={activeTab}
    //                                 onSelect={setActiveTab}
    //                                 className="mt-3"
    //                             >
    //                                 <Tab
    //                                     eventKey={"ALL"}
    //                                     title={
    //                                         <div className="d-flex flex-row align-items-center">
    //                                             <span className="mr-2">All</span>
    //                                         </div>
    //                                     }
    //                                     disabled={isLoading}
    //                                 />
    //                                 <Tab
    //                                     eventKey={"COMPLETED"}
    //                                     title={
    //                                         <div className="d-flex flex-row align-items-center">
    //                                             <span className="mr-2">Completed</span>
    //                                         </div>
    //                                     }
    //                                     disabled={isLoading}
    //                                 />
    //                                 <Tab
    //                                     eventKey={"FAILED"}
    //                                     title={
    //                                         <div className="d-flex flex-row align-items-center">
    //                                             <span className="mr-2">Failed</span>
    //                                         </div>
    //                                     }
    //                                     disabled={isLoading}
    //                                 />
    //                             </Tabs>
    //                             {currentEventKey===item._id&&
    //                                 <ImportJobLogs
    //                                     activeTab={activeTab}
    //                                     affinityGroupId={affinityGroupDetails._id}
    //                                     importJobId={item._id}
    //                                     currentEventKey={currentEventKey}
    //                                 />
    //                             }
    //                         </Card.Body>
    //                     </Accordion.Collapse>
    //                 </Card>
    //             </Accordion>
    //         ),
    //         _id: item._id,
    //     })),
    // [activeTab, affinityGroupDetails._id, currentEventKey, data, isLoading]);

    const loadImportedJobLis = useCallback(async ({ skip, limit, searchKey }) => {
            try {
                setData([]);
                setIsLoadingImportJobs(true)
                const importedJobLisData=await getImportedJobLis({
                    limit,
                    skip: (skip - 1) * limit,
                    regionId:selectedRegion._id,
                    searchKey:searchKey,
                    affinityGroupId:affinityGroupDetails._id,
                    ...(fromDate?{createdDateFrom:fromDate}:{}),
                    ...(toDate?{createdDateTo:toDate}:{}),
                })
                setData(importedJobLisData.items);
                setIsLoadingImportJobs(false);
            } catch (e) {
                setIsLoadingImportJobs(false);
                console.error(e)
                toast.error(
                    <div>
                        Failed to load affinity group's import jobs!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            selectedRegion,
            affinityGroupDetails,
            fromDate,
            toDate,
            setIsLoadingImportJobs,
            setData,
        ]);

    const setSearch = useCallback(text => {
            if (searchStateUpdateTimeout) {
                clearTimeout(searchStateUpdateTimeout);
            }
            setSearchText(text);
            searchStateUpdateTimeout = setTimeout(async () => {
                await loadImportedJobLis({
                    skip: defaultSkip,
                    limit,
                    searchKey: text,
                });
            }, 2000);
        },
        [limit, loadImportedJobLis,setSearchText]
    );

    const onClickApplyFilter =useCallback((e) => {
            e.stopPropagation()
            loadImportedJobLis({
                skip: defaultSkip,
                limit,
                searchKey: searchText,
            });
        },[limit, searchText, loadImportedJobLis]
    );

    //TODO: Future pagination supported implementation.
    // const onChangePagination = useCallback(
    //     newSkip => {
    //         setSkip(newSkip);
    //         loadImportedJobLis({
    //             limit,
    //             skip: newSkip,
    //             searchKey: searchText,
    //         });
    //     },
    //     [limit, searchText, setSkip, loadImportedJobLis]
    // );
    // const onChangePageSize = useCallback(
    //     newLimit => {
    //         setLimit(newLimit);
    //         setSkip(defaultSkip);
    //         loadImportedJobLis({
    //             limit: newLimit,
    //             skip: defaultSkip,
    //             searchKey: searchText,
    //         });
    //     },
    //     [searchText, setLimit, setSkip, loadImportedJobLis]
    // );
    // const rowClick = useCallback((e, row) => onRowClick(row), [onRowClick]);
    // const tableRowEvents = { onClick: rowClick };
    // const options = {
    //     page: skip,
    //     sizePerPage: limit,
    //     totalSize: totalCount,
    //     pageStartIndex: 1,
    //     showTotal: true,
    //     withFirstAndLast: true,
    //     sizePerPageList: [
    //         { text: "5", value: 5 },
    //         { text: "25", value: 25 },
    //         { text: "50", value: 50 },
    //         { text: "100", value: 100 },
    //     ],
    //     onPageChange: onChangePagination,
    //     onSizePerPageChange: onChangePageSize,
    // };
    // const onTableChange = (_type, _newState) => {
    //     // * When "remote" is enabled, the "onTableChange" prop is activated automatically, so if we don't provide this method react-bootstrap will throw an error.
    // }

    useEffect(() => {
        if(affinityGroupDetails.hasOwnProperty("_id")&&
            affinityGroupDetails._id
            &&isAuthorizedForAction(
            AccessPermissionModuleNames.AFFINITY_GROUPS,
            AccessPermissionModules[AccessPermissionModuleNames.AFFINITY_GROUPS]
                .actions.ListAffinityGroupMemberImportJobs
        ))
            loadImportedJobLis({ skip: defaultSkip, limit });
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if(reloadImportedJobList && isAuthorizedForAction(
            AccessPermissionModuleNames.AFFINITY_GROUPS,
            AccessPermissionModules[AccessPermissionModuleNames.AFFINITY_GROUPS]
                .actions.ListAffinityGroupMemberImportJobs
        )){
            loadImportedJobLis({ skip: defaultSkip, limit });
            setStates({reloadImportedJobList:false})
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [reloadImportedJobList]);

    useEffect(() => {
        setToDate("")
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fromDate]);

    useEffect(() => {
        if(clearFilter){
            setToDate("")
            setFromDate("")
            setSelectedOption( [{ label: " ", value: "" }])
            setClearFilter(false)
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [clearFilter]);

    useEffect(() => {
        if(clearFilter){
            setToDate("")
            setFromDate("")
            setSelectedOption( [{ label: " ", value: "" }])
            setClearFilter(false)
            setStates({reloadImportedJobList:true})
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [clearFilter]);

    return(
        <BaseLayout
            containerClassName="affinity-group-profile p-3"
            bottom={
                <>
                    {isAuthorizedForAction(
                        AccessPermissionModuleNames.AFFINITY_GROUPS,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.AFFINITY_GROUPS
                            ].actions.ListAffinityGroupMemberImportJobs
                    ) ? (
                        <>
                            <Row className="mt-3">
                                <Col sm={5}>
                                    <FormSearch
                                        placeholder="Search by file name"
                                        selected={searchText}
                                        disabled={isLoading}
                                        onChange={setSearch}
                                        id="search-cards"
                                    />
                                </Col>
                                <Col>
                                    <div className="float-right">
                                        <Button
                                            variant={`${
                                                !showFilters ? "outline-" : ""
                                            }primary`}
                                            size="sm"
                                            disabled={isLoading}
                                            onClick={toggleShowFilters}
                                        >
                                            <IcIcon
                                                className="mr-2"
                                                size="lg"
                                                icon={
                                                    showFilters
                                                        ? faFilterSlash
                                                        : faFilter
                                                }
                                            />
                                            {showFilters
                                                ? "Hide Filters"
                                                : "Filter By"}
                                        </Button>
                                    </div>
                                </Col>
                                {showFilters&&
                                    <Col sm={12}>
                                        <div className="mt-3">
                                            <Card>
                                                <Card.Body>
                                                    <div className="d-flex flex-row text-center align-items-center">
                                                        <FilterDropdown
                                                            onChangeSelect={setSelectedOption}
                                                            selectOptions={selectOptions}
                                                            placeHolder={"Filter By"}
                                                            selectedValue={selectedOption}
                                                            size="sm"
                                                        />
                                                        {(selectedOption.length!==0&&selectedOption[0].value==="CREATED_DATE")&&  <>
                                                            <div className="mx-2 ">between</div>
                                                            <FormDate
                                                                onChange={setFromDate}
                                                                date={fromDate}
                                                                size="sm"
                                                            />
                                                            <div className="mx-2 ">and</div>
                                                            <FormDate
                                                                onChange={setToDate}
                                                                date={toDate}
                                                                disabled={fromDate===""}
                                                                size="sm"
                                                                minDate={new Date(fromDate)}
                                                            />
                                                        </>}
                                                        <div className="text-center mx-2">
                                                            <Button
                                                                variant='outline-primary '
                                                                size='sm'
                                                                className='mx-2'
                                                                onClick={onClickApplyFilter}
                                                                disabled={fromDate===""||toDate===""}
                                                            >
                                                                Apply Filter
                                                            </Button>
                                                        </div>
                                                        <div className="text-center mx-2">
                                                            <Button
                                                                variant='outline-danger '
                                                                size='sm'
                                                                className='mx-2'
                                                                onClick={setClearFilter}
                                                            >
                                                                Clear Filter
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </Card.Body>
                                            </Card>
                                        </div>
                                    </Col>
                                }
                            </Row>
                            {importJobList}
                            {/* //TODO: Future pagination supported implementation.
                                <div>
                                    <PaginationProvider
                                        pagination={paginationFactory(options)}
                                        keyField="_id"
                                        columns={columns}
                                        data={tableData}
                                    >
                                        {({ paginationTableProps }) => (
                                            <ToolkitProvider
                                                keyField="_id"
                                                data={tableData}
                                                columns={columns}
                                                columnToggle
                                            >
                                                {(props) => (
                                                    <div>
                                                        <BootstrapTable
                                                            {...paginationTableProps}
                                                            remote={{ pagination: true }}
                                                            rowEvents={tableRowEvents}
                                                            loading={isLoading}
                                                            keyField="_id"
                                                            onTableChange={onTableChange}
                                                            noDataIndication={() => (
                                                                <NoData loading={isLoading} />
                                                            )}
                                                            overlay={BootstrapTableOverlay}
                                                            {...props.baseProps}
                                                        />
                                                    </div>
                                                )}
                                            </ToolkitProvider>
                                        )}
                                    </PaginationProvider>
                                </div>
                            */}
                        </>
                    ) : (
                        <h4 className="text-danger text-center">
                            You are not authorized to view imported job list
                        </h4>
                    )}
                </>
            }
        />
    )
}

export default ImportedJobList
