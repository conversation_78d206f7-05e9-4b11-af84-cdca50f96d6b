import React, { useCallback, useContext, useState } from "react";
import { toast } from "react-toastify";
import {
    Button,
    Col,
    Form,
    Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { CardStatus, MemberTypes } from "Data";
import { getCardByCardNumber, getMemberById } from "Services";
import { AffinityGroupProfileContext } from "../context/AffinityGroupProfileContext";

const SearchLoyaltyMember = () => {
    const { selectedRegion, organization } = useContext(UserContext);
    const {
        isValidatingCard,
        isSearchingMember,
        setIsValidatingCard,
        setIsSearchingMember,
        setStates,
    } = useContext(AffinityGroupProfileContext);
    const [validated, setValidated] = useState(false);
    const [loyaltyCard, setLoyaltyCard] = useState("");

    const onCloseShowCard = useCallback(() => {
        setLoyaltyCard("");
        setStates({ show: false });
    }, [setStates]);

    const onChangeLoyaltyCard = useCallback(
        (e) =>
            setLoyaltyCard(
                e.target.value
                    .replace(/[^\dA-Z]/g, "")
                    .replace(/(.{4})/g, "$1 ")
                    .trim()
            ),
        []
    );

    const searchCard = useCallback(
        async (e) => {
            e.preventDefault();
            if (e.target.checkValidity()) {
                try {
                    setIsValidatingCard(true);
                    const card = await getCardByCardNumber(
                        loyaltyCard.replace(/\s/g, ""),
                        selectedRegion._id
                    );
                    if (card.items.length > 0) {
                        if (card.items[0].status === CardStatus.ASSIGNED) {
                            setIsValidatingCard(false);
                            setIsSearchingMember(true);
                            const memberDetails = await getMemberById(
                                card.items[0].member._id
                            );
                            if (memberDetails.type === MemberTypes.PRIMARY) {
                                setStates({
                                    memberDetails: {
                                        _id: memberDetails._id || "-",
                                        loyaltyCardNumber: loyaltyCard.replace(
                                            /\s/g,
                                            ""
                                        ),
                                        name:
                                            memberDetails.firstName +
                                                " " +
                                                memberDetails.lastName || "-",
                                        email: memberDetails.email || "-",
                                        mobileNumber:
                                            memberDetails.mobileNumber || "-",
                                        isoCode:
                                            memberDetails.countryCode ||
                                            selectedRegion.defaultCountryISO2Code,
                                        expiryDate: "",
                                        membershipId: "",
                                    },
                                    popUpState: {
                                        submitButtonName: "",
                                        modalHeaderName:
                                            "Loyalty Account Details",
                                        renderingComponentName:
                                            "ACCOUNT_DETAILS",
                                        modalFooterVisibility: false,
                                    },
                                });
                                setIsSearchingMember(false);
                            } else {
                                throw new Error(
                                    "Member should be a primary member"
                                );
                            }
                        } else {
                            throw new Error("Card should be an assigned card");
                        }
                    } else {
                        throw new Error(
                            "Card not found! Please try again with a different assigned card"
                        );
                    }
                } catch (e) {
                    setIsValidatingCard(false);
                    setIsSearchingMember(false);
                    console.error(e);
                    toast.error(
                        <div>
                            Failed to search for a loyalty member!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setValidated(true);
            }
        },
        [
            loyaltyCard,
            selectedRegion,
            setStates,
            setIsValidatingCard,
            setIsSearchingMember,
        ]
    );
    return (
        <Form onSubmit={searchCard} validated={validated} noValidate>
            <Form.Group>
                <Form.Label className="d-flex align-items-center">
                    Enter Loyalty Card Number
                    <div className="ml-1 text-danger">*</div>
                </Form.Label>
                <Form.Control
                    type="text"
                    required
                    minLength={
                        organization.configuration
                            ? organization.configuration.cardConfiguration
                                .loyaltyCardNumberLength +
                            (Math.round(
                                organization.configuration.cardConfiguration
                                    .loyaltyCardNumberLength / 4
                            ) -
                                1)
                            : 14
                    }
                    maxLength={
                        organization.configuration
                            ? organization.configuration.cardConfiguration
                                .loyaltyCardNumberLength +
                            (Math.round(
                                organization.configuration.cardConfiguration
                                    .loyaltyCardNumberLength / 4
                            ) -
                                1)
                            : 14
                    }
                    value={loyaltyCard}
                    disabled={isValidatingCard || isSearchingMember}
                    onChange={onChangeLoyaltyCard}
                    placeholder="Enter Loyalty Card Number"
                />
            </Form.Group>
            <Row>
                <Col className="d-flex justify-content-end">
                    <Button
                        size="sm"
                        variant="outline-primary"
                        disabled={isValidatingCard || isSearchingMember}
                        className="m-1"
                        onClick={onCloseShowCard}
                    >
                        Cancel
                    </Button>
                    <Button
                        size="sm"
                        variant="primary"
                        className="m-1"
                        disabled={
                            isValidatingCard ||
                            isSearchingMember ||
                            loyaltyCard === ""
                        }
                        type="submit"
                    >
                        {isValidatingCard && "Validating card number..."}
                        {isSearchingMember && "Searching for member..."}
                        {!isValidatingCard && !isSearchingMember && "Search"}
                    </Button>
                </Col>
            </Row>
        </Form>
    );
};

export default SearchLoyaltyMember;
