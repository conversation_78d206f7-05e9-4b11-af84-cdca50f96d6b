import React, { useContext, useState, useCallback } from "react";
import { toast } from "react-toastify";
import {
    Button,
    Col,
    Form,
    FormDate,
    Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {  updateMemberAffinityGroup } from "Services";
import { AffinityGroupProfileContext } from "../context/AffinityGroupProfileContext";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";

const AddMembers = () => {
    const {
        setStates,
        memberDetails,
        handleClose,
        affinityGroupDetails,
        isAddingMember,
        setIsAddingMember,
    } = useContext(AffinityGroupProfileContext);
    const [validated, setValidated] = useState(false);

    const onSubmit = useCallback(
        async (e) => {
            e.preventDefault();
            if (e.target.checkValidity() && memberDetails.expiryDate) {
                try {
                    setIsAddingMember(true);
                    await updateMemberAffinityGroup(memberDetails._id,  {
                        affinityGroupId: affinityGroupDetails._id || "-",
                        expiryDate: memberDetails.expiryDate || "-",
                        ...(memberDetails.membershipId?{membershipId: memberDetails.membershipId}:{})
                    });
                    setIsAddingMember(false);
                    setStates({ show: false, reloadMemberList: true });
                    toast.success(
                        "Successfully added member and it's secondary accounts (if any), to affinity group."
                    );
                } catch (e) {
                    setIsAddingMember(false);
                    console.error(e);
                    toast.error(
                        <div>
                            Failed to add member to affinity group!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setValidated(true);
            }
        },
        [setStates, affinityGroupDetails, memberDetails, setIsAddingMember]
    );

    const onChangeExpiryDate = useCallback(
        (date) =>
            setStates({
                memberDetails: { ...memberDetails, expiryDate: date },
            }),
        [memberDetails, setStates]
    );

    const onChangeMemberShipId = useCallback(
        (event) =>{
            event.stopPropagation();
            setStates({
                memberDetails: { ...memberDetails, membershipId: event.target.value},
            })
        },
        [memberDetails, setStates]
    );

    return (
        <>
            <div>
                <DetailsAsLabelValue
                    label="Loyalty Card Number"
                    value={memberDetails?.loyaltyCardNumber || "~ unknown"}
                />
            </div>
            <div className="mt-3">
                <DetailsAsLabelValue
                    label="Name"
                    value={memberDetails?.name || "~ unknown"}
                />
            </div>
            <div className="mt-3">
                <DetailsAsLabelValue
                    label="Email"
                    value={memberDetails?.email || "~ unknown"}
                />
            </div>
            <div className="my-3">
                <DetailsAsLabelValue
                    label="Mobile Number"
                    value={memberDetails?.mobileNumber || "~ unknown"}
                />
            </div>
            <Form onSubmit={onSubmit} validated={validated} noValidate>
                <Form.Group className="w-100 d-flex flex-column">
                    <Form.Label className="d-flex align-items-center">
                        Expiry date
                        <div className="ml-1 text-danger">*</div>
                    </Form.Label>
                    <div
                        className={`${
                            validated &&
                            !memberDetails.expiryDate &&
                            "border border-danger rounded"
                        }`}
                    >
                        <FormDate
                            selectText="Select an expiry date"
                            disabled={isAddingMember}
                            onChange={onChangeExpiryDate}
                            date={memberDetails.expiryDate}
                            minDate={new Date()}
                        />
                    </div>
                    {validated && (
                        <>
                            {!memberDetails.expiryDate && (
                                <small className="text-danger">
                                    * Expiry date cannot be empty.
                                </small>
                            )}
                        </>
                    )}
                </Form.Group>
                <Form.Group>
                    <Form.Label> Member ID</Form.Label>
                    <Form.Control
                        type="text"
                        name="membershipId"
                        placeholder="Enter member ID"
                        value={memberDetails.membershipId}
                        onChange={onChangeMemberShipId}
                        disabled={isAddingMember}
                    />
                </Form.Group>
                <Row>
                    <Col className="d-flex  mt-3 justify-content-end">
                        <Button
                            size="sm"
                            variant="outline-primary"
                            disabled={isAddingMember}
                            className="m-1"
                            onClick={handleClose}
                        >
                            Cancel
                        </Button>
                        <Button
                            size="sm"
                            variant="primary"
                            className="m-1"
                            disabled={isAddingMember}
                            type="submit"
                        >
                            {isAddingMember ? "Adding Member..." : "Add Member"}
                        </Button>
                    </Col>
                </Row>
            </Form>
        </>
    );
};

export default AddMembers;
