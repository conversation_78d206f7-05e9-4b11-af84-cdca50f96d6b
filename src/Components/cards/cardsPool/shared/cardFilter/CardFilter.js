import React, { useCallback, useMemo } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    FormDate,
} from "@shoutout-labs/shoutout-themes-enterprise";
import PropTypes from "prop-types";
import { CardStatus, CardGenerateJobStatus, CardTypes } from "Data";
import FilterDropdown from "../../../cardGenerate/shared/topPanel/FilterDropdown";
import CardStatsView from "../cardStats/CardStatsViewContainer";

const selectOptions = [
    { label: "Created Date", value: "Created_Date" },
    { label: "Assigned Date", value: "Assigned_Date" },
    { label: "Status", value: "Status" },
];
const selectCardState = [
    { label: "Suspended", value: CardStatus.SUSPENDED },
    { label: "Deactivated", value: CardStatus.DEACTIVATED },
    { label: "Assigned", value: CardStatus.ASSIGNED },
    { label: "Active", value: CardStatus.ACTIVE },
];

const selectProcessingState = [
    { label: "Pending", value: CardGenerateJobStatus.PENDING },
    { label: "Printing", value: CardGenerateJobStatus.PRINTING },
    { label: "Printed", value: CardGenerateJobStatus.PRINTED },
    { label: "Dispatched", value: CardGenerateJobStatus.DISPATCHED },
    { label: "Completed", value: CardGenerateJobStatus.COMPLETED },
];
const CardFilter = ({
    showFilters,
    isLoading,
    applyFilter,
    onSelectOption,
    onSelectState,
    selectedOption,
    cardState,
    clearFilter,
    setFromDate,
    setToDate,
    fromDate,
    toDate,
    onSelectProcessingState,
    processingState,
    cardType,
}) => {
    const filterOptions = useMemo(() => {
        let options = [...selectOptions];

        if (
            cardType === CardTypes.INSTANT_CARD ||
            cardType === CardTypes.EMBOSSED_CARD ||
            cardType === "all"
        ) {
            options = [
                ...selectOptions,
                { label: "Processing Status", value: "Processing_Status" },
            ];
        }
        return options;
    }, [cardType]);

    const processingStatusOptions = useMemo(() => {
        let processingOptions = [...selectProcessingState];
        switch (cardType) {
            case CardTypes.EMBOSSED_CARD:
                processingOptions = [
                    {
                        label: "New Request",
                        value: CardGenerateJobStatus.NEW_REQUEST,
                    },
                    ...selectProcessingState,
                ];
                break;
            case "all":
                processingOptions = [
                    {
                        label: "New Request",
                        value: CardGenerateJobStatus.NEW_REQUEST,
                    },
                    ...selectProcessingState,
                ];
                break;
            default:
                break;
        }
        return processingOptions;
    }, [cardType]);

    const fromDateChange = useCallback(
        (date) => {
            setFromDate(date);
            setToDate(null);
        },
        [setFromDate, setToDate]
    );

    const onChangeSelectOption = useCallback(
        (event) => {
            onSelectOption(event);
            setFromDate(null);
            setToDate(null);
            onSelectState([{ label: "", value: "" }]);
        },
        [setFromDate, setToDate, onSelectOption, onSelectState]
    );

    const filterStatus = useMemo(() => {
        if (
            (selectedOption[0]?.value === "Created_Date" ||
                selectedOption[0]?.value === "Assigned_Date") &&
            (!fromDate || !toDate)
        ) {
            return true;
        } else
            return selectedOption[0]?.value === "Status" && !cardState[0].label;
    }, [fromDate, toDate, cardState, selectedOption]);

    return (
        <div className="mt-3">
            {showFilters ? (
                <Card>
                    <Card.Body>
                        <div className="d-flex flex-row text-center align-items-center">
                            <FilterDropdown
                                size="sm"
                                placeHolder="Filter By"
                                selectedValue={selectedOption}
                                selectOptions={filterOptions}
                                disabled={isLoading}
                                onChangeSelect={onChangeSelectOption}
                            />

                            {selectedOption && selectedOption.length > 0 && (
                                <>
                                    {selectedOption[0]?.value ===
                                        "Created_Date" ||
                                    selectedOption[0]?.value ===
                                        "Assigned_Date" ? (
                                        <>
                                            <div className="mx-2 ">between</div>
                                            <FormDate
                                                disabled={isLoading}
                                                onChange={fromDateChange}
                                                date={fromDate}
                                                size="sm"
                                            />
                                            <div className="mx-2 ">and</div>
                                            <FormDate
                                                disabled={isLoading}
                                                onChange={setToDate}
                                                date={toDate}
                                                size="sm"
                                                minDate={new Date(fromDate)}
                                            />
                                        </>
                                    ) : (
                                        <>
                                            <div className="mx-2 ">is</div>
                                            {selectedOption[0]?.value ===
                                            "Status" ? (
                                                <FilterDropdown
                                                    size="sm"
                                                    selectOptions={
                                                        selectCardState
                                                    }
                                                    selectedValue={cardState}
                                                    placeHolder={
                                                        "Select status..."
                                                    }
                                                    disabled={isLoading}
                                                    onChangeSelect={
                                                        onSelectState
                                                    }
                                                />
                                            ) : (
                                                <FilterDropdown
                                                    size="sm"
                                                    selectOptions={
                                                        processingStatusOptions
                                                    }
                                                    selectedValue={
                                                        processingState
                                                    }
                                                    placeHolder={
                                                        "Select processing status..."
                                                    }
                                                    disabled={isLoading}
                                                    onChangeSelect={
                                                        onSelectProcessingState
                                                    }
                                                />
                                            )}
                                        </>
                                    )}
                                    <div className="text-center mx-2">
                                        <Button
                                            variant="outline-primary "
                                            size="sm"
                                            className="mx-2"
                                            disabled={isLoading || filterStatus}
                                            onClick={applyFilter}
                                        >
                                            Apply Filter
                                        </Button>
                                    </div>
                                    <div className="text-center mx-2">
                                        <Button
                                            variant="outline-danger "
                                            size="sm"
                                            className="mx-2"
                                            disabled={isLoading}
                                            onClick={clearFilter}
                                        >
                                            Clear Filter
                                        </Button>
                                    </div>
                                </>
                            )}
                        </div>
                    </Card.Body>
                </Card>
            ) : null}
        </div>
    );
};

CardStatsView.defaultProps = {
    cardType: "all",
};

CardFilter.propTypes = {
    showFilters: PropTypes.bool.isRequired,
    applyFilter: PropTypes.func.isRequired,
    onSelectOption: PropTypes.func.isRequired,
    onSelectState: PropTypes.func.isRequired,
    selectedOption: PropTypes.array.isRequired,
    cardState: PropTypes.array.isRequired,
    onsetDateRange: PropTypes.func.isRequired,
    onsetAssignedDateTo: PropTypes.func.isRequired,
};

export default CardFilter;
