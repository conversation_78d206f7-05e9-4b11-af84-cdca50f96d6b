import {CardTypes} from "Data";

const cardTypeArraySelector =({cardType})=>{
    const newCardTypeType=[];
    switch(cardType) {
        case "all":
            newCardTypeType.push(CardTypes.DIGITAL_CARD,CardTypes.REGULAR_CARD,CardTypes.KEY_TAG,CardTypes.REGULAR_CARD_AND_KEY_TAG,CardTypes.EMBOSSED_CARD )
            break;
        case CardTypes.INSTANT_CARD:
            newCardTypeType.push(CardTypes.REGULAR_CARD,CardTypes.KEY_TAG,CardTypes.REGULAR_CARD_AND_KEY_TAG)
            break;
        default:
            newCardTypeType.push(cardType)
    }
    return newCardTypeType
}

export default cardTypeArraySelector;
