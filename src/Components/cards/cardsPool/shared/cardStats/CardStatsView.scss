.card-status-view {
    .card-status-view-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .container-left {
            width: 80%;
            display: flex;
            flex-direction: row;
            align-items: center;

            .color-icon-div {
                display: flex;
                margin-right: 10px;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                color: #ffffff;
                width: 50px;
                height: 40px;
                border-radius: 5px;
            }
        }
    }

    .card-bg-ACTIVE {
        background-color: var(--success);
    }

    .card-bg-ASSIGNED {
        background-color: var(--secondary);
    }

    .card-bg-SUSPENDED {
        background-color: var(--orange);
    }

    .card-bg-DEACTIVATED {
        background-color: var(--danger);
    }
}
