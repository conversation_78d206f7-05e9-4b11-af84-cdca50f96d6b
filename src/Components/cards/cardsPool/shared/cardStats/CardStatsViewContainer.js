import React, { useCallback, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Row, Col } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { getCardSummary } from "Services";
import CardStatusView from "./CardStatsView";
import CardTypeSelector from "../CardTypeSelector";
import { CardStatus } from "Data";

const CardStatsView = ({ setIsLoadingTabs, cardType }) => {
    const [isLoadingStats, setIsLoadingStats] = useState(false);
    const [statData, setStatData] = useState({
        suspendedCards: 0,
        activatedCards: 0,
        assignedCards: 0,
        deactivatedCards: 0,
    });
    const { regionId } = useContext(UserContext);

    const loadCardStats = useCallback(async () => {
        try {
            setIsLoadingTabs(true);
            setIsLoadingStats(true);
            const newCardState = await getCardSummary({
                regionId,
                cardTypes: CardTypeSelector({ cardType }),
            });
            setStatData(newCardState);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load cards statistics!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoadingTabs(false);
            setIsLoadingStats(false);
        }
    }, [cardType, setIsLoadingTabs, regionId, setStatData, setIsLoadingStats]);

    useEffect(() => {
        loadCardStats();
        // eslint-disable-next-line
    }, [cardType]);

    return (
        <div className="card-stats-container">
            <Row>
                <Col>
                    <CardStatusView
                        isLoadingStats={isLoadingStats}
                        cardStatus="Active Cards"
                        cardQuantity={statData.activatedCards}
                        cardColor={CardStatus.ACTIVE}
                    />
                </Col>
                <Col>
                    <CardStatusView
                        isLoadingStats={isLoadingStats}
                        cardStatus="Assigned Cards"
                        cardQuantity={statData.assignedCards}
                        cardColor={CardStatus.ASSIGNED}
                    />
                </Col>
                <Col>
                    <CardStatusView
                        isLoadingStats={isLoadingStats}
                        cardStatus="Suspended Cards"
                        cardQuantity={statData.suspendedCards}
                        cardColor={CardStatus.SUSPENDED}
                    />
                </Col>
                <Col>
                    <CardStatusView
                        isLoadingStats={isLoadingStats}
                        cardStatus="Deactivated Cards"
                        cardQuantity={statData.deactivatedCards}
                        cardColor={CardStatus.DEACTIVATED}
                    />
                </Col>
            </Row>
        </div>
    );
};

CardStatsView.defaultProps = {
    cardType: "all",
};

CardStatsView.propTypes = {
    /**
     * Card type
     */
    cardType: PropTypes.string.isRequired,
};
export default CardStatsView;
