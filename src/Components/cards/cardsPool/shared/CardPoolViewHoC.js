import React, { useContext } from "react";
import { UserContext } from "Contexts";
import { AccessPermissionModules, AccessPermissionModuleNames } from "Data";
import { UnauthorizedAccessMessage } from "Components/utils";
import CardStatsViewContainer from "./cardStats/CardStatsViewContainer";
import CardTableViewContainer from "./cardTable/CardTableViewContainer";

const CardPoolViewHoC = ({ setIsLoadingTabs, cardType }) => {
    const { isAuthorizedForAction } = useContext(UserContext);

    if (
        !isAuthorizedForAction(
            AccessPermissionModuleNames.CARD,
            AccessPermissionModules[AccessPermissionModuleNames.CARD].actions
                .ListCards
        )
    ) {
        return <UnauthorizedAccessMessage />;
    }

    return (
        <div>
            <CardStatsViewContainer
                setIsLoadingTabs={setIsLoadingTabs}
                cardType={cardType}
            />
            <CardTableViewContainer
                setIsLoadingTabs={setIsLoadingTabs}
                cardType={cardType}
            />
        </div>
    );
};
export default CardPoolViewHoC;
