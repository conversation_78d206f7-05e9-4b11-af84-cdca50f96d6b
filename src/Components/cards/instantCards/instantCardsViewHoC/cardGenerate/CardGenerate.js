import React, { useCallback, useContext, useState } from "react";
import { toast } from "react-toastify";
import {
    Button,
    Col,
    Form,
    FormSelect,
    Row,
    SubHeading,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { CardTypes } from "Data";
import { useOnChangeInput } from "Hooks";
import { createCardPrintJobs } from "Services";

const CardTypeSelectOptions = [
    // TODO: Remove if not needed.
    // {
    //     label: "Regular cards",
    //     value: CardTypes.REGULAR_CARD,
    // },
    // TODO: Remove if not needed.
    // {
    //     label: "Regular cards with key tags",
    //     value: CardTypes.REGULAR_CARD_AND_KEY_TAG,
    // },
    {
        label: "Keyring",
        value: CardTypes.KEY_TAG,
    },
];

const CardGenerate = () => {
    const { selectedRegion } = useContext(UserContext);
    const [cardType, setCardType] = useState([]);
    const regionId = selectedRegion._id;
    const [cardAmount, setCardAmount, resetCardAmount] = useOnChangeInput("");
    const [isRequesting, setIsRequesting] = useState(false);
    const [validated, setValidated] = useState(false);

    const requestCreateCardBatchJob = useCallback(
        async (e) => {
            e.preventDefault();
            if (e.target.checkValidity()) {
                try {
                    setIsRequesting(true);
                    setValidated(false);
                    await createCardPrintJobs({
                        jobType: cardType[0].value,
                        regionId,
                        quantity: Number(cardAmount),
                    });
                    setIsRequesting(false);
                    toast.success(
                        `Successfully created a card print job request. You can view the request in the "Pending" tab.`
                    );
                    resetCardAmount();
                    setCardType([]);
                } catch (e) {
                    console.error(e);
                    setIsRequesting(false);
                    toast.error(
                        <div>
                            Failed to create the card generation request!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setValidated(true);
            }
        },
        [
            resetCardAmount,
            cardType,
            cardAmount,
            regionId,
            setIsRequesting,
            setCardType,
            setValidated,
        ]
    );

    return (
        <Row className="mt-5">
            <Col xl="6" lg="6" md="6" sm="12" className="pl-0">
                <SubHeading text="Generate Cards" />
                <Form
                    onSubmit={requestCreateCardBatchJob}
                    validated={validated}
                    noValidate
                >
                    <Form.Group controlId="card-type">
                        <Form.Label className="d-flex align-items-center">
                            Card Type
                            <span className="text-danger">*</span>
                        </Form.Label>
                        <FormSelect
                            allowNew
                            clearButton
                            id="card_type"
                            required
                            multiple={false}
                            onChange={setCardType}
                            options={CardTypeSelectOptions}
                            placeholder="Select a card type..."
                            selected={cardType}
                        />
                    </Form.Group>
                    <Form.Group controlId="card-amount">
                        <Form.Label className="d-flex align-items-center">
                            Card Amount
                            <span className="text-danger">*</span>
                        </Form.Label>
                        <Form.Control
                            type="number"
                            value={cardAmount}
                            onChange={setCardAmount}
                            placeholder="Enter card amount to generate"
                            required
                        />
                    </Form.Group>
                    <div className="text-right mt-3">
                        <Button
                            variant="primary"
                            size="sm"
                            type="submit"
                            disabled={isRequesting}
                        >
                            {isRequesting
                                ? "Generating Cards..."
                                : "Generate Cards"}
                        </Button>
                    </div>
                </Form>
            </Col>
        </Row>
    );
};

export default CardGenerate;
