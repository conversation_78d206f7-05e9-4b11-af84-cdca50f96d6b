import React, {
    use<PERSON><PERSON>back,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    <PERSON><PERSON>,
    Col,
    FormSearch,
    IcIcon,
    Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faBuilding,
    faCreditCard,
    faFilter,
    faFilterSlash,
    faMapMarkerAlt,
    faObjectGroup,
    faPen,
} from "FaICIconMap";
import { UserContext } from "Contexts";
import { CardStatus, CardTypes, InstantCardsStatus } from "Data";
import { useToggle } from "Hooks";
import { getCardStock, getCardSummary } from "Services";
import { getCardTypeVariant } from "Components/common/cardStatus/CardStatus";
import NameIconTemplate from "Components/utils/table/NameIconTemplate";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import CardStatusView from "../../cardsPool/shared/cardStats/CardStatsView";
import CardsTableView from "../../cardsPool/shared/cardTable/CardsTableView";
import CardStockFilter from "./CardStockFilter";

const columns = [
    {
        dataField: "merchantName",
        text: NameIconTemplate({ name: "Merchant", icon: faBuilding }),
    },
    {
        dataField: "merchantLocationName",
        text: NameIconTemplate({
            name: "Merchant Location",
            icon: faMapMarkerAlt,
        }),
    },
    {
        dataField: "assignedCardsCount",
        text: NameIconTemplate({ name: "Assigned Cards", icon: faCreditCard }),
    },

    {
        dataField: "activeCardsCount",
        text: NameIconTemplate({ name: "Remaining Cards", icon: faCreditCard }),
    },
    {
        dataField: "type",
        text: NameIconTemplate({ name: "Type", icon: faObjectGroup }),
    },
    { dataField: "distributeCard" },
];

const defaultSkip = 1,
    defaultLimit = 25;
let searchStateUpdateTimeout;

const CardStock = ({ generateNewCardDistribution }) => {
    const { regionId } = useContext(UserContext);
    const [cardSummary, setCardSummary] = useState({
        readyCards: 0,
    });
    const [searchText, setSearchText] = useState("");
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [selectedOption, setSelectedOption] = useState([]);
    const [selectedCardType, setSelectedCardType] = useState([
        { label: "", value: "" },
    ]);
    const [limit, setLimit] = useState(defaultLimit);
    const [isLoading, setIsLoading] = useState(false);
    const [dataSet, setDataSet] = useState({ totalCount: 0, items: [] });
    const [resetView, setResetView] = useState(false);
    const [skip, setSkip] = useState(defaultSkip);

    const getCardStockData = useCallback(
        async ({ limit, skip, searchKey }) => {
            try {
                setDataSet({ totalCount: 0, items: [] });
                setIsLoading(true);
                const [cardStockResponse, cardStockSummary] = await Promise.all(
                    [
                        getCardStock({
                            limit,
                            skip: (skip - 1) * limit,
                            searchKey: searchKey,
                            cardTypes:
                                selectedCardType[0].value !== ""
                                    ? [selectedCardType[0].value]
                                    : [
                                        CardTypes.REGULAR_CARD,
                                        CardTypes.KEY_TAG,
                                        CardTypes.REGULAR_CARD_AND_KEY_TAG,
                                    ],
                            regionId,
                        }),
                        getCardSummary({
                            cardTypes: [
                                CardTypes.REGULAR_CARD,
                                CardTypes.KEY_TAG,
                                CardTypes.REGULAR_CARD_AND_KEY_TAG,
                            ],
                            regionId,
                        }),
                    ]
                );
                setCardSummary(cardStockSummary);
                setDataSet({
                    totalCount: cardStockResponse.totalCount,
                    items: cardStockResponse.items,
                });
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load card stock!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
                setDataSet({ items: [], totalCount: 0 });
                setCardSummary({
                    readyCards: 0,
                });
            } finally {
                setIsLoading(false);
            }
        },
        [setIsLoading, setDataSet, regionId, selectedCardType]
    );

    const tableData = useMemo(() => {
        return dataSet.items.map(
            ({
                type,
                status,
                merchantId,
                merchantName,
                merchantLocationId,
                merchantLocationName,
                ...rest
            }) => ({
                type: applyBadgeStyling({
                    text: type,
                    variant: getCardTypeVariant(type),
                }),
                merchantName: merchantName || "~ unknown",
                merchantLocationName: merchantLocationName || "~ unknown",
                distributeCard: (
                    <Button
                        onClick={generateNewCardDistribution}
                        variant="primary"
                        size="sm"
                        data-type={type}
                        data-event_key={InstantCardsStatus.CARD_DISTRIBUTION}
                        data-merchant_id={merchantId || ""}
                        data-merchant_name={merchantName || ""}
                        data-location_id={merchantLocationId || ""}
                        data-location_name={merchantLocationName || ""}
                    >
                        <IcIcon className="mr-2" icon={faPen} />
                        Distribute Card
                    </Button>
                ),
                ...rest,
            })
        );
    }, [dataSet.items, generateNewCardDistribution]);

    const setSearch = useCallback(
        (searchText) => {
            if (searchStateUpdateTimeout) {
                clearTimeout(searchStateUpdateTimeout);
            }
            setSearchText(searchText);

            searchStateUpdateTimeout = setTimeout(() => {
                getCardStockData({
                    skip: skip,
                    limit: limit,
                    searchKey: searchText,
                });
            }, 2000);
        },
        [setSearchText, getCardStockData, limit, skip]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            getCardStockData({
                skip: newSkip,
                limit: limit,
                searchKey: searchText,
            });
        },
        [setSkip, limit, getCardStockData, searchText]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            getCardStockData({
                skip: defaultSkip,
                limit: newLimit,
                searchKey: searchText,
            });
        },
        [getCardStockData, setSkip, setLimit, searchText]
    );
    const applyFilter = useCallback(() => {
        getCardStockData({ limit, skip: defaultSkip });
    }, [limit, getCardStockData]);

    const resetFilter = useCallback(() => {
        setSelectedCardType([{ label: "", value: "" }]);
        setSelectedOption([{ label: "", value: "" }]);
        setResetView(true);
    }, [setSelectedCardType, setSelectedOption]);

    useEffect(() => {
        getCardStockData({ limit, skip: defaultSkip });
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (selectedOption.length !== 0 && resetView) {
            getCardStockData({ limit, skip: defaultSkip });
            setResetView(false);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedOption, setResetView]);

    return (
        <>
            <Row>
                <Col xs={4} md={4} lg={4}>
                    <CardStatusView
                        isLoadingStats={isLoading}
                        cardStatus="Available cards for distribution"
                        cardQuantity={cardSummary.readyCards}
                        cardColor={CardStatus.ACTIVE}
                    />
                </Col>
                <Col xs={2} md={2} lg={2} />
                <Col
                    xs={6}
                    md={6}
                    lg={6}
                    className="d-flex justify-content-between align-items-center"
                >
                    <div className="w-75">
                        <FormSearch
                            placeholder="Search"
                            selected={searchText}
                            onChange={setSearch}
                            id="search-cards"
                        />
                    </div>
                    <Button
                        variant={`${!showFilters ? "outline-" : ""}primary`}
                        size="sm"
                        disabled={isLoading}
                        onClick={toggleShowFilters}
                    >
                        <IcIcon
                            className="mr-2"
                            size="lg"
                            icon={showFilters ? faFilterSlash : faFilter}
                        />
                        {showFilters ? "Hide Filters" : "Filter By"}
                    </Button>
                </Col>
            </Row>
            <Row className="mt-4">
                <Col xs={12} md={12} lg={12}>
                    <CardStockFilter
                        showFilters={showFilters}
                        setSelectedOption={setSelectedOption}
                        selectedOption={selectedOption}
                        setSelectedCardType={setSelectedCardType}
                        selectedCardType={selectedCardType}
                        resetFilter={resetFilter}
                        applyFilter={applyFilter}
                    />
                </Col>
            </Row>
            <Row>
                <CardsTableView
                    columns={columns}
                    data={tableData}
                    sizePerPage={limit}
                    page={skip}
                    onChangePagination={onChangePagination}
                    onChangePageSize={onChangePageSize}
                    totalCount={dataSet.totalCount}
                    isLoading={isLoading}
                />
            </Row>
        </>
    );
};

CardStock.defaultProps = { generateNewCardDistribution: () => {} };

CardStock.propTypes = { generateNewCardDistribution: PropTypes.func };

export default CardStock;
