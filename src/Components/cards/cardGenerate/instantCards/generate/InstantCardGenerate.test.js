import React from 'react'
import { create } from "react-test-renderer";
import InstantCardGenerate from './InstantCardGenerate';
import {merchants,regionId,merchantLocations} from "TestHelpers";
import { MemoryRouter } from 'react-router-dom'

describe('InstantCardGenerate component snapshot', () => {
    test('Matches the snapshot', ()=> {

        const component = create(
                <MemoryRouter>
                    <InstantCardGenerate merchants={merchants} regionId={regionId} merchantLocations={merchantLocations}/>
                </MemoryRouter>);
        expect(component.toJSON()).toMatchSnapshot();

    });
});
