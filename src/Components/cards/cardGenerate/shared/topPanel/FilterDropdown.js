import React from "react";
import PropTypes from "prop-types";
import { Form } from "@shoutout-labs/shoutout-themes-enterprise";

const FilterDropdown = ({
    disabled,
    onChangeSelect,
    selectOptions,
    placeHolder,
    selectedValue,
    labelKey,
    groupBy,
}) => {
    return (
        <Form.Select
            id="basic-typeahead-single"
            size="sm"
            labelKey={labelKey}
            disabled={disabled}
            onChange={onChangeSelect}
            options={selectOptions}
            placeholder={placeHolder}
            selected={selectedValue}
            groupBy={groupBy}
        />
    );
};

FilterDropdown.defaultProps = {
    labelKey: "label",
    groupBy: "",
};

FilterDropdown.propTypes = {
    /**
     * onChangeSelect - Change filtering option
     */
    onChangeSelect: PropTypes.func.isRequired,
    /**
     * selectOptions - Available options
     */
    selectOptions: PropTypes.array.isRequired,
    /**
     * placeholder value
     */
    placeHolder: PropTypes.string,
    /**
     * selectedValue
     */
    selectedValue: PropTypes.array.isRequired,
    /**
     * LabelKey for typeahead
     */
    labelKey: PropTypes.string,
};

export default FilterDropdown;
