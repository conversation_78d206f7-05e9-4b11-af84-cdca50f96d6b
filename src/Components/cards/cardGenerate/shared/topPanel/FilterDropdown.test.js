import React from 'react'
import { create } from "react-test-renderer";
import FilterDropdown from './FilterDropdown';

describe('FilterDropdown component snapshot', () => {
    test('Matches the snapshot', ()=> {

        const props =  {
            onChangeSelect : jest.fn(),
            selectOptions : [
                {label : 'Created Date', value: 'Created Date'},
                {label : 'Merchant', value: 'Merchant'},
                {label : 'Merchant Location', value: 'Merchant Location'},
                {label : 'Card Type', value: 'Card Type'}
            ], 
            placeHolder : "Merchant", 
            selectedValue : [ {label : 'Created Date', value: 'Created Date'}]
          }

        const component = create(<FilterDropdown props={props}/>);
        expect(component.toJSON()).toMatchSnapshot();

    });
});