import React, { use<PERSON><PERSON>back, useContext, useMemo, useState } from "react";
import PropTypes from "prop-types";
import { UilPrint } from "@iconscout/react-unicons";
import {
    Button,
    FormSearch,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faFilter, faFilterSlash, faSync } from "FaICIconMap";
import { DataContext } from "Contexts";
import { CardTypes, CardGenerateJobStatus, InstantCardsStatus } from "Data";
import { useToggle } from "Hooks";
import { getDefaultValuesOfFilters } from "Utils";
import QueryParamFilters from "Components/common/queryParamFilters/QueryParamFilters";

const selectOptionsDigital = [{ label: "Created On", value: "createdOnRange" }];

const selectOptionsEmbossed = [
    { label: "Created On", value: "createdOnRange" },
];

const selectOptionsEmbossedNewRequest = [
    { label: "Pickup Location", value: "merchantLocationId" },
    { label: "Requested On", value: "requestedOnRange" },
];

const selectOptionsForDistributionJobs = [
    { label: "Created On", value: "createdOnRange" },
    { label: "Merchant", value: "merchantId" },
    { label: "Merchant Location", value: "merchantLocationId" },
    { label: "Card Type", value: "cardTypes" },
];

const selectOptionsForPrintJobs = [
    { label: "Created On", value: "createdOnRange" },
    { label: "Card Type", value: "cardTypes" },
];

const cardTypesList = [
    { label: "Keyring", value: CardTypes.KEY_TAG },
    // TODO: Remove below commented out options if not needed.
    /* 
        { label: "Regular", value: CardTypes.REGULAR_CARD },
        { label: "Regular & Key Tag", value: CardTypes.REGULAR_CARD_AND_KEY_TAG }, 
    */
];

const inputKeys = [
    {
        key: "select",
        values: ["merchantId", "merchantLocationId", "cardTypes"],
    },
    {
        key: "date-range",
        values: {
            requestedOnRange: {
                fromDateKey: "fromEmbossRequestedOn",
                toDateKey: "toEmbossRequestedOn",
            },
            createdOnRange: {
                fromDateKey: "createdOnFrom",
                toDateKey: "createdOnTo",
            },
        },
    },
];

const TopPanel = ({
    type,
    isLoading,
    isReloading,
    searchText,
    statusOfProcess,
    isSelectedItems,
    confimCreatePrintJob,
    isCardView,
    appliedFilters,
    reloadDataText,
    setAppliedFilters,
    onSearch,
    onReload,
}) => {
    const {
        allMerchantsForDropdown = [],
        allMerchantLocationsForDropdown = [],
    } = useContext(DataContext);
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [appliedFilterRows, setAppliedFilterRows] = useState([]);

    const getQueryParamData = useCallback(
        (arrayToReduce) =>
            arrayToReduce.reduce((result, item) => {
                const filterInput =
                    inputKeys.find((iK) => {
                        if (iK.key === "date-range") {
                            return Object.keys(iK.values).includes(item.value);
                        } else {
                            return iK.values.includes(item.value);
                        }
                    })?.key || "";
                let options = [];
                let labelKey = "label";
                let valueKey = "value";
                let groupBy = "";

                const defaultValues = getDefaultValuesOfFilters(
                    { filterInput, filterKey: item.value },
                    inputKeys
                );

                if (filterInput === "select") {
                    switch (item.value) {
                        case "merchantId":
                            options = allMerchantsForDropdown;
                            labelKey = "merchantName";
                            valueKey = "_id";
                            break;
                        case "merchantLocationId":
                            options = allMerchantLocationsForDropdown;
                            labelKey = "locationName";
                            valueKey = "_id";
                            groupBy = "merchantName";
                            break;
                        case "cardTypes":
                            options = cardTypesList;
                            break;
                        default:
                            break;
                    }
                }

                result[item.value] = {
                    filterInput,
                    key: item.value,
                    id: item.value,
                    name: item.value,
                    valueKey,
                    placeholder: item?.label?.toLowerCase() || "",
                    ...defaultValues,
                    ...(filterInput === "select"
                        ? { options, labelKey, groupBy }
                        : {}),
                };

                return result;
            }, {}),
        [allMerchantsForDropdown, allMerchantLocationsForDropdown]
    );

    const tabQueryFilterData = useMemo(() => {
        let queryParamFilterOptions = selectOptionsDigital;
        let queryParamFilterMetadata = getQueryParamData(selectOptionsDigital);

        switch (type) {
            case CardTypes.DIGITAL_CARD:
                queryParamFilterOptions = selectOptionsDigital;
                queryParamFilterMetadata =
                    getQueryParamData(selectOptionsDigital);
                break;
            case InstantCardsStatus.CARD_PRINTING:
                queryParamFilterOptions = selectOptionsForPrintJobs;
                queryParamFilterMetadata = getQueryParamData(
                    selectOptionsForPrintJobs
                );
                break;
            case InstantCardsStatus.CARD_DISTRIBUTION:
                queryParamFilterOptions = selectOptionsForDistributionJobs;
                queryParamFilterMetadata = getQueryParamData(
                    selectOptionsForDistributionJobs
                );
                break;
            case CardTypes.EMBOSSED_CARD:
                if (isCardView === CardGenerateJobStatus.NEW_REQUEST) {
                    queryParamFilterOptions = selectOptionsEmbossedNewRequest;
                    queryParamFilterMetadata = getQueryParamData(
                        selectOptionsEmbossedNewRequest
                    );
                } else {
                    queryParamFilterOptions = selectOptionsEmbossed;
                    queryParamFilterMetadata = getQueryParamData(
                        selectOptionsEmbossed
                    );
                }
                break;
            default:
                break;
        }

        return { queryParamFilterOptions, queryParamFilterMetadata };
    }, [type, isCardView, getQueryParamData]);

    const filterBtnVariant = useMemo(() => {
        if (isCardView && isCardView === CardGenerateJobStatus.NEW_REQUEST) {
            return "outline-primary";
        } else return `${!showFilters ? "outline-" : ""}primary`;
    }, [isCardView, showFilters]);

    return (
        <div className="mt-5">
            <div className="d-flex justify-content-between">
                <div className="d-flex justify-content-start align-items-center">
                    <div className="search-bar-width">
                        <FormSearch
                            id="search-top-panel-card-generate"
                            placeholder={`Search by ${
                                (type === CardTypes.EMBOSSED_CARD &&
                                    statusOfProcess ===
                                        CardGenerateJobStatus.NEW_REQUEST) ||
                                isCardView
                                    ? "card number"
                                    : "batch id"
                            }...`}
                            selected={searchText}
                            disabled={isLoading || isReloading}
                            onChange={onSearch}
                        />
                    </div>
                    <Button
                        className="shadow-none"
                        variant="link"
                        size="sm"
                        disabled={isLoading || isReloading}
                        onClick={onReload}
                    >
                        {!isReloading && (
                            <div className="d-flex align-items-center">
                                <IcIcon
                                    size="md"
                                    className="mr-2"
                                    icon={faSync}
                                />
                                Reload
                                {reloadDataText ? ` ${reloadDataText}` : ""}
                            </div>
                        )}
                    </Button>
                    <div style={{ fontSize: "0.9rem" }}>
                        {isReloading && (
                            <div className="text-primary">Reloading...</div>
                        )}
                    </div>
                </div>
                <div className="mb-3">
                    {type === CardTypes.EMBOSSED_CARD &&
                        statusOfProcess === CardGenerateJobStatus.NEW_REQUEST &&
                        isCardView && (
                            <Button
                                className="mr-2"
                                variant="primary"
                                size="sm"
                                disabled={isLoading || !isSelectedItems}
                                onClick={confimCreatePrintJob}
                            >
                                <IcIcon
                                    className="mr-2"
                                    size="lg"
                                    icon={UilPrint}
                                />
                                Create Print Job
                            </Button>
                        )}
                    {(!isCardView ||
                        (isCardView &&
                            isCardView ===
                                CardGenerateJobStatus.NEW_REQUEST)) && (
                        <Button
                            variant={filterBtnVariant}
                            size="sm"
                            disabled={isLoading || isReloading}
                            onClick={toggleShowFilters}
                        >
                            <IcIcon
                                className="mr-2"
                                size="lg"
                                icon={showFilters ? faFilterSlash : faFilter}
                            />
                            {showFilters ? "Hide Filters" : "Filter By"}
                        </Button>
                    )}
                </div>
            </div>
            <div className="mt-3">
                {!showFilters && appliedFilters.length !== 0 && (
                    <div className="d-flex align-items-center">
                        <h3 className="mb-0 mr-2">
                            {appliedFilters.length === 1
                                ? "A filter is "
                                : appliedFilters.length + " filters are "}
                            applied.
                        </h3>
                        <Button
                            variant="info"
                            size="sm"
                            disabled={isLoading || isReloading}
                            onClick={toggleShowFilters}
                        >
                            Show Applied Filters
                        </Button>
                    </div>
                )}
                {showFilters && (
                    <QueryParamFilters
                        multipleFilters
                        queryParamFilterMetadata={
                            tabQueryFilterData.queryParamFilterMetadata
                        }
                        queryParamFilterOptions={
                            tabQueryFilterData.queryParamFilterOptions
                        }
                        isLoading={isLoading}
                        appliedFilters={appliedFilters}
                        appliedFilterRows={appliedFilterRows}
                        setAppliedFilters={setAppliedFilters}
                        setAppliedFilterRows={setAppliedFilterRows}
                    />
                )}
                <hr />
            </div>
        </div>
    );
};

TopPanel.defaultProps = {
    isLoading: false,
    isReloading: false,
    searchText: "",
    isSelectedItems: false,
    isCardView: "",
    appliedFilters: [],
    reloadDataText: "",
    confimCreatePrintJob: () => {},
    setAppliedFilters: () => {},
    onSearch: () => {},
    onReload: () => {},
};

TopPanel.propTypes = {
    type: PropTypes.string.isRequired,
    isLoading: PropTypes.bool,
    isReloading: PropTypes.bool,
    searchText: PropTypes.string,
    statusOfProcess: PropTypes.string.isRequired,
    isSelectedItems: PropTypes.bool,
    isCardView: PropTypes.any,
    appliedFilters: PropTypes.array,
    reloadDataText: PropTypes.string,
    confimCreatePrintJob: PropTypes.func,
    setAppliedFilters: PropTypes.func,
    onSearch: PropTypes.func,
    onReload: PropTypes.func,
};

export default TopPanel;
