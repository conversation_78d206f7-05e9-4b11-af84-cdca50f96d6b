import React, { useState, useCallback, useContext } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Row,
    Col,
    IcIcon,
    Button,
    Badge,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faFileExport,
    faCreditCard,
    faCalendar,
    faBuilding,
    faTimes,
} from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    CardTypes,
    CardGenerateJobStatus,
    AccessPermissionModules,
    AccessPermissionModuleNames,
    JobURLParams,
    ExportJobURLParams,
    batchJobIdType,
    cardDistributionStatus,
    InstantCardsStatus,
    CardTypeBadgeVariants,
} from "Data";
import { useToggle } from "Hooks";
import {
    updateCardBatchJobs,
    exportCardList,
    exportFailedEmbossedCardList,
} from "Services";
import { downloadLink, toTitleCase } from "Utils";
import ConfirmationModal from "../ConfirmationModal";
import StatusChangeDropwdown from "../StatusChangeDropwdown";
import HistoryView from "./HistoryView";

import "./BatchDetails.scss";

const BatchDetails = ({
    batchData,
    jobStatus,
    cardsStatus,
    cardType,
    backToBatchView,
    instantCardsStatus,
}) => {
    const { regionId, isAuthorizedForAction } = useContext(UserContext);
    const [selectedStatus, setSeletedStatus] = useState("");
    const [showModel, setShowModel] = useState(false);
    const [isSaving, toggleIsSaving] = useToggle(false);
    const [isRequesting, toggleIsRequesting] = useToggle(false);
    const [showHistoryView, setShowHistoryView] = useState(false);

    const hideModal = useCallback(() => {
        setShowModel(false);
        setSeletedStatus("");
    }, [setShowModel, setSeletedStatus]);

    const onChangeBatchJobStatus = useCallback(
        async (e) => {
            e.stopPropagation();

            const payload = {
                status: selectedStatus,
                note: `Status updated to ${selectedStatus} `,
            };

            try {
                toggleIsSaving();
                await updateCardBatchJobs(
                    JobURLParams[cardsStatus],
                    batchData.id,
                    payload
                );
                toast.success("Successfully updated batch job status");
                toggleIsSaving();
                backToBatchView();
            } catch (e) {
                toggleIsSaving();
                console.error(e);
                toast.error(
                    <div>
                        Failed to update batch job status!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
            hideModal();
        },
        [
            selectedStatus,
            backToBatchView,
            batchData.id,
            hideModal,
            toggleIsSaving,
            cardsStatus,
        ]
    );

    const exportBatchList = useCallback(
        async (e) => {
            e.stopPropagation();

            const queryObj = {
                [`${batchJobIdType[cardsStatus]}`]: e.currentTarget.dataset.id,
            };

            try {
                toggleIsRequesting();
                const url = await exportCardList(
                    ExportJobURLParams[cardsStatus],
                    queryObj
                );
                downloadLink(url.url);
                toggleIsRequesting();
            } catch (e) {
                toggleIsRequesting();
                console.error(e);
                toast.error(
                    <div>
                        Failed to export batch!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [toggleIsRequesting, cardsStatus]
    );

    const exportFailedBatchList = useCallback(
        async (e) => {
            e.stopPropagation();
            try {
                toggleIsRequesting();
                const url = await exportFailedEmbossedCardList("cards/export", {
                    regionId,
                    cardIds: batchData.embossCardIds,
                });
                downloadLink(url.url);
                toggleIsRequesting();
            } catch (e) {
                toggleIsRequesting();
                console.error(e);
                toast.error(e.message || "Please try again later.");
            }
        },
        [toggleIsRequesting, regionId, batchData]
    );

    const onChangeFailedStatus = useCallback(() => {
        setSeletedStatus(CardGenerateJobStatus.FAILED);
        setShowModel(true);
    }, [setSeletedStatus, setShowModel]);

    const onShowHistoryView = useCallback(() => {
        setShowHistoryView(true);
    }, [setShowHistoryView]);

    const onHideHistoryView = useCallback(() => {
        setShowHistoryView(false);
    }, [setShowHistoryView]);

    return (
        <div className="batch-details">
            {~[
                CardTypes.REGULAR_CARD,
                CardTypes.KEY_TAG,
                CardTypes.REGULAR_CARD_AND_KEY_TAG,
                CardTypes.EMBOSSED_CARD,
            ].indexOf(batchData.job) ? (
                <div className="basic-details-top">
                    <Row className="mb-3 pt-3 basic-detail-row">
                        <Col
                            xl="2"
                            lg="2"
                            md="2"
                            className="border-solid-right d-flex align-items-center"
                        >
                            <div className="pr-2">
                                <div className="d-flex justify-content-between">
                                    <div className="d-flex mr-auto">
                                        <h2 className="my-0 py-0">
                                            Batch ID : {batchData.batchId}
                                        </h2>
                                    </div>
                                </div>
                                <Badge
                                    className="px-3 py-2"
                                    variant={
                                        CardTypeBadgeVariants[batchData.job]
                                    }
                                >
                                    {toTitleCase(batchData.job)}
                                </Badge>
                                <div className="mt-3 text-muted">
                                    {`Card Count : `}
                                    <span className="font-weight-bold">
                                        {batchData?.quantity || "-"}
                                    </span>
                                </div>
                            </div>
                        </Col>
                        {cardsStatus ===
                            InstantCardsStatus.CARD_DISTRIBUTION && (
                            <Col
                                xl="2"
                                lg="2"
                                md="2"
                                className="border-solid-right d-flex align-items-center"
                            >
                                <div className="pr-1 contact-div">
                                    <div className="border-solid-bottom panel pr-2">
                                        <div className="d-flex justify-content-between">
                                            <div className="d-flex mr-auto">
                                                <p className="py-1 mb-0 mx-2">
                                                    <IcIcon
                                                        className="mr-2 text-primary"
                                                        size="w-10"
                                                        icon={faBuilding}
                                                    />
                                                    Merchant
                                                </p>
                                            </div>
                                        </div>
                                        <p className="py-1 mb-0 mx-2 font-weight-bold">
                                            {batchData.merchant
                                                ? batchData.merchant
                                                    .merchantName
                                            : "-"}
                                        </p>
                                    </div>
                                    <div className="pr-2 pt-2">
                                        <div className="d-flex justify-content-between">
                                            <div className="d-flex mr-auto">
                                                <p className="py-1 mb-0 mx-2">
                                                    <IcIcon
                                                        className="mr-2 text-primary"
                                                        size="w-10"
                                                        icon={faBuilding}
                                                    />
                                                    Merchant Location
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <p className="py-1 mb-0 mx-2 font-weight-bold">
                                        {batchData.merchantLocation
                                            ? batchData.merchantLocation
                                                .locationName
                                        : "-"}
                                    </p>
                                </div>
                            </Col>
                        )}
                        {cardType !== CardTypes.EMBOSSED_CARD && (
                            <Col
                                xl="3"
                                lg="3"
                                md="3"
                                className="border-solid-right d-flex align-items-center"
                            >
                                <div className="pr-1 contact-div">
                                    <div className="border-solid-bottom panel pr-2">
                                        <div className="d-flex justify-content-between">
                                            <div className="d-flex mr-auto">
                                                <p className="py-1 mb-0 mx-2">
                                                    <IcIcon
                                                        className="mr-2 text-primary"
                                                        size="w-10"
                                                        icon={faCreditCard}
                                                    />
                                                    Starting Card No
                                                </p>
                                            </div>
                                        </div>
                                        <>
                                            {instantCardsStatus ===
                                            InstantCardsStatus.CARD_DISTRIBUTION ? (
                                                <p className="py-1 mb-0 mx-2 font-weight-bold ml-4">
                                                    {batchData?.hasOwnProperty(
                                                        "cardRanges"
                                                    ) &&
                                                    batchData?.cardRanges
                                                        ?.length !== 0
                                                        ? batchData
                                                            ?.cardRanges[0]
                                                            .startingCardNo
                                                    : "-"}
                                                </p>
                                            ) : (
                                                <p className="py-1 mb-0 mx-2 font-weight-bold ml-4">
                                                    {batchData.startingCardNo
                                                        ? batchData.startingCardNo
                                                        : "-"}
                                                </p>
                                            )}
                                        </>
                                    </div>
                                    <div className="pr-2 pt-2">
                                        <div className="d-flex justify-content-between">
                                            <div className="d-flex mr-auto">
                                                <p className="py-1 mb-0 mx-2">
                                                    <IcIcon
                                                        className="mr-2 text-primary"
                                                        size="w-10"
                                                        icon={faCreditCard}
                                                    />
                                                    Ending Card No
                                                </p>
                                            </div>
                                        </div>
                                        {instantCardsStatus ===
                                        InstantCardsStatus.CARD_DISTRIBUTION ? (
                                            <p className="py-1 mb-0 mx-2 font-weight-bold ml-4">
                                                {batchData.hasOwnProperty(
                                                    "cardRanges"
                                                ) &&
                                                batchData?.cardRanges
                                                    ?.length !== 0
                                                    ? batchData?.cardRanges[0]
                                                        .endingCardNo
                                                : "-"}
                                            </p>
                                        ) : (
                                            <p className="py-1 mb-0 mx-2 font-weight-bold ml-4">
                                                {batchData.endingCardNo
                                                    ? batchData.endingCardNo
                                                    : "-"}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </Col>
                        )}
                        <Col
                            xl={
                                cardType === CardTypes.EMBOSSED_CARD ? "8" : "3"
                            }
                            lg={
                                cardType === CardTypes.EMBOSSED_CARD ? "8" : "3"
                            }
                            md={
                                cardType === CardTypes.EMBOSSED_CARD ? "8" : "3"
                            }
                            className="border-solid-right d-flex align-items-center"
                        >
                            <div className="pr-1 contact-div">
                                <div className="w-100 border-solid-bottom panel pr-2">
                                    <div className="d-flex justify-content-between">
                                        <div className="d-flex mr-auto">
                                            <p className="py-1 mb-0 mx-2">
                                                <IcIcon
                                                    className="mr-2 text-primary"
                                                    size="w-10"
                                                    icon={faCalendar}
                                                />
                                                Created On
                                            </p>
                                        </div>
                                    </div>
                                    <p className="py-1 mb-0 mx-2 font-weight-bold ml-4">
                                        {batchData.createdOn}
                                    </p>
                                </div>
                                <div className="pr-2 pt-2">
                                    <div className="d-flex justify-content-between">
                                        <div className="d-flex mr-auto">
                                            <p className="py-1 mb-0 mx-2">
                                                <IcIcon
                                                    className="mr-2 text-primary"
                                                    size="w-10"
                                                    icon={faCalendar}
                                                />
                                                Updated On
                                            </p>
                                        </div>
                                    </div>
                                    <p className="py-1 mb-0 mx-2 font-weight-bold ml-4">
                                        {batchData.updatedOn}
                                    </p>
                                </div>
                            </div>
                        </Col>
                        <Col
                            xl="2"
                            lg="2"
                            md="2"
                            className="d-flex align-items-center"
                        >
                            <div className="pr-1 contact-div">
                                <div className="panel pr-2">
                                    <div className="d-flex justify-content-end">
                                        <div className="d-flex mr-auto">
                                            {(jobStatus ===
                                                CardGenerateJobStatus.PENDING ||
                                                jobStatus ===
                                                    cardDistributionStatus.PROCESSING ||
                                                jobStatus ===
                                                    CardGenerateJobStatus.PRINTING ||
                                                jobStatus ===
                                                    CardGenerateJobStatus.PRINTED ||
                                                jobStatus ===
                                                    CardGenerateJobStatus.DISPATCHED) && (
                                                <StatusChangeDropwdown
                                                    jobStatus={jobStatus}
                                                    setShowModel={setShowModel}
                                                    cardType={cardType}
                                                    setSeletedStatus={
                                                        setSeletedStatus
                                                    }
                                                />
                                            )}
                                            {~[
                                                CardGenerateJobStatus.COMPLETED,
                                                CardGenerateJobStatus.FAILED,
                                            ].indexOf(batchData.status) ? (
                                                <Badge
                                                    className="px-3 py-2"
                                                    variant={
                                                        batchData.status ===
                                                        CardGenerateJobStatus.COMPLETED
                                                            ? "success"
                                                            : "danger"
                                                    }
                                                >
                                                    {batchData.status
                                                        ? toTitleCase(
                                                            batchData.status
                                                        )
                                                    : "~ unknown"}
                                                </Badge>
                                            ) : null}
                                        </div>
                                    </div>
                                </div>
                                <div className="pr-2 pt-2">
                                    <div className="d-flex justify-content-end">
                                        <div className="d-flex mr-auto">
                                            {jobStatus ===
                                                CardGenerateJobStatus.FAILED &&
                                            cardType ===
                                                CardTypes.EMBOSSED_CARD ? (
                                                <Button
                                                    className="ml-3 action-btn"
                                                    size="sm"
                                                    variant="outline-primary"
                                                    onClick={
                                                        exportFailedBatchList
                                                    }
                                                    disabled={
                                                        !isAuthorizedForAction(
                                                            AccessPermissionModuleNames.CARD,
                                                            AccessPermissionModules[
                                                                AccessPermissionModuleNames
                                                                    .CARD
                                                            ].actions.ListCards
                                                        ) || isRequesting
                                                    }
                                                >
                                                    <IcIcon
                                                        className="mr-2"
                                                        size="lg"
                                                        icon={faFileExport}
                                                    />
                                                    {isRequesting
                                                        ? "Exporting..."
                                                        : "Export "}
                                                </Button>
                                            ) : (
                                                <Button
                                                    className="action-btn"
                                                    size="sm"
                                                    variant="outline-primary"
                                                    data-id={batchData.id}
                                                    onClick={exportBatchList}
                                                    disabled={
                                                        !isAuthorizedForAction(
                                                            AccessPermissionModuleNames.CARD,
                                                            AccessPermissionModules[
                                                                AccessPermissionModuleNames
                                                                    .CARD
                                                            ].actions
                                                                .ExportCardBatchJobs
                                                        ) || isRequesting
                                                    }
                                                >
                                                    <IcIcon
                                                        className="mr-2"
                                                        size="lg"
                                                        icon={faFileExport}
                                                    />
                                                    {isRequesting
                                                        ? "Exporting..."
                                                        : "Export"}
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Col>
                    </Row>
                </div>
            ) : (
                <div className="basic-details-top">
                    <Row className="mb-3 pt-3 basic-detail-row">
                        <Col
                            xl="3"
                            lg="3"
                            md="3"
                            className="border-solid-right d-flex align-items-center"
                        >
                            <div className="pr-2">
                                <div className="d-flex justify-content-between">
                                    <div className="d-flex mr-auto">
                                        <h2 className="my-0 py-0">
                                            Batch ID : {batchData.batchId}
                                        </h2>
                                    </div>
                                </div>
                                <Badge
                                    className="px-3 py-2"
                                    variant={
                                        CardTypeBadgeVariants[batchData.job] ||
                                        "default"
                                    }
                                >
                                    {batchData.job
                                        ? toTitleCase(batchData.job)
                                        : "~ unknown"}
                                </Badge>
                                <div className="mt-3 text-muted">
                                    {`Card Count : `}
                                    <span className="font-weight-bold">
                                        {batchData?.quantity || "-"}
                                    </span>
                                </div>
                            </div>
                        </Col>
                        <Col
                            xl="3"
                            lg="3"
                            md="3"
                            className="border-solid-right d-flex align-items-center"
                        >
                            <div className="pr-1 contact-div">
                                <div className="border-solid-bottom panel pr-2">
                                    <div className="d-flex justify-content-between">
                                        <div className="d-flex mr-auto">
                                            <p className="py-1 mb-0 mx-2">
                                                <IcIcon
                                                    className="mr-2 text-primary"
                                                    size="w-10"
                                                    icon={faCreditCard}
                                                />
                                                Starting Card No
                                            </p>
                                        </div>
                                    </div>
                                    <p className="py-1 mb-0 mx-2 font-weight-bold">
                                        {batchData.startingCardNo
                                            ? batchData.startingCardNo
                                            : "-"}
                                    </p>
                                </div>
                                <div className="pr-2 pt-2">
                                    <div className="d-flex justify-content-between">
                                        <div className="d-flex mr-auto">
                                            <p className="py-1 mb-0 mx-2">
                                                <IcIcon
                                                    className="mr-2 text-primary"
                                                    size="w-10"
                                                    icon={faCreditCard}
                                                />
                                                Ending Card No
                                            </p>
                                        </div>
                                    </div>
                                    <p className="py-1 mb-0 mx-2 font-weight-bold">
                                        {batchData.endingCardNo
                                            ? batchData.endingCardNo
                                            : "-"}
                                    </p>
                                </div>
                            </div>
                        </Col>
                        <Col
                            xl="3"
                            lg="3"
                            md="3"
                            className="border-solid-right d-flex align-items-center"
                        >
                            <div className="pr-1 contact-div">
                                <div className="border-solid-bottom panel pr-2">
                                    <div className="d-flex justify-content-between">
                                        <div className="d-flex mr-auto">
                                            <p className="py-1 mb-0 mx-2">
                                                <IcIcon
                                                    className="mr-2 text-primary"
                                                    size="w-10"
                                                    icon={faCreditCard}
                                                />
                                                Quantity
                                            </p>
                                        </div>
                                    </div>
                                    <p className="py-1 mb-0 mx-2 font-weight-bold">
                                        {batchData.quantity
                                            ? batchData.quantity
                                            : "-"}
                                    </p>
                                </div>
                                <div className="pr-2 pt-2">
                                    <div className="d-flex justify-content-between">
                                        <div className="d-flex mr-auto">
                                            <p className="py-1 mb-0 mx-2">
                                                <IcIcon
                                                    className="mr-2 text-primary"
                                                    size="w-10"
                                                    icon={faCalendar}
                                                />
                                                Created On
                                            </p>
                                        </div>
                                    </div>
                                    <p className="py-1 mb-0 mx-2 font-weight-bold">
                                        {batchData.createdOn
                                            ? batchData.createdOn
                                            : "-"}
                                    </p>
                                </div>
                            </div>
                        </Col>
                        <Col
                            xl="3"
                            lg="3"
                            md="3"
                            className="d-flex justify-content-between align-items-center"
                        >
                            <div className="pr-1">
                                <div className="panel pr-2">
                                    <div className="d-flex justify-content-end">
                                        <div className="d-flex mr-auto">
                                            {batchData.status ===
                                                CardGenerateJobStatus.FAILED ||
                                            batchData.status ===
                                                CardGenerateJobStatus.COMPLETED ? (
                                                <Badge
                                                    className="px-3 py-2"
                                                    variant={
                                                        batchData.status ===
                                                        CardGenerateJobStatus.COMPLETED
                                                            ? "success"
                                                            : "danger"
                                                    }
                                                >
                                                    {batchData.status
                                                        ? toTitleCase(
                                                            batchData.status
                                                        )
                                                    : "~ unknown"}
                                                </Badge>
                                            ) : (
                                                <Button
                                                    className="action-btn mr-2"
                                                    size="sm"
                                                    variant="outline-danger"
                                                    data-id={batchData.id}
                                                    onClick={
                                                        onChangeFailedStatus
                                                    }
                                                >
                                                    <IcIcon
                                                        className="mr-2"
                                                        size="lg"
                                                        icon={faTimes}
                                                    />
                                                    Mark as Failed
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <div className="w-100 pr-2 pt-2">
                                    <div className="d-flex">
                                        <Button
                                            className="mr-3"
                                            size="sm"
                                            variant="outline-primary"
                                            data-id={batchData.id}
                                            onClick={exportBatchList}
                                            disabled={isRequesting}
                                        >
                                            <IcIcon
                                                className="mr-2"
                                                size="lg"
                                                icon={faFileExport}
                                            />
                                            {isRequesting
                                                ? "Exporting..."
                                                : "Export "}
                                        </Button>
                                        <Button
                                            className="shadow-none"
                                            variant="link"
                                            size="sm"
                                            onClick={onShowHistoryView}
                                        >
                                            View History
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </Col>
                    </Row>
                </div>
            )}
            {selectedStatus !== "" && (
                <ConfirmationModal
                    showModel={showModel}
                    hideModal={hideModal}
                    selectedStatus={selectedStatus}
                    onChangeBatchJobStatus={onChangeBatchJobStatus}
                    isSaving={isSaving}
                />
            )}
            <HistoryView
                show={showHistoryView}
                handleClose={onHideHistoryView}
                historyEvents={batchData?.historyEvents}
            />
        </div>
    );
};

BatchDetails.propTypes = {
    /**
     * Batch data
     */
    batchData: PropTypes.object.isRequired,
    /**
     * Load Batch Data
     */
    loadBatchData: PropTypes.func,
};

export default BatchDetails;
