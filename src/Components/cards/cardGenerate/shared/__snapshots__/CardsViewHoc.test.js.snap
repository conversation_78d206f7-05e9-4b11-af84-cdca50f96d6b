// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CardsViewHoc component snapshot Matches the snapshot 1`] = `
<div>
  <div
    className="mt-5"
  >
    <div
      className="d-flex justify-content-between"
    >
      <div
        className="d-flex justify-content-start align-items-center"
      >
        <div
          className="search-bar-width"
        >
          <div
            className="rbt select-typeahead select-search "
            style={
              Object {
                "outline": "none",
                "position": "relative",
              }
            }
            tabIndex={-1}
          >
            <div
              style={
                Object {
                  "display": "flex",
                  "flex": 1,
                  "height": "100%",
                  "position": "relative",
                }
              }
            >
              <input
                aria-autocomplete="both"
                aria-expanded={false}
                aria-haspopup="listbox"
                autoComplete="off"
                className="select-input  form-control"
                disabled={true}
                onBlur={[Function]}
                onChange={[Function]}
                onClick={[Function]}
                onFocus={[Function]}
                onKeyDown={[Function]}
                placeholder="Search by card number..."
                role="combobox"
                type="text"
                value=""
              />
              <svg
                className="sh-icon icon-sm action-button"
                viewBox="0 0 13.94 13.942"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M13.75 12.054l-2.714-2.715a.653.653 0 00-.463-.191h-.444a5.661 5.661 0 10-.98.98v.444a.653.653 0 00.191.463l2.715 2.715a.651.651 0 00.923 0l.771-.771a.657.657 0 00.001-.925zM5.663 9.149a3.485 3.485 0 113.486-3.486 3.483 3.483 0 01-3.486 3.486z"
                />
              </svg>
              <input
                aria-hidden={true}
                className="rbt-input-hint"
                readOnly={true}
                style={
                  Object {
                    "backgroundColor": "transparent",
                    "borderColor": "transparent",
                    "boxShadow": "none",
                    "color": "rgba(0, 0, 0, 0.54)",
                    "left": 0,
                    "pointerEvents": "none",
                    "position": "absolute",
                    "top": 0,
                    "width": "100%",
                  }
                }
                tabIndex={-1}
                value=""
              />
            </div>
          </div>
        </div>
        <button
          className="shadow-none btn btn-link btn-sm"
          disabled={true}
          onClick={[Function]}
          type="button"
        >
          <div
            className="d-flex align-items-center"
          >
            <svg
              className="ic-icon icon-md mr-2"
              fill="currentColor"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.91,15.51H15.38a1,1,0,0,0,0,2h2.4A8,8,0,0,1,4,12a1,1,0,0,0-2,0,10,10,0,0,0,16.88,7.23V21a1,1,0,0,0,2,0V16.5A1,1,0,0,0,19.91,15.51ZM12,2A10,10,0,0,0,5.12,4.77V3a1,1,0,0,0-2,0V7.5a1,1,0,0,0,1,1h4.5a1,1,0,0,0,0-2H6.22A8,8,0,0,1,20,12a1,1,0,0,0,2,0A10,10,0,0,0,12,2Z"
              />
            </svg>
            Reload
             Card List
          </div>
        </button>
        <div
          style={
            Object {
              "fontSize": "0.9rem",
            }
          }
        />
      </div>
      <div
        className="mb-3"
      />
    </div>
    <div
      className="mt-3"
    >
      <hr />
    </div>
  </div>
  <div
    className="d-flex justify-content-start"
  >
    <div
      className="w-100"
    >
      <div
        className="h-100 card-table"
      >
        <div>
          <div
            className="bs-table-overlay"
          >
            <div
              className="loading-overlay active"
            >
              <div
                className="loading-component"
                data-testid="loading-component"
              >
                <div
                  className="loader-content"
                  data-testid="loading-content"
                />
              </div>
            </div>
            <div
              className="react-bootstrap-table"
            >
              <table
                className="table table-bordered bs-table table-borderless table-hover"
              >
                <thead>
                  <tr
                    className="bs-header"
                  >
                    <th
                      style={
                        Object {
                          "width": "15%",
                        }
                      }
                      tabIndex={0}
                    >
                      <div
                        className="d-flex align-items-center"
                      >
                        <svg
                          className="ic-icon icon-lg mr-2 text-black"
                          fill="currentColor"
                          height="24"
                          viewBox="0 0 24 24"
                          width="24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M7,15h3a1,1,0,0,0,0-2H7a1,1,0,0,0,0,2ZM19,5H5A3,3,0,0,0,2,8v9a3,3,0,0,0,3,3H19a3,3,0,0,0,3-3V8A3,3,0,0,0,19,5Zm1,12a1,1,0,0,1-1,1H5a1,1,0,0,1-1-1V11H20Zm0-8H4V8A1,1,0,0,1,5,7H19a1,1,0,0,1,1,1Z"
                          />
                        </svg>
                        Card Number
                      </div>
                    </th>
                    <th
                      style={
                        Object {
                          "width": "20%",
                        }
                      }
                      tabIndex={0}
                    >
                      <div
                        className="d-flex align-items-center"
                      >
                        <svg
                          className="ic-icon icon-lg mr-2 text-black"
                          fill="currentColor"
                          height="24"
                          viewBox="0 0 24 24"
                          width="24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M15.71,12.71a6,6,0,1,0-7.42,0,10,10,0,0,0-6.22,8.18,1,1,0,0,0,2,.22,8,8,0,0,1,15.9,0,1,1,0,0,0,1,.89h.11a1,1,0,0,0,.88-1.1A10,10,0,0,0,15.71,12.71ZM12,12a4,4,0,1,1,4-4A4,4,0,0,1,12,12Z"
                          />
                        </svg>
                        Printed Name
                      </div>
                    </th>
                    <th
                      style={
                        Object {
                          "width": "15%",
                        }
                      }
                      tabIndex={0}
                    >
                      <div
                        className="d-flex align-items-center"
                      >
                        <svg
                          className="ic-icon icon-lg mr-2 text-black"
                          fill="currentColor"
                          height="24"
                          viewBox="0 0 24 24"
                          width="24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12,2a8,8,0,0,0-8,8c0,5.4,7.05,11.5,7.35,11.76a1,1,0,0,0,1.3,0C13,21.5,20,15.4,20,10A8,8,0,0,0,12,2Zm0,17.65c-2.13-2-6-6.31-6-9.65a6,6,0,0,1,12,0C18,13.34,14.13,17.66,12,19.65ZM12,6a4,4,0,1,0,4,4A4,4,0,0,0,12,6Zm0,6a2,2,0,1,1,2-2A2,2,0,0,1,12,12Z"
                          />
                        </svg>
                        Pickup Location
                      </div>
                    </th>
                    <th
                      tabIndex={0}
                    >
                      <div
                        className="d-flex align-items-center"
                      >
                        <svg
                          className="ic-icon icon-lg mr-2 text-black"
                          fill="currentColor"
                          height="24"
                          viewBox="0 0 24 24"
                          width="24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.21,6.21l.79-.8V10a1,1,0,0,0,2,0V5.41l.79.8a1,1,0,0,0,1.42,0,1,1,0,0,0,0-1.42l-2.5-2.5a1,1,0,0,0-.33-.21,1,1,0,0,0-.76,0,1,1,0,0,0-.33.21l-2.5,2.5a1,1,0,0,0,1.42,1.42ZM18,7.56A1,1,0,1,0,16.56,9,6.45,6.45,0,1,1,7.44,9,1,1,0,1,0,6,7.56a8.46,8.46,0,1,0,12,0Z"
                          />
                        </svg>
                        Card Status
                      </div>
                    </th>
                    <th
                      tabIndex={0}
                    >
                      <div
                        className="d-flex align-items-center"
                      >
                        <svg
                          className="ic-icon icon-lg mr-2 text-black"
                          fill="currentColor"
                          height="24"
                          viewBox="0 0 24 24"
                          width="24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.9917,14.502a.99974.99974,0,0,0-1,1v1.78229a7.97243,7.97243,0,0,1-2-5.28229,7.29085,7.29085,0,0,1,.05273-.87988.99992.99992,0,1,0-1.98535-.24023A9.17334,9.17334,0,0,0,1.9917,12.002a9.96434,9.96434,0,0,0,2.41687,6.5H2.9917a1,1,0,1,0,0,2h4a.98173.98173,0,0,0,.79413-.42181c.01166-.01538.02655-.0268.03741-.043.00666-.00995.00684-.02173.01306-.03186a.96576.96576,0,0,0,.106-.2583.95234.95234,0,0,0,.03143-.15589c.00287-.03088.018-.05749.018-.08911v-4A.99974.99974,0,0,0,6.9917,14.502Zm1.5-8.5H6.70923a7.9737,7.9737,0,0,1,5.28247-2,7.07475,7.07475,0,0,1,.87939.05274,1.00046,1.00046,0,0,0,.24121-1.98633A9.22717,9.22717,0,0,0,11.9917,2.002a9.96421,9.96421,0,0,0-6.5,2.41669V3.002a1,1,0,0,0-2,0v4a.95355.95355,0,0,0,.03931.19471l.00024.00122a.96893.96893,0,0,0,.14117.345l.01142.0169a.97291.97291,0,0,0,.2445.24634c.01093.008.01636.02026.02771.02789.01429.00946.03046.01246.04505.02112a.95817.95817,0,0,0,.17932.084.98784.98784,0,0,0,.26184.05285c.01733.00092.03192.01.04944.01h4a1,1,0,0,0,0-2ZM20.45215,16.80609a.96745.96745,0,0,0-.14124-.34509l-.01129-.01679a.97315.97315,0,0,0-.24469-.24646c-.01092-.00793-.01629-.02026-.02759-.02783-.0108-.00714-.02362-.00738-.0346-.0141a1.15354,1.15354,0,0,0-.40973-.13543c-.03162-.003-.0589-.01844-.09131-.01844h-4a1,1,0,0,0,0,2h1.78241a7.97338,7.97338,0,0,1-5.28241,2,7.07446,7.07446,0,0,1-.8794-.05371,1.00046,1.00046,0,0,0-.24121,1.98633,9.36538,9.36538,0,0,0,1.12061.06738,9.96425,9.96425,0,0,0,6.5-2.41668V21.002a1,1,0,0,0,2,0v-4a.95345.95345,0,0,0-.03931-.1947ZM20.9917,5.502a1,1,0,0,0,0-2h-4a.9519.9519,0,0,0-.19183.0387l-.00666.00134a.96837.96837,0,0,0-.3407.13953l-.01959.01324a.974.974,0,0,0-.2453.24378c-.00787.0108-.02.01611-.02746.02728-.00714.01074-.00739.02344-.0141.03436a1.14563,1.14563,0,0,0-.13636.41266c-.00286.03089-.018.0575-.018.08911v4a1,1,0,1,0,2,0V6.71912a7.97527,7.97527,0,0,1,2,5.28283,7.289,7.289,0,0,1-.05274.87989,1.00106,1.00106,0,0,0,.87208,1.11328,1.02916,1.02916,0,0,0,.12207.00683.99971.99971,0,0,0,.99121-.87988A9.17363,9.17363,0,0,0,21.9917,12.002a9.96411,9.96411,0,0,0-2.417-6.5Z"
                          />
                        </svg>
                        Processing Status
                      </div>
                    </th>
                    <th
                      style={
                        Object {
                          "width": "20%",
                        }
                      }
                      tabIndex={0}
                    >
                      <div
                        className="d-flex align-items-center"
                      >
                        
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="bs-body"
                >
                  <tr>
                    <td
                      className="react-bs-table-no-data"
                      colSpan={6}
                      data-toggle="collapse"
                    />
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div
            className="row react-bootstrap-table-pagination"
          >
            <div
              className="col-md-6 col-xs-6 col-sm-6 col-lg-6"
            >
              <div
                className="btn-group size-per-page-renderer"
                role="group"
              >
                <button
                  className="btn btn-primary btn-sm"
                  onClick={[Function]}
                  type="button"
                >
                  25
                </button>
                <button
                  className="btn btn-outline-primary btn-sm"
                  onClick={[Function]}
                  type="button"
                >
                  50
                </button>
                <button
                  className="btn btn-outline-primary btn-sm"
                  onClick={[Function]}
                  type="button"
                >
                  100
                </button>
              </div>
              <span
                className="react-bootstrap-table-pagination-total"
              >
                 Showing rows 
                0
                 to 
                0
                 of 
                0
              </span>
            </div>
            <div
              className="react-bootstrap-table-pagination-list col-md-6 col-xs-6 col-sm-6 col-lg-6"
            >
              <ul
                className="pagination react-bootstrap-table-page-btns-ul"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
