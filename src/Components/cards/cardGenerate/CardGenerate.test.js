import React from "react";
import { create } from "react-test-renderer";
import CardGenerate from "./CardGenerate";
import { BrowserRouter as Router } from "react-router-dom";
import App from "App";
describe("CardGenerate component snapshot", () => {
  test("Matches the snapshot", () => {
    const component = create(
      <Router>
        <App>
          <CardGenerate />
        </App>
      </Router>
    );
    expect(component.toJSON()).toMatchSnapshot();
  });
});
