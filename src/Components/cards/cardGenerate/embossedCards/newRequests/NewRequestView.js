import React, {
    useMemo,
    useState,
    useCallback,
    useEffect,
    useContext,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faCalendar, faUser, faCreditCard, faPower, faMap } from "FaICIconMap";
import { DataContext, UserContext } from "Contexts";
import { CardGenerateJobStatus, CardStatusVariant } from "Data";
import { getCardList } from "Services";
import {
    formatToCommonReadableFormat,
    getQueryFilters,
    getTruncatedStringWithTooltip,
    toTitleCaseFromCamelCase,
} from "Utils";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import TopPanel from "../../shared/topPanel/TopPanel";
import NewRequestTable from "./NewRequestTable";
import ConfirmCreatePrintJobModal from "./confirmCreatePrintJobModal/ConfirmCreatePrintJobModal";

const defaultColsEmbossed = [
    { name: "id", hidden: true },
    { name: "cardNumber", icon: faCreditCard },
    { name: "printedName", headerStyle: { width: "25%" }, icon: faUser },
    { name: "status", headerStyle: { width: "10%" }, icon: faPower },
    { name: "pickupLocation", headerStyle: { width: "25%" }, icon: faMap },
    { name: "requestedOn", headerStyle: { width: "15%" }, icon: faCalendar },
];

const defaultColumnTemplate = ({ name, icon, ...rest }) => ({
    dataField: name,
    text: (
        <div className="d-flex align-items-center">
            {icon && <IcIcon className="mr-2" size="lg" icon={icon} />}
            {toTitleCaseFromCamelCase(name)}
        </div>
    ),
    sort: false,
    ...rest,
});

const defaultSkip = 1,
    defaultLimit = 25;
let searchStateUpdateTimeout;

const NewRequestView = ({ jobStatus, cardType, setIsLoadingTab }) => {
    const { regionId } = useContext(UserContext);
    const { merchantLocations } = useContext(DataContext);
    const [isLoading, setIsLoading] = useState(false);
    const [isReloading, setIsReloading] = useState(false);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [searchText, setSearchText] = useState("");
    const [newRequestsData, setNewRequestsData] = useState([]);
    const [totalCards, setTotalCards] = useState(0);
    const [selectedItems, setSelectedItems] = useState([]);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [appliedFilters, setAppliedFilters] = useState([]);

    const pickupLocationsObj = useMemo(
        () =>
            Object.values(merchantLocations)
                .reduce((result, location) => {
                    result.push(
                        Object.values(location || {}).filter(
                            (locObj) => locObj?.isPickupLocation
                        )
                    );
                    return result;
                }, [])
                .flat()
                .reduce((result, pickupLocation) => {
                    result[pickupLocation?._id] = pickupLocation;
                    return result;
                }, {}),
        [merchantLocations]
    );

    const loadCardData = useCallback(
        async ({ limit, skip, searchKey = "" }, filters = []) => {
            let queryObj = {
                regionId,
                limit,
                skip: (skip - 1) * limit,
                processingStatus: jobStatus,
                cardTypes: [cardType],
                searchKey,
            };

            try {
                setIsLoadingTab(true);
                setIsLoading(true);
                setSelectedItems([]);

                if (filters.length !== 0) {
                    queryObj = { ...queryObj, ...getQueryFilters(filters) };
                }

                const cardsResponse = await getCardList(queryObj);
                setNewRequestsData(cardsResponse.items);
                setTotalCards(cardsResponse.total);

                setIsLoadingTab(false);
                setIsLoading(false);
            } catch (e) {
                console.error(e);
                setIsLoadingTab(false);
                setIsLoading(false);
                toast.error(
                    <div>
                        Failed to load embossed card requests!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            cardType,
            jobStatus,
            setIsLoadingTab,
            regionId,
            setNewRequestsData,
            setIsLoading,
            setTotalCards,
            setSelectedItems,
        ]
    );

    const onReloadNewRequests = useCallback(async () => {
        setIsReloading(true);
        await loadCardData(
            { limit, skip: defaultSkip, searchKey: searchText },
            appliedFilters
        );
        setSkip(defaultSkip);
        setIsReloading(false);
    }, [
        limit,
        searchText,
        appliedFilters,
        setSkip,
        loadCardData,
        setIsReloading,
    ]);

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadCardData(
                { limit, skip: newSkip, searchKey: searchText },
                appliedFilters
            );
        },
        [limit, searchText, appliedFilters, setSkip, loadCardData]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadCardData(
                {
                    limit: newLimit,
                    skip: defaultSkip,
                    searchKey: searchText,
                },
                appliedFilters
            );
        },
        [searchText, appliedFilters, setLimit, loadCardData, setSkip]
    );

    const onShowConfirmModal = useCallback(
        () => setShowConfirmModal(true),
        [setShowConfirmModal]
    );

    const onHideConfirmModal = useCallback(
        (e, data) => {
            if (data) {
                loadCardData({
                    limit,
                    skip: defaultSkip,
                    searchKey: searchText,
                });
            }
            setShowConfirmModal(false);
        },
        [limit, searchText, loadCardData]
    );

    const columns = useMemo(() => {
        const columns = [];

        defaultColsEmbossed.forEach((item) => {
            columns.push(defaultColumnTemplate(item));
        });

        return columns.sort((a, b) => a.order - b.order);
    }, []);

    const data = useMemo(
        () =>
            newRequestsData.map((card) => ({
                id: card._id,
                cardNumber:
                    card?.cardNo ||
                    applyBadgeStyling({
                        customValue: "Card number not found.",
                    }),
                printedName: getTruncatedStringWithTooltip({
                    value: card.embossCard?.printedName,
                    customUnknownValue: applyBadgeStyling({
                        customValue: "Printed name not found.",
                    }),
                }),
                status: applyBadgeStyling({
                    text: card?.status,
                    variant: CardStatusVariant[card?.status],
                }),
                pickupLocation: getTruncatedStringWithTooltip({
                    value: pickupLocationsObj[
                        card?.embossCard?.merchantLocationId
                    ]?.locationName,
                    customUnknownValue: applyBadgeStyling({
                        customValue: "Pickup location not found.",
                    }),
                }),
                requestedOn: card?.embossCard?.embossRequestedOn
                    ? formatToCommonReadableFormat(
                        card.embossCard.embossRequestedOn
                    )
                    : applyBadgeStyling({
                        customValue: "Requested date not found.",
                    }),
            })),
        [newRequestsData, pickupLocationsObj]
    );

    const onSearch = useCallback(
        (searchText) => {
            if (searchStateUpdateTimeout) {
                clearTimeout(searchStateUpdateTimeout);
            }

            setSearchText(searchText);
            searchStateUpdateTimeout = setTimeout(async () => {
                await loadCardData(
                    {
                        limit,
                        skip: defaultSkip,
                        searchKey: searchText,
                    },
                    appliedFilters
                );
                setSkip(defaultSkip);
            }, 2000);
        },
        [limit, appliedFilters, setSearchText, loadCardData, setSkip]
    );

    useEffect(() => {
        if (jobStatus === CardGenerateJobStatus.NEW_REQUEST) {
            loadCardData(
                {
                    limit,
                    skip: defaultSkip,
                    searchKey: searchText,
                },
                appliedFilters
            );
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [appliedFilters]);

    return (
        <div>
            <TopPanel
                type={cardType}
                isLoading={isLoading}
                searchText={searchText}
                onSearch={onSearch}
                setSearchText={setSearchText}
                statusOfProcess={jobStatus}
                isCardView={CardGenerateJobStatus.NEW_REQUEST}
                appliedFilters={appliedFilters}
                setAppliedFilters={setAppliedFilters}
                isSelectedItems={selectedItems.length > 0}
                confimCreatePrintJob={onShowConfirmModal}
                isReloading={isReloading}
                reloadDataText="New Requests"
                onReload={onReloadNewRequests}
            />
            <NewRequestTable
                columns={columns}
                data={data}
                sizePerPage={limit}
                page={skip}
                onChangePagination={onChangePagination}
                onChangePageSize={onChangePageSize}
                totalCount={totalCards}
                isLoading={isLoading}
                selectedItems={selectedItems}
                setSelectedItems={setSelectedItems}
            />
            {showConfirmModal && (
                <ConfirmCreatePrintJobModal
                    show={showConfirmModal}
                    onHide={onHideConfirmModal}
                    cardType={cardType}
                    selectedItems={selectedItems}
                />
            )}
        </div>
    );
};

NewRequestView.defaultProps = { setIsLoadingTab: () => {} };

NewRequestView.propTypes = {
    jobStatus: PropTypes.string.isRequired,
    cardType: PropTypes.string.isRequired,
    setIsLoadingTab: PropTypes.func,
};

export default NewRequestView;
