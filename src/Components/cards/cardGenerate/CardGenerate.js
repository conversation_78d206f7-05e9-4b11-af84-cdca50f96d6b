import React, { useCallback, useContext, useEffect, useState } from "react";
import {
    Heading,
    Tab,
    Tabs,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { UilCreditCard } from "@iconscout/react-unicons";
import BaseLayout from "../../../Layout/BaseLayout";
import InstantCards from "./instantCards/InstantCards";
import EmbossedCards from "./embossedCards/EmbossedCards";
import DigitalCards from "./digitalCards/DigitalCards";
import {
    CardTypes,
    AccessPermissionModules,
    AccessPermissionModuleNames,
} from "Data";
import { Route, Switch } from "react-router-dom";
import { useHistory, useLocation } from "react-router-dom";

import { UserContext } from "Contexts";
import { UnauthorizedAccessMessage } from "Components/utils/unauthorizedAccessAlert/UnauthorizedAccessMessage";
import "./CardGenerate.scss";

const CardGenerate = () => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const history = useHistory();
    const location = useLocation();
    const [tab, setTab] = useState(CardTypes.INSTANT_CARD);
    const selectedTab = useCallback(
        (e) => {
            setTab(e);
            history.push(`${e.toLowerCase()}`);
        },
        [setTab, history]
    );
    useEffect(() => {
        const pathName = location.pathname.split("/")[3];
        if (pathName) {
            setTab(pathName.toUpperCase());
        }
        // eslint-disable-next-line
    }, [location.pathname]);

    return (
        <>
            <BaseLayout
                topLeft={<Heading text="Generate Cards" />}
                bottom={
                    isAuthorizedForAction(
                        AccessPermissionModuleNames.CARD,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.CARD
                        ].actions.CreateCardBatchJob
                    ) ||
                    isAuthorizedForAction(
                        AccessPermissionModuleNames.CARD,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.CARD
                        ].actions.ListCardBatchJobs
                    ) ? (
                        <>
                            <div className="container-fluid card-generate mt-3">
                                <Tabs
                                    onSelect={selectedTab}
                                    defaultActiveKey={CardTypes.INSTANT_CARD}
                                    activeKey={tab}
                                    transition={false}
                                    id="noanim-tab-example"
                                    className="ml-n4 mb-3 border-solid-bottom"
                                >
                                    <Tab
                                        eventKey={CardTypes.INSTANT_CARD}
                                        title={
                                            <>
                                                <IcIcon
                                                    className="mr-2"
                                                    size="w-10"
                                                    icon={UilCreditCard}
                                                />
                                                <span className="mr-2">
                                                    Instant Cards
                                                </span>
                                            </>
                                        }
                                    >
                                        {/*{tab === CardTypes.INSTANT_CARD && <InstantCards />}*/}
                                    </Tab>
                                    <Tab
                                        eventKey={CardTypes.DIGITAL_CARD}
                                        title={
                                            <>
                                                <IcIcon
                                                    className="mr-2"
                                                    size="w-10"
                                                    icon={UilCreditCard}
                                                />
                                                <span className="mr-2">
                                                    Digital Cards
                                                </span>
                                            </>
                                        }
                                    >
                                        {/*{tab === CardTypes.DIGITAL_CARD && <DigitalCards />}*/}
                                    </Tab>
                                    <Tab
                                        eventKey={CardTypes.EMBOSSED_CARD}
                                        title={
                                            <>
                                                <IcIcon
                                                    className="mr-2"
                                                    size="w-10"
                                                    icon={UilCreditCard}
                                                />
                                                <span className="mr-2">
                                                    Embossed Cards
                                                </span>
                                            </>
                                        }
                                        disabled={
                                          !isAuthorizedForAction(
                                            AccessPermissionModuleNames.CARD,
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames.CARD
                                            ].actions.ListCardBatchJobs
                                        )
                                        }
                                    >
                                        {/*  {tab === CardTypes.EMBOSSED_CARD && <EmbossedCards />}*/}
                                    </Tab>
                                </Tabs>
                                <Switch>
                                    <Route
                                        name="EmbossedCards"
                                        exact
                                        path={`/cards/generate/${CardTypes.EMBOSSED_CARD.toLowerCase()}`}
                                        component={isAuthorizedForAction(
                                          AccessPermissionModuleNames.CARD,
                                          AccessPermissionModules[
                                              AccessPermissionModuleNames.CARD
                                          ].actions.ListCardBatchJobs)?EmbossedCards:UnauthorizedAccessMessage}
                                    />
                                    <Route
                                        name="DigitalCards"
                                        exact
                                        path={`/cards/generate/${CardTypes.DIGITAL_CARD.toLowerCase()}`}
                                        component={DigitalCards}
                                    />
                                    <Route
                                        name="InstantCards"
                                        exact
                                        path={`/cards/generate/${CardTypes.INSTANT_CARD.toLowerCase()}`}
                                        component={InstantCards}
                                    />
                                    <Route
                                        name="InstantCards"
                                        component={InstantCards}
                                    />
                                </Switch>
                            </div>
                        </>
                    ) : (
                        <UnauthorizedAccessMessage></UnauthorizedAccessMessage>
                    )
                }
            />
        </>
    );
};

export default CardGenerate;
