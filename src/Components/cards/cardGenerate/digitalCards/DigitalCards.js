import React, { useContext, useState } from "react";
import { Tab, Tabs } from "@shoutout-labs/shoutout-themes-enterprise";
import { DigitalGenerateContainer } from "./DigitalGenerate";
import { CardTypes } from "Data";
import CardGenerateViewHoc from "../shared/CardGenerateViewHoc";
import { AccessPermissionModules, AccessPermissionModuleNames } from "Data";
import { UserContext } from "Contexts";
const digitalCardViews = {
    GENERATE: "generate",
    HISTORY: "history",
};

const DigitalCards = () => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [tab, setTab] = useState(
        isAuthorizedForAction(
            AccessPermissionModuleNames.CARD,
            AccessPermissionModules[AccessPermissionModuleNames.CARD].actions
                .CreateCardBatchJob
        )
            ? digitalCardViews.GENERATE
            : digitalCardViews.HISTORY
    );

    return (
        <>
            <Tabs
                //defaultActiveKey="generate"
                activeKey={tab}
                transition={false}
                id="noanim-tab-example"
                className="ml-n4 mb-3 border-solid-bottom"
                onSelect={setTab}
            >
                <Tab
                    eventKey={digitalCardViews.GENERATE}
                    title={<span className="mr-2">Generate </span>}
                    disabled={
                        !isAuthorizedForAction(
                            AccessPermissionModuleNames.CARD,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.CARD
                            ].actions.CreateCardBatchJob
                        )
                    }
                >
                    {tab === digitalCardViews.GENERATE && (
                        <DigitalGenerateContainer />
                    )}
                </Tab>
                <Tab
                    eventKey={digitalCardViews.HISTORY}
                    title={<span className="mr-2">History</span>}
                    disabled={
                        !isAuthorizedForAction(
                            AccessPermissionModuleNames.CARD,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.CARD
                            ].actions.ListCardBatchJobs
                        )
                    }
                >
                    {tab === digitalCardViews.HISTORY && (
                        <CardGenerateViewHoc cardType={CardTypes.DIGITAL_CARD} />
                    )}
                </Tab>
            </Tabs>
        </>
    );
};

export default DigitalCards;
