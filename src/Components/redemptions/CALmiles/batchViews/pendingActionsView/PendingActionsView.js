import React, { useState } from "react";
import { Tab, Tabs } from "@shoutout-labs/shoutout-themes-enterprise";
import { CALMilesJobStatus, PartnerRewardPendingActionsStatus } from "Data";
import IndividualViewHoc from "../../individualViews/IndividualViewHoc";

const PendingActionsView = ({
    jobStatus,
    batchID,
    rewardName,
    rewardId,
    isTabDisabled,
    setIsTabDisabled,
    isReloadRedemptions,
    setIsReloadRedemptions,
    redemptionCount,
    setRedemptionCount,
}) => {
    const [redemptionsTab, setRedemptionsTab] = useState(
        PartnerRewardPendingActionsStatus.ALL
    );

    return (
        <div>
            <Tabs
                transition={false}
                id="pending-actions-tabs"
                className="border-solid-bottom"
                activeKey={redemptionsTab}
                onSelect={setRedemptionsTab}
            >
                <Tab
                    eventKey={PartnerRewardPendingActionsStatus.ALL}
                    title={<span className="mr-2">All</span>}
                    disabled={isTabDisabled}
                >
                    {redemptionsTab ===
                        PartnerRewardPendingActionsStatus.ALL && (
                        <IndividualViewHoc
                            jobStatus={jobStatus}
                            redemptionProcessingStatus={
                                PartnerRewardPendingActionsStatus.ALL
                            }
                            batchID={batchID}
                            rewardId={rewardId}
                            rewardName={rewardName}
                            isTabDisabled={isTabDisabled}
                            setIsTabDisabled={setIsTabDisabled}
                            isReloadRedemptions={isReloadRedemptions}
                            setIsReloadRedemptions={setIsReloadRedemptions}
                            redemptionCount={redemptionCount}
                            setRedemptionCount={setRedemptionCount}
                        />
                    )}
                </Tab>
                {jobStatus === CALMilesJobStatus.PROCESSING && (
                    <Tab
                        eventKey={PartnerRewardPendingActionsStatus.PROCESSING}
                        title={<span className="mr-2">Processing</span>}
                        disabled={isTabDisabled}
                    >
                        {redemptionsTab ===
                            PartnerRewardPendingActionsStatus.PROCESSING && (
                            <IndividualViewHoc
                                jobStatus={jobStatus}
                                redemptionProcessingStatus={
                                    PartnerRewardPendingActionsStatus.PROCESSING
                                }
                                batchID={batchID}
                                rewardId={rewardId}
                                rewardName={rewardName}
                                isTabDisabled={isTabDisabled}
                                setIsTabDisabled={setIsTabDisabled}
                                isReloadRedemptions={isReloadRedemptions}
                                setIsReloadRedemptions={setIsReloadRedemptions}
                                redemptionCount={redemptionCount}
                                setRedemptionCount={setRedemptionCount}
                            />
                        )}
                    </Tab>
                )}
                <Tab
                    eventKey={PartnerRewardPendingActionsStatus.COMPLETED}
                    title={<span className="mr-2">Completed</span>}
                    disabled={isTabDisabled}
                >
                    {redemptionsTab ===
                        PartnerRewardPendingActionsStatus.COMPLETED && (
                        <IndividualViewHoc
                            jobStatus={jobStatus}
                            redemptionProcessingStatus={
                                PartnerRewardPendingActionsStatus.COMPLETED
                            }
                            batchID={batchID}
                            rewardId={rewardId}
                            rewardName={rewardName}
                            isTabDisabled={isTabDisabled}
                            setIsTabDisabled={setIsTabDisabled}
                            isReloadRedemptions={isReloadRedemptions}
                            setIsReloadRedemptions={setIsReloadRedemptions}
                            redemptionCount={redemptionCount}
                            setRedemptionCount={setRedemptionCount}
                        />
                    )}
                </Tab>
                <Tab
                    eventKey={PartnerRewardPendingActionsStatus.FAILED}
                    title={<span className="mr-2">Failed</span>}
                    disabled={isTabDisabled}
                >
                    {redemptionsTab ===
                        PartnerRewardPendingActionsStatus.FAILED && (
                        <IndividualViewHoc
                            jobStatus={jobStatus}
                            redemptionProcessingStatus={
                                PartnerRewardPendingActionsStatus.FAILED
                            }
                            batchID={batchID}
                            rewardId={rewardId}
                            rewardName={rewardName}
                            isTabDisabled={isTabDisabled}
                            setIsTabDisabled={setIsTabDisabled}
                            isReloadRedemptions={isReloadRedemptions}
                            setIsReloadRedemptions={setIsReloadRedemptions}
                            redemptionCount={redemptionCount}
                            setRedemptionCount={setRedemptionCount}
                        />
                    )}
                </Tab>
                <Tab
                    eventKey={PartnerRewardPendingActionsStatus.REFUNDED}
                    title={<span className="mr-2">Refunded</span>}
                    disabled={isTabDisabled}
                >
                    {redemptionsTab ===
                        PartnerRewardPendingActionsStatus.REFUNDED && (
                        <IndividualViewHoc
                            jobStatus={jobStatus}
                            redemptionProcessingStatus={
                                PartnerRewardPendingActionsStatus.REFUNDED
                            }
                            batchID={batchID}
                            rewardId={rewardId}
                            rewardName={rewardName}
                            isTabDisabled={isTabDisabled}
                            setIsTabDisabled={setIsTabDisabled}
                            isReloadRedemptions={isReloadRedemptions}
                            setIsReloadRedemptions={setIsReloadRedemptions}
                            redemptionCount={redemptionCount}
                            setRedemptionCount={setRedemptionCount}
                        />
                    )}
                </Tab>
            </Tabs>
        </div>
    );
};

export default PendingActionsView;
