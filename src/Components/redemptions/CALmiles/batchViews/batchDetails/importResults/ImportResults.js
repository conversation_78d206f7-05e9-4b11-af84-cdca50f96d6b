import React, { useCallback, useContext, useMemo, useState } from "react";
import { toast } from "react-toastify";
import { camelCase } from "lodash";
import {
    Button,
    Form,
    IcIcon,
    Modal,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faCheckCircle, faTimesCircle } from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    importRewardDistributionJobs,
    uploadRewardDistributionJobsFile,
} from "Services";
import FileUploaderComponent from "../../../../../common/fileUploaderComponent/FileUploaderComponent";

const ImportResults = ({ rewardName, onHide, showModelImport, batchData }) => {
    const { regionId } = useContext(UserContext);
    const [validatedFile, setValidatedFile] = useState(false);
    const [isFetchingToken, setIsFetchingToken] = useState(false);
    const [isFileProcessed, setIsFileProcessed] = useState(false);
    const [fileProcessErr, setFileProcessErr] = useState("");
    const [isImporting, setIsImporting] = useState(false);
    const [transactionsFile, setTransactionsFile] = useState("");
    const [isImported, setIsImported] = useState(false);

    const fileProcessedState = useMemo(() => {
        if (isFetchingToken) {
            return (
                <h2 className="text-center font-weight-bold">
                    Processing file...
                </h2>
            );
        } else {
            if (isFileProcessed) {
                return (
                    <h2 className="d-flex align-items-center justify-content-center">
                        <IcIcon
                            className={`text-${
                                fileProcessErr ? "danger" : "success"
                            } mr-2`}
                            size="lg"
                            icon={
                                fileProcessErr ? faTimesCircle : faCheckCircle
                            }
                        />
                        {fileProcessErr
                            ? `Error: ${fileProcessErr}`
                            : "Processing successful. File is ready to be imported."}
                    </h2>
                );
            } else {
                return <></>;
            }
        }
    }, [isFetchingToken, isFileProcessed, fileProcessErr]);

    const onCloseAfterSuccessfulImport = useCallback(
        () => onHide(null, "imported"),
        [onHide]
    );

    const onChangTransactionFile = useCallback(
        async (file) => {
            if (file) {
                try {
                    setIsFetchingToken(true);
                    const fileFormData = new FormData();
                    fileFormData.append("file", file);
                    const uploadResponse =
                        await uploadRewardDistributionJobsFile(
                            {
                                regionId,
                                distributionJobId: batchData._id,
                            },
                            fileFormData
                        );
                    setTransactionsFile(uploadResponse);
                    setIsFetchingToken(false);
                } catch (e) {
                    setIsFetchingToken(false);
                    console.error(e.message);
                    setFileProcessErr(
                        e.message || "Error unknown! Please try again later."
                    );
                    toast.error(
                        <div>
                            Failed to process the handback file!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                } finally {
                    setIsFileProcessed(true);
                }
            } else {
                setTransactionsFile("");
                setIsFileProcessed(false);
                setFileProcessErr("");
            }
        },
        [
            batchData,
            regionId,
            setIsFetchingToken,
            setTransactionsFile,
            setIsFileProcessed,
            setFileProcessErr,
        ]
    );

    const onClickTransactionFile = useCallback(
        async (e) => {
            e.preventDefault();

            if (transactionsFile) {
                try {
                    const payload = {
                        fieldMappings: transactionsFile.headers.map(
                            (header) => ({
                                fileColumnName: camelCase(header),
                                systemAttributeName: header,
                            })
                        ),
                        fileToken: transactionsFile.fileToken,
                    };

                    setValidatedFile(false);
                    setIsImporting(true);
                    await importRewardDistributionJobs(payload);
                    setIsImported(true);
                    setIsImporting(false);
                    toast.success("Successfully imported the handback file.");
                } catch (e) {
                    setIsImporting(false);
                    console.error(e.message);
                    toast.error(
                        <div>
                            Failed to import the handback file!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setValidatedFile(true);
            }
        },
        [transactionsFile, setValidatedFile, setIsImporting, setIsImported]
    );

    return (
        <Modal
            show={showModelImport}
            onHide={isImported ? onCloseAfterSuccessfulImport : onHide}
            size="lg"
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!isFetchingToken && !isImporting}>
                <Modal.Title>
                    {isImported
                        ? "Import Process Started"
                        : `Import ${rewardName} Handback File`}
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {isImported ? (
                    <div>
                        It may take a while for the new changes in redemptions
                        to be reflected on the loyalty dashboard.
                    </div>
                ) : (
                    <>
                        <Form.Group className="mt-3">
                            <Form.Label className="mb-2">
                                {rewardName} Handback File{" "}
                                <span className="text-danger">*</span>
                            </Form.Label>
                            <div
                                className={transactionsFile && "border rounded"}
                            >
                                <FileUploaderComponent
                                    disabled={isFetchingToken || isImporting}
                                    onChange={onChangTransactionFile}
                                    hasClickNext={false}
                                    customFileTypes=".txt"
                                />
                            </div>
                            {validatedFile && (
                                <span className="text-danger p-2">
                                    Please select a file.
                                </span>
                            )}
                        </Form.Group>
                        {fileProcessedState}
                        <div className="my-3">
                            <p className="font-weight-bold">
                                Please check if the file meets the following
                                requirements.
                            </p>
                            <p className="pl-3">
                                * File extention must be{" "}
                                <span className="text-secondary">.txt</span>{" "}
                                {`(eg: filename.txt)`}
                            </p>
                            <p className="pl-3">
                                * Must be less than{" "}
                                <span className="text-secondary">25MB</span> or{" "}
                                <span className="text-secondary">400,000</span>{" "}
                                rows. Split larger files into multiple files
                            </p>
                            {/* <p className="pl-3">
                                * The first row must contain{" "}
                                <span className="text-secondary">
                                    column names
                                </span>
                                {`(eg: Points, Loyalty ID)`}
                            </p> */}
                        </div>
                    </>
                )}
            </Modal.Body>
            <Modal.Footer>
                <Button
                    variant={isImported ? "primary" : "outline-primary"}
                    size="sm"
                    type="button"
                    onClick={isImported ? onCloseAfterSuccessfulImport : onHide}
                    disabled={isFetchingToken || isImporting}
                >
                    {isImported ? "OK" : "Cancel"}
                </Button>
                {!isImported && (
                    <Button
                        size="sm"
                        disabled={
                            isFetchingToken || isImporting || !transactionsFile
                        }
                        className="ml-2"
                        onClick={onClickTransactionFile}
                        variant="purple"
                    >
                        {isImporting ? "Importing..." : "Import File"}
                    </Button>
                )}
            </Modal.Footer>
        </Modal>
    );
};
export default ImportResults;
