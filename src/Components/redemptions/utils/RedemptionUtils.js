import { Bad<PERSON>, Card } from "@shoutout-labs/shoutout-themes-enterprise";
import { RedemptionStatus, RewardGenerationJobStatus } from "Data";
import { formatToCommonReadableFormat, toTitleCase } from "Utils";

const LogisticFlowStatuses = {
    ...RedemptionStatus,
    ...RewardGenerationJobStatus,
};
const sentenceSeperators = [",", "."];

const formatToEventHistoryCard = (eventHistory, showAllEvents = false) => {
    return eventHistory.map((eventItem) => {
        const eventToString = eventItem?.eventDetails.toString();
        let modifiedEventDetails;
        let multiSentence = [];
        let eventToArr = [];

        if (sentenceSeperators.some((item) => eventToString.includes(item))) {
            if (eventToString.indexOf(",") > -1) {
                eventToArr = eventToString.replace(/\./gi, "").split(",");
            } else {
                eventToArr = eventToString.split(".");
            }

            multiSentence = eventToArr.map((sentence, index) => {
                const sentenceToArray = sentence
                    .split(/(\s+)/)
                    .map((word) =>
                        index === 1 && word === "status"
                            ? toTitleCase(word)
                            : word
                    );

                if (index === 1) {
                    sentenceToArray.splice(0, 2);
                    return sentenceToArray;
                }
                return sentenceToArray;
            });
        }

        if (multiSentence.length > 0) {
            modifiedEventDetails = (
                <ul className="my-0">
                    {multiSentence.map((item) => {
                        const statusesInItem = item.filter(
                            (status) => LogisticFlowStatuses[status]
                        );

                        return (
                            <li className="mt-2">
                                {item.map((word) =>
                                    ~statusesInItem.indexOf(word) ? (
                                        <Badge
                                            className="mx-2 py-2 px-3"
                                            variant={word || "default"}
                                        >
                                            {toTitleCase(word)}
                                        </Badge>
                                    ) : (
                                        word
                                    )
                                )}
                            </li>
                        );
                    })}
                </ul>
            );
        } else {
            const eventDetailsToArr = eventToString.split(/(\s+)/);
            const statusesInArr = eventDetailsToArr.filter(
                (status) => LogisticFlowStatuses[status]
            );

            modifiedEventDetails = eventDetailsToArr.map((word) =>
                ~statusesInArr.indexOf(word) ? (
                    <Badge
                        key={`${new Date().getTime().toString()}_${word}`}
                        className="mx-2 py-2 px-3"
                        variant={word || "default"}
                    >
                        {toTitleCase(word)}
                    </Badge>
                ) : (
                    word
                )
            );
        }

        return (
            <Card
                key={eventItem._id}
                className={`p-2 ${showAllEvents && "my-3"}`}
            >
                <Card.Header className="p-1 font-weight-bold">
                    <div>
                        {eventItem?.eventDate
                            ? formatToCommonReadableFormat(eventItem?.eventDate)
                            : "~ date unknown"}
                    </div>
                </Card.Header>
                <Card.Body className="p-2">
                    <div className="d-flex flex-column">
                        <div className="d-flex align-items-center">
                            {modifiedEventDetails}
                        </div>
                        <small className="mt-2 text-muted">
                            Changed by: <span />
                            {eventItem?.eventBy || "~ unknown"}
                        </small>
                    </div>
                </Card.Body>
            </Card>
        );
    });
};

export { formatToEventHistoryCard };
