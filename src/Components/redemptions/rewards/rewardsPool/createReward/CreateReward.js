import React, { useCallback, useContext, useEffect, useMemo } from "react";
import { Button, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faPlus } from "FaICIconMap";
import { RewardPoolContext } from "../context/RewardPoolContext";
import CreateRewardWizardView from "./createRewardWizardViews/CreateRewardWizardView";

const CreateRewardWizardPage = ({
    isLoading,
    limit,
    skip,
    searchText,
    setSearchText,
    loadRewards,
}) => {
    const {
        showCreateRewardWizard: show,
        setCreateRewardWizard,
        isCreating,
        reset,
        isCreated,
        setIsCreated,
    } = useContext(RewardPoolContext);

    const handleShow = useCallback(
        () => setCreateRewardWizard(true),
        [setCreateRewardWizard]
    );

    const handleClose = useCallback(() => {
        setCreateRewardWizard(false);
        reset();
    }, [setCreateRewardWizard, reset]);

    const reloadRewards = useCallback(
        () => loadRewards({ limit: limit, skip: skip }),
        [limit, skip, loadRewards]
    );

    useEffect(() => {
        if (isCreated) {
            if (searchText) {
                setSearchText("");
            }
            reloadRewards();
            setIsCreated(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isCreated, setIsCreated, setSearchText]);

    return useMemo(
        () => (
            <CreateRewardWizard
                isLoading={isLoading}
                show={show}
                handleShow={handleShow}
                closeModal={handleClose}
                isCreating={isCreating}
            />
        ),
        [isLoading, show, handleShow, handleClose, isCreating]
    );
};

const CreateRewardWizard = ({
    isLoading,
    show,
    handleShow,
    closeModal,
    isCreating,
}) => (
    <>
        <Button
            className="action-btn"
            size="sm"
            variant="primary"
            disabled={isLoading}
            onClick={handleShow}
        >
            <div className="d-flex align-items-center">
                <IcIcon className="mr-2" size="lg" icon={faPlus} />
                Create Reward
            </div>
        </Button>
        <CreateRewardWizardView
            show={show}
            onHide={closeModal}
            isCreating={isCreating}
        />
    </>
);

const CreateReward = ({
    isLoading,
    limit,
    skip,
    searchText,
    setSearchText,
    loadRewards,
}) => {
    return (
        <CreateRewardWizardPage
            isLoading={isLoading}
            limit={limit}
            skip={skip}
            searchText={searchText}
            setSearchText={setSearchText}
            loadRewards={loadRewards}
        />
    );
};

export default CreateReward;
