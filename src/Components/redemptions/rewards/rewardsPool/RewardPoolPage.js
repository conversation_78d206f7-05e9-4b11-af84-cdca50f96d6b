import React from "react";
import { RewardPoolContextProvider } from "./context/RewardPoolContext";
import RewardPool from "./RewardPool";

const RewardPoolPage = ({ tab, setTabIsLoading }) => {
    return (
        <RewardPoolContextProvider>
            <RewardPool
                tab={tab}
                setTabIsLoading={setTabIsLoading}
            />
        </RewardPoolContextProvider>
    );
};

export default RewardPoolPage;