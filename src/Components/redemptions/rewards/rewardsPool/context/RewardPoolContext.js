import React, { useCallback, useContext, useMemo, useReducer } from "react";
import { toast } from "react-toastify";
import update from "immutability-helper";
import { UserContext } from "Contexts";
import {
    RewardPickUp,
    RewardPointValueType,
    RewardStatus,
    RewardType,
    TopUpMethod,
    ValidityPeriod,
} from "Data";
import {
    createReward,
    topUpReward,
    topUpRewardFile,
    uploadImage,
} from "Services";

const RewardPoolContext = React.createContext();

const couponCode = { code: "" };

const initialState = {
    rewardName: "",
    description: "",
    rewardType: [],
    rewardSubType: "VOUCHER",
    validPeriod: "",
    validFrom: "",
    validTo: "",
    portalVisibility: "",
    rewardPickup: "",
    pickUpLocations: [],
    imageUrls: "",
    pointsStatic: 0,
    dailyRedemptionLimit: "",
    dailyRedemptionLimitAmount: 0,
    topUpOption: "",
    quantity: 0,
    fileInfo: "",
    couponCodes: [{ ...couponCode }],
    showCreateRewardWizard: false,
    isCreating: false,
    isCreated: false,
    isLoading: false,
    showTopUp: false,
    toppingUp: false,
    fetchingImage: false,
    isSrcSet: false,
};

const RewardPoolContextActions = {
    SET_ATTRIBUTE_VALUE: "setAttributeValue",
    SET_REWARD_TYPE: "setRewardType",
    SET_IMAGE: "setImage",
    IS_SRC_SET: "isSrcSet",
    FETCHING_IMAGE: "fetchingImage",
    SET_PICKUP_LOCATIONS: "setPickupLocations",
    SET_FILE: "setFileInfo",
    ADD_COUPON_CODE_ITEM: "addCouponCodeItem",
    REMOVE_COUPON_CODE_ITEM: "removeCouponCodeItem",
    SET_COUPON_CODE: "setCouponCode",
    SET_SHOW_WIZARD: "setShowWizard",
    SET_IS_CREATING: "setIsCreating",
    SET_IS_CREATED: "setIsCreated",
    SET_SHOW_TOP_UP: "setShowTopUp",
    SET_IS_TOPPING_UP: "setIsToppingUp",
    RESET_TOP_UP: "resetTopUp",
    RESET: "reset",
};

const reducer = (state, action) => {
    switch (action.type) {
        case RewardPoolContextActions.SET_ATTRIBUTE_VALUE: {
            return {
                ...state,
                [action.key]: action.value,
            };
        }
        case RewardPoolContextActions.SET_REWARD_TYPE: {
            return {
                ...state,
                rewardType: action.rewardType,
            };
        }
        case RewardPoolContextActions.SET_PICKUP_LOCATIONS: {
            return update(state, {
                pickUpLocations: { $set: action.selectedPickupLocations },
            });
        }
        case RewardPoolContextActions.FETCHING_IMAGE: {
            return {
                ...state,
                fetchingImage: action.status,
            };
        }
        case RewardPoolContextActions.SET_IMAGE: {
            return {
                ...state,
                imageUrls: action.imageUrl,
            };
        }
        case RewardPoolContextActions.IS_SRC_SET: {
            return {
                ...state,
                isSrcSet: action.status,
            };
        }
        case RewardPoolContextActions.SET_FILE: {
            return {
                ...state,
                fileInfo: action.fileInfo,
            };
        }
        case RewardPoolContextActions.ADD_COUPON_CODE_ITEM: {
            return update(state, {
                couponCodes: { $push: [{ ...couponCode }] },
            });
        }
        case RewardPoolContextActions.REMOVE_COUPON_CODE_ITEM: {
            return update(state, {
                couponCodes: {
                    $splice: [[action.couponCodeIndex, 1]],
                },
            });
        }
        case RewardPoolContextActions.SET_COUPON_CODE: {
            return update(state, {
                couponCodes: {
                    [action.couponCodeIndex]: {
                        $set: { code: action.couponCode },
                    },
                },
            });
        }
        case RewardPoolContextActions.SET_SHOW_WIZARD: {
            return {
                ...state,
                showCreateRewardWizard: action.showCreateRewardWizard,
            };
        }
        case RewardPoolContextActions.SET_IS_CREATING: {
            return {
                ...state,
                isCreating: action.status,
            };
        }
        case RewardPoolContextActions.SET_IS_CREATED: {
            return {
                ...state,
                isCreated: action.status,
            };
        }
        case RewardPoolContextActions.SET_SHOW_TOP_UP: {
            return {
                ...state,
                showTopUp: action.showTopUp,
            };
        }
        case RewardPoolContextActions.SET_IS_TOPPING_UP: {
            return {
                ...state,
                toppingUp: action.status,
            };
        }
        case RewardPoolContextActions.RESET_TOP_UP: {
            return {
                ...state,
                topUpOption: "",
                quantity: 0,
                fileInfo: "",
                couponCodes: [{ ...couponCode }],
            };
        }
        case RewardPoolContextActions.RESET: {
            return { ...initialState };
        }
        default:
            return state;
    }
};

const RewardPoolContextProvider = (props) => {
    const { selectedRegion } = useContext(UserContext);
    const [state, dispatch] = useReducer(reducer, initialState);

    const setPickupLocations = useCallback(
        (selectedPickupLocations) => {
            dispatch({
                type: RewardPoolContextActions.SET_PICKUP_LOCATIONS,
                selectedPickupLocations,
            });
        },
        [dispatch]
    );

    const setAttributeValue = useCallback(
        (event) => {
            const key = event.currentTarget.name || "";
            const value =
                event.currentTarget.type === "number"
                    ? parseInt(event.currentTarget.value)
                    : event.currentTarget.value || "";

            if (
                key === "rewardPickup" &&
                value === RewardPickUp.ALL_LOCATIONS
            ) {
                setPickupLocations([]);
            }

            dispatch({
                type: RewardPoolContextActions.SET_ATTRIBUTE_VALUE,
                key,
                value,
            });
        },
        [setPickupLocations, dispatch]
    );

    const setValidityDates = useCallback(
        (validtyDateObj) => {
            dispatch({
                type: RewardPoolContextActions.SET_ATTRIBUTE_VALUE,
                key: validtyDateObj.name,
                value: validtyDateObj.value,
            });
        },
        [dispatch]
    );

    const setRewardTypes = useCallback(
        (rewardType) => {
            if (rewardType[0]?.key === RewardType.DIGITAL) {
                dispatch({
                    type: RewardPoolContextActions.SET_ATTRIBUTE_VALUE,
                    key: "rewardPickup",
                    value: "",
                });
                setPickupLocations([]);
            }
            dispatch({
                type: RewardPoolContextActions.SET_REWARD_TYPE,
                rewardType,
            });
        },
        [setPickupLocations, dispatch]
    );

    const setImageUrl = useCallback(
        async (imageUrl) => {
            if (imageUrl[0]) {
                dispatch({
                    type: RewardPoolContextActions.IS_SRC_SET,
                    status: true,
                });
                try {
                    dispatch({
                        type: RewardPoolContextActions.FETCHING_IMAGE,
                        status: true,
                    });
                    const imageIntoUrl = await uploadImage(imageUrl[0]);
                    dispatch({
                        type: RewardPoolContextActions.SET_IMAGE,
                        imageUrl: imageIntoUrl.url,
                    });
                } catch (e) {
                    console.error(e);
                    toast.error(
                        <div>
                            Failed to upload image!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                } finally {
                    dispatch({
                        type: RewardPoolContextActions.FETCHING_IMAGE,
                        status: false,
                    });
                }
            } else {
                dispatch({
                    type: RewardPoolContextActions.IS_SRC_SET,
                    status: false,
                });
            }
        },
        [dispatch]
    );

    const setFileInfo = useCallback(
        (fileInfo) => {
            dispatch({
                type: RewardPoolContextActions.SET_FILE,
                fileInfo,
            });
        },
        [dispatch]
    );

    const addCouponCode = useCallback(
        () => dispatch({ type: RewardPoolContextActions.ADD_COUPON_CODE_ITEM }),
        [dispatch]
    );

    const removeCouponCode = useCallback(
        (couponCodeIndex) => {
            dispatch({
                type: RewardPoolContextActions.REMOVE_COUPON_CODE_ITEM,
                couponCodeIndex,
            });
        },
        [dispatch]
    );

    const setCouponCode = useCallback(
        (couponCodeIndex, couponCodeValue) => {
            dispatch({
                type: RewardPoolContextActions.SET_COUPON_CODE,
                couponCodeIndex,
                couponCode: couponCodeValue,
            });
        },
        [dispatch]
    );

    const setCreateRewardWizard = useCallback(
        (showCreateRewardWizard) => {
            dispatch({
                type: RewardPoolContextActions.SET_SHOW_WIZARD,
                showCreateRewardWizard,
            });
        },
        [dispatch]
    );

    const setIsCreated = useCallback(
        (status) => {
            dispatch({
                type: RewardPoolContextActions.SET_IS_CREATED,
                status,
            });
        },
        [dispatch]
    );

    const setTopUpRewardModal = useCallback(
        (showTopUp) => {
            dispatch({
                type: RewardPoolContextActions.SET_SHOW_TOP_UP,
                showTopUp,
            });
        },
        [dispatch]
    );

    const resetTopUp = useCallback(
        () => dispatch({ type: RewardPoolContextActions.RESET_TOP_UP }),
        [dispatch]
    );

    const reset = useCallback(
        () => dispatch({ type: RewardPoolContextActions.RESET }),
        [dispatch]
    );

    const onCreateReward = useCallback(async () => {
        try {
            let validPeriodData;
            let rewardMetadata = {};
            let rewardPayload;

            if (state.rewardType[0].key === RewardType.TANGIBLE) {
                switch (state.rewardPickup) {
                    case RewardPickUp.ALL_LOCATIONS:
                        rewardMetadata = { allowAllClaimLocations: true };
                        break;
                    case RewardPickUp.SELECTED_LOCATIONS:
                        rewardMetadata = {
                            allowAllClaimLocations: false,
                            claimLocations: state.pickUpLocations.map(
                                (location) => location.value
                            ),
                        };
                        break;
                    default:
                        rewardMetadata = {};
                }
            }

            rewardPayload = {
                regionId: selectedRegion._id,
                name: state.rewardName,
                description: state.description,
                type: state.rewardType[0].key,
                subType: state.rewardSubType,
                imageUrls: [state.imageUrls],
                pointValueType: RewardPointValueType.STATIC,
                pointsStatic: state.pointsStatic,
                dailyRedemptionLimit: state.dailyRedemptionLimit,
                dailyRedemptionAmount: state.dailyRedemptionLimitAmount,
                status: RewardStatus.DRAFT,
                ...(state.rewardType[0].key === RewardType.TANGIBLE
                    ? { rewardMetadata }
                    : {}),
                portalVisibility: state.portalVisibility,
            };

            switch (state.validPeriod) {
                case ValidityPeriod.FIXED:
                    validPeriodData = {
                        validityPeriod: state.validPeriod,
                        validFrom: state.validFrom,
                        validTo: state.validTo,
                    };
                    break;
                case ValidityPeriod.OPEN:
                    validPeriodData = { validityPeriod: state.validPeriod };
                    break;
                default:
                    validPeriodData = {};
            }
            dispatch({
                type: RewardPoolContextActions.SET_IS_CREATING,
                status: true,
            });

            rewardPayload = { ...rewardPayload, ...validPeriodData };
            await createReward(rewardPayload);
            dispatch({
                type: RewardPoolContextActions.SET_IS_CREATING,
                status: false,
            });

            toast.success(
                "Successfully created a new reward. Please top up the newly created reward."
            );
            reset();
            setIsCreated(true);
            setCreateRewardWizard(false);
        } catch (e) {
            console.error(e);
            dispatch({
                type: RewardPoolContextActions.SET_IS_CREATING,
                status: false,
            });
            toast.error(
                <div>
                    Failed to create reward!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        selectedRegion._id,
        state.rewardName,
        state.description,
        state.rewardType,
        state.rewardSubType,
        state.imageUrls,
        state.pointsStatic,
        state.validPeriod,
        state.validFrom,
        state.validTo,
        state.dailyRedemptionLimit,
        state.dailyRedemptionLimitAmount,
        state.rewardPickup,
        state.pickUpLocations,
        state.portalVisibility,
        dispatch,
        setIsCreated,
        reset,
        setCreateRewardWizard,
    ]);

    const onTopUpReward = useCallback(
        async (rewardId) => {
            const rewardTopUpData = {
                regionId: selectedRegion._id,
                rewardId,
            };
            let topUpPayload;

            switch (state.topUpOption) {
                case TopUpMethod.GENERATED.methodValue:
                    topUpPayload = {
                        ...rewardTopUpData,
                        method: TopUpMethod.GENERATED.methodValue,
                        requestedVouchersCount: state.quantity,
                    };
                    break;
                case TopUpMethod.UPLOAD.methodValue:
                    topUpPayload = state.fileInfo;
                    break;
                case TopUpMethod.MANUAL.methodValue:
                    topUpPayload = {
                        ...rewardTopUpData,
                        method: TopUpMethod.MANUAL.methodValue,
                        voucherCodes: state.couponCodes.map(
                            (coupon) => coupon.code
                        ),
                    };
                    break;
                default:
                    topUpPayload = {};
            }

            try {
                dispatch({
                    type: RewardPoolContextActions.SET_IS_TOPPING_UP,
                    status: true,
                });

                if (state.topUpOption === TopUpMethod.UPLOAD.methodValue) {
                    const queryObj = { rewardId: rewardId };
                    await topUpRewardFile(queryObj, topUpPayload);
                } else {
                    await topUpReward(topUpPayload);
                }
                toast.success("Reward top up successful.");
                reset();
                return true;
            } catch (e) {
                console.error(e);
                const errorMsg = "Failed to top up reward!";
                const error = `${e}`;
                if (state.topUpOption === TopUpMethod.MANUAL.methodValue) {
                    const formattedErrorMsg = error
                        .split(" ")
                        .map((item) => {
                            if (item.charAt(1) === "v") {
                                let couponIndex = item.substring(
                                    item.indexOf("[") + 1,
                                    item.lastIndexOf("]")
                                );
                                item = `At coupon code ${
                                    parseInt(couponIndex) + 1
                                },`;
                            }
                            return item;
                        })
                        .join(" ");
                    toast.error(
                        <div>
                            {errorMsg}
                            <br />
                            <br />
                            {formattedErrorMsg}
                        </div>
                    );
                } else {
                    toast.error(
                        <div>
                            {errorMsg}
                            <br />
                            <br />
                            {error}
                        </div>
                    );
                }
                return false;
            } finally {
                dispatch({
                    type: RewardPoolContextActions.SET_IS_TOPPING_UP,
                    status: false,
                });
            }
        },
        [
            dispatch,
            selectedRegion._id,
            state.topUpOption,
            state.quantity,
            state.fileInfo,
            state.couponCodes,
            reset,
        ]
    );

    const value = useMemo(
        () => ({
            ...state,
            setAttributeValue,
            setValidityDates,
            setRewardTypes,
            setImageUrl,
            setPickupLocations,
            setFileInfo,
            addCouponCode,
            removeCouponCode,
            setCouponCode,
            setCreateRewardWizard,
            onCreateReward,
            setIsCreated,
            reset,
            setTopUpRewardModal,
            onTopUpReward,
            resetTopUp,
        }),
        [
            state,
            setAttributeValue,
            setValidityDates,
            setRewardTypes,
            setImageUrl,
            setPickupLocations,
            setFileInfo,
            addCouponCode,
            removeCouponCode,
            setCouponCode,
            setCreateRewardWizard,
            onCreateReward,
            setIsCreated,
            reset,
            setTopUpRewardModal,
            onTopUpReward,
            resetTopUp,
        ]
    );

    return (
        <RewardPoolContext.Provider value={value}>
            {props.children}
        </RewardPoolContext.Provider>
    );
};

export { RewardPoolContext, RewardPoolContextProvider };
