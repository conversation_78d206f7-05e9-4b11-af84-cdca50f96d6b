import React, { use<PERSON>allback, useMemo } from "react";
import paginationFactory, {
    PaginationProvider,
} from "react-bootstrap-table2-paginator";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import moment from "moment";
import {
    Badge,
    BootstrapTable,
    Button,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faCalendar, faCreditCard, faMapMarkerAlt, faPower } from "FaICIconMap";
import { RedemptionLogStatusColorCode } from "Data";
import { toTitleCase } from "Utils";
import { BootstrapTableOverlay } from "Components/utils/UtilComponents";
import ViewMember from "Components/redemptions/shared/viewMember/ViewMember";
import SizePerPageRenderer from "Components/utils/table/sizePerPageRenderer/SizePerPageRenderer";

const defaultSkip = 1;

const sortCaret = (order) => {
    return (
        <span className={`${order ? `caret-4-${order}` : "order-4"}`}></span>
    );
};

const defaultColumnTemplate = ({ name, icon, ...rest }) => ({
    dataField: name,
    text: (
        <div className="d-flex align-items-center">
            {icon && (
                <IcIcon size="lg" icon={icon} className="mr-2 text-black" />
            )}
            {toTitleCase(name)}
        </div>
    ),
    sort: true,
    sortCaret: sortCaret,
    ...rest,
});

const defaultCols = [
    { name: "transaction_id", icon: faCreditCard, sort: false },
    { name: "redeemed_date", icon: faCalendar, sort: false },
    { name: "pick_up_location", icon: faMapMarkerAlt, sort: false },
    { name: "status", icon: faPower, sort: false },
    { name: "_id", hidden: true, icon: null, sort: false },
    { name: "", sort: false },
];

const NoData = ({ loading }) => {
    if (loading) return null;
    return <div>No redemption logs found.</div>;
};

const RedemptionLogsTable = ({
    redemptionLogsList,
    totalCount,
    isLoading,
    limit,
    skip,
    appliedFilters,
    setLimit,
    setSkip,
    loadRedemptionLogs,
    viewMemberDetails,
    memberId,
    onShowMemberDetails,
    onHideMemberDetails,
}) => {
    const columns = useMemo(() => {
        const columns = [];

        defaultCols.forEach((item) => {
            columns.push(defaultColumnTemplate(item));
        });

        return columns;
    }, []);

    const data = useMemo(
        () =>
            Object.values(redemptionLogsList).map((log) => {
                return {
                    transaction_id: <div>{log.transactionId}</div>,
                    redeemed_date: (
                        <div>
                            {" "}
                            {moment(log?.createdOn).format(
                                "MMM DD, YYYY LT"
                            )}{" "}
                        </div>
                    ),
                    pick_up_location: (
                        <div>{log?.pickupLocation?.locationName}</div>
                    ),
                    status: (
                        <div>
                            <Badge
                                className="py-2 px-3"
                                style={RedemptionLogStatusColorCode[log.status]}
                            >
                                {toTitleCase(log.status)}
                            </Badge>
                        </div>
                    ),
                    "": (
                        <div>
                            <Button
                                name="VIEW_MEMBER_DETAILS"
                                id={log.memberId}
                                variant="link"
                                size="sm"
                                onClick={onShowMemberDetails}
                            >
                                View Member
                            </Button>
                        </div>
                    ),
                    _id: log._id,
                };
            }),
        [redemptionLogsList, onShowMemberDetails]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadRedemptionLogs({ limit: limit, skip: newSkip }, appliedFilters);
        },
        [limit, appliedFilters, setSkip, loadRedemptionLogs]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadRedemptionLogs(
                { limit: newLimit, skip: defaultSkip },
                appliedFilters
            );
        },
        [appliedFilters, setLimit, setSkip, loadRedemptionLogs]
    );

    const options = {
        page: skip,
        sizePerPage: limit,
        sizePerPageRenderer: SizePerPageRenderer,
        totalSize: totalCount,
        pageStartIndex: 1,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageList: [
            { text: "10", value: 10 },
            { text: "25", value: 25 },
            { text: "50", value: 50 },
        ],
        onPageChange: onChangePagination,
        onSizePerPageChange: onChangePageSize,
    };

    return (
        <>
            <div className="w-100">
                <div className="h-100 batch-table">
                    <PaginationProvider
                        pagination={paginationFactory(options)}
                        keyField="_id"
                        columns={columns}
                        data={data}
                    >
                        {({ paginationTableProps }) => (
                            <ToolkitProvider
                                keyField="_id"
                                data={data}
                                columns={columns}
                                columnToggle
                            >
                                {(props) => (
                                    <div>
                                        <BootstrapTable
                                            {...paginationTableProps}
                                            remote={{
                                                search: true,
                                                pagination: true,
                                            }}
                                            loading={isLoading}
                                            keyField="_id"
                                            noDataIndication={() => (
                                                <NoData loading={isLoading} />
                                            )}
                                            overlay={BootstrapTableOverlay}
                                            {...props.baseProps}
                                        />
                                    </div>
                                )}
                            </ToolkitProvider>
                        )}
                    </PaginationProvider>
                </div>
            </div>

            <ViewMember
                show={viewMemberDetails}
                onHide={onHideMemberDetails}
                memberId={memberId}
            />
        </>
    );
};

export default RedemptionLogsTable;
