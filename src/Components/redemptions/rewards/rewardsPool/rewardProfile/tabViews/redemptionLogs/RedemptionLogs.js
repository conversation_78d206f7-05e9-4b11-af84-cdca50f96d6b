import React, { useCallback, useEffect, useState, useContext } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Button, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faFilter, faFilterSlash, faSync } from "FaICIconMap";
import { UserContext } from "Contexts";
import { useToggle } from "Hooks";
import { getRedemptionLogs } from "Services";
import { formatToCommonFormat } from "Utils";
import BaseLayout from "Layout/BaseLayout";
import DateFilter from "Components/redemptions/rewards/rewardsPool/shared/dateFilter/DateFilter";
import RedemptionLogsTable from "./RedemptionLogsTable";

const defaultLimit = 10,
    defaultSkip = 1;

const RedemptionLogs = ({ rewardId }) => {
    const { selectedRegion } = useContext(UserContext);
    const [isLoading, setIsLoading] = useState(false);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [redemptionLogs, setRedemptionLogs] = useState([]);
    const [redemptionLogsCount, setRedemptionLogsCount] = useState(0);
    const [isReloading, setIsReloading] = useState(false);
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [getFromDate, setGetFromDate] = useState("");
    const [getToDate, setGetToDate] = useState("");
    const [isSet, setIsSet] = useState(false);
    const [appliedFilters, setAppliedFilters] = useState();
    const [isApplied, setIsApplied] = useState(false);
    const [memberId, setMemberId] = useState("");
    const [viewMemberDetails, setViewMemberDetails] = useState(false);

    const loadRedemptionLogs = useCallback(
        async ({ limit, skip }, filters) => {
            try {
                let queryObj = {
                    limit: limit,
                    skip: (skip - 1) * limit,
                    rewardId,
                    regionId: selectedRegion._id,
                };

                if (filters) {
                    queryObj = { ...queryObj, ...filters };
                }

                setIsLoading(true);
                const redemptionLogsResponse = await getRedemptionLogs(
                    queryObj
                );
                setRedemptionLogs(redemptionLogsResponse.items);
                setRedemptionLogsCount(redemptionLogsResponse.total);
            } catch (e) {
                console.error(e);
                toast.error(
                    e.message ||
                        "Could not load redemption logs! Please try again."
                );
            } finally {
                setIsLoading(false);
            }
        },
        [
            rewardId,
            setIsLoading,
            setRedemptionLogs,
            setRedemptionLogsCount,
            selectedRegion._id,
        ]
    );

    const reloadRedemptionLogs = useCallback(async () => {
        setIsReloading(true);
        await loadRedemptionLogs({ limit, skip: defaultSkip }, appliedFilters);
        setIsReloading(false);
        setSkip(defaultSkip);
    }, [limit, appliedFilters, loadRedemptionLogs, setIsReloading, setSkip]);

    const onFilterBy = useCallback(() => {
        toggleShowFilters();
        if (!appliedFilters) {
            setGetFromDate("");
            setGetToDate("");
            setIsSet(false);
        }
    }, [toggleShowFilters, appliedFilters, setGetFromDate, setGetToDate]);

    const onChangeFromDate = useCallback(
        (date) => {
            setGetFromDate(date);
            if (appliedFilters) {
                setIsApplied(
                    formatToCommonFormat(date) === appliedFilters.fromDate
                );
            }
            setIsSet(true);
        },
        [appliedFilters, setGetFromDate]
    );

    const onChangeToDate = useCallback(
        (date) => {
            setGetToDate(date);
            if (appliedFilters) {
                setIsApplied(
                    formatToCommonFormat(date) === appliedFilters.toDate
                );
            }
            setIsSet(true);
        },
        [appliedFilters, setGetToDate]
    );

    const applyFilter = useCallback(() => {
        if (getToDate === "") {
            setAppliedFilters({
                fromDate: formatToCommonFormat(getFromDate),
            });
        } else if (getFromDate === "") {
            setAppliedFilters({
                toDate: formatToCommonFormat(getToDate),
            });
        } else {
            setAppliedFilters({
                fromDate: formatToCommonFormat(getFromDate),
                toDate: formatToCommonFormat(getToDate),
            });
        }
        setIsApplied(true);
        setSkip(defaultSkip);
    }, [getFromDate, getToDate, setIsApplied, setSkip]);

    const removeFromDate = useCallback(() => {
        setGetFromDate("");
        setAppliedFilters();
        if (appliedFilters?.toDate) {
            setAppliedFilters({
                toDate: formatToCommonFormat(getToDate),
            });
        } else {
            setIsSet(false);
            setIsApplied(false);
        }
        setSkip(defaultSkip);
    }, [
        appliedFilters,
        getToDate,
        setGetFromDate,
        setAppliedFilters,
        setIsSet,
        setIsApplied,
        setSkip,
    ]);

    const removeToDate = useCallback(() => {
        setGetToDate("");
        setAppliedFilters();
        if (appliedFilters?.fromDate) {
            setAppliedFilters({
                fromDate: formatToCommonFormat(getFromDate),
            });
        } else {
            setIsSet(false);
            setIsApplied(false);
        }
        setSkip(defaultSkip);
    }, [
        appliedFilters,
        getFromDate,
        setGetToDate,
        setAppliedFilters,
        setIsSet,
        setIsApplied,
        setSkip,
    ]);

    const resetFilter = useCallback(() => {
        setGetFromDate("");
        setGetToDate("");
        setAppliedFilters();
        setIsSet(false);
        setIsApplied(false);
        setSkip(defaultSkip);
    }, [setGetFromDate, setGetToDate, setIsSet, setIsApplied, setSkip]);

    const onShowMemberDetails = useCallback(
        (e) => {
            setMemberId(e.currentTarget.id);
            setViewMemberDetails(true);
        },
        [setMemberId, setViewMemberDetails]
    );

    const onHideMemberDetails = useCallback(() => {
        setMemberId("");
        setViewMemberDetails(false);
    }, [setMemberId, setViewMemberDetails]);

    useEffect(() => {
        if (rewardId) {
            loadRedemptionLogs({ limit, skip: defaultSkip }, appliedFilters);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [rewardId, appliedFilters]);

    return (
        <div>
            <BaseLayout
                bottom={
                    <div className="w-100 mt-5">
                        <div className=" d-flex flex-row justify-content-between mb-4">
                            <div className="d-flex flex-row align-items-center">
                                <Button
                                    variant="link"
                                    size="sm"
                                    disabled={isLoading || isReloading}
                                    onClick={reloadRedemptionLogs}
                                >
                                    {!isReloading && (
                                        <div className="d-flex align-items-center">
                                            <IcIcon
                                                size="md"
                                                className="mr-2"
                                                icon={faSync}
                                            />
                                            Reload Redemption Logs
                                        </div>
                                    )}
                                </Button>
                                <span style={{ fontSize: "0.9rem" }}>
                                    {isReloading && (
                                        <div className="text-primary">
                                            Reloading...
                                        </div>
                                    )}
                                </span>
                            </div>
                            <Button
                                variant={`${
                                    !showFilters ? "outline-" : ""
                                }primary`}
                                size="sm"
                                disabled={isLoading || isReloading}
                                onClick={onFilterBy}
                            >
                                <IcIcon
                                    className="mr-2"
                                    size="lg"
                                    icon={
                                        showFilters ? faFilterSlash : faFilter
                                    }
                                />
                                {showFilters ? "Hide Filters" : "Filter By"}
                            </Button>
                        </div>
                        <DateFilter
                            appliedFilters={appliedFilters}
                            showFilters={showFilters}
                            isLoading={isLoading}
                            isReloading={isReloading}
                            isSet={isSet}
                            isApplied={isApplied}
                            getFromDate={getFromDate}
                            getToDate={getToDate}
                            onChangeFromDate={onChangeFromDate}
                            onChangeToDate={onChangeToDate}
                            applyFilter={applyFilter}
                            removeFromDate={removeFromDate}
                            removeToDate={removeToDate}
                            resetFilter={resetFilter}
                        />
                        <div className="mt-4">
                            <div className="w-100">
                                <RedemptionLogsTable
                                    redemptionLogsList={redemptionLogs}
                                    totalCount={redemptionLogsCount}
                                    isLoading={isLoading}
                                    limit={limit}
                                    skip={skip}
                                    appliedFilters={appliedFilters}
                                    setLimit={setLimit}
                                    setSkip={setSkip}
                                    loadRedemptionLogs={loadRedemptionLogs}
                                    memberId={memberId}
                                    viewMemberDetails={viewMemberDetails}
                                    onShowMemberDetails={onShowMemberDetails}
                                    onHideMemberDetails={onHideMemberDetails}
                                />
                            </div>
                        </div>
                    </div>
                }
            />
        </div>
    );
};

RedemptionLogs.defaultProps = { rewardId: "" };

RedemptionLogs.propTypes = { rewardId: PropTypes.string };

export default RedemptionLogs;
