import React, { use<PERSON><PERSON>back, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    // Card,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faArrowUp, faFilter, faFilterSlash, faSync } from "FaICIconMap";
import { UserContext } from "Contexts";
import { TopUpMethodOptions, TopUpStatusOptions } from "Data";
import { useToggle } from "Hooks";
import { getRewardById, getTopupLogs } from "Services";
import { getDefaultValuesOfFilters, getQueryFilters } from "Utils";
import BaseLayout from "Layout/BaseLayout";
import QueryParamFilters from "Components/common/queryParamFilters/QueryParamFilters";
import { RewardPoolContext } from "../../../context/RewardPoolContext";
import TopUpReward from "../../../topUpReward/TopUpReward";
import TopUpLogsTable from "./TopUpLogsTable";

const topUpFilters = [
    { label: "Method", value: "method" },
    { label: "Status", value: "status" },
];

const inputKeys = [{ key: "select", values: ["method", "status"] }];

const defaultLimit = 10,
    defaultSkip = 1;

const TopUpLogs = ({
    rewardId,
    reloadAfterTopUp,
    setRewardStatData,
    setIsReloadafterTopUp,
}) => {
    const { selectedRegion } = useContext(UserContext);
    const { showTopUp, setTopUpRewardModal } = useContext(RewardPoolContext);
    const [isLoading, setIsLoading] = useState(false);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [topUpLogs, setTopUpLogs] = useState([]);
    const [topUpLogsCount, setTopUpLogsCount] = useState(0);
    const [isReloading, setIsReloading] = useState(false);
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [appliedFilterRows, setAppliedFilterRows] = useState([]);
    const [appliedFilters, setAppliedFilters] = useState([]);

    const getQueryParamData = (arrayToReduce) =>
        arrayToReduce.reduce((result, item) => {
            const filterInput =
                inputKeys.find((iK) => {
                    if (iK.key === "date-range") {
                        return Object.keys(iK.values).includes(item.value);
                    } else {
                        return iK.values.includes(item.value);
                    }
                })?.key || "";
            let options = [];
            let labelKey = "label";
            let valueKey = "value";
            let groupBy = "";

            const defaultValues = getDefaultValuesOfFilters(
                { filterInput, filterKey: item.value },
                inputKeys
            );

            if (filterInput === "select") {
                switch (item.value) {
                    case "method":
                        options = TopUpMethodOptions;
                        break;
                    case "status":
                        options = TopUpStatusOptions;
                        break;
                    default:
                        break;
                }
            }

            result[item.value] = {
                filterInput,
                key: item.value,
                id: item.value,
                name: item.value,
                valueKey,
                placeholder: item?.label?.toLowerCase() || "",
                ...defaultValues,
                ...(filterInput === "select"
                    ? { options, labelKey, groupBy }
                    : {}),
            };

            return result;
        }, {});

    const tabQueryFilterData = {
        queryParamFilterOptions: topUpFilters,
        queryParamFilterMetadata: getQueryParamData(topUpFilters),
    };

    const loadTopUpLogs = useCallback(
        async ({ limit, skip }, filters = []) => {
            try {
                let queryObj = {
                    limit,
                    skip: (skip - 1) * limit,
                    regionId: selectedRegion?._id,
                    rewardId,
                };

                if (filters.length !== 0) {
                    queryObj = { ...queryObj, ...getQueryFilters(filters) };
                }

                setIsLoading(true);
                const topUpLogsResponse = await getTopupLogs(queryObj);
                setTopUpLogs(topUpLogsResponse.items);
                setTopUpLogsCount(topUpLogsResponse.total);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load top up logs!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setIsLoading(false);
            }
        },
        [
            rewardId,
            selectedRegion?._id,
            setIsLoading,
            setTopUpLogs,
            setTopUpLogsCount,
        ]
    );

    const reloadTopUpLogs = useCallback(async () => {
        setIsReloading(true);
        await loadTopUpLogs({ limit, skip: defaultSkip }, appliedFilters);
        setIsReloading(false);
        setSkip(defaultSkip);
    }, [limit, appliedFilters, loadTopUpLogs, setIsReloading, setSkip]);

    const onShowTopUp = useCallback(
        () => setTopUpRewardModal(true),
        [setTopUpRewardModal]
    );

    const onHideTopUp = useCallback(
        (e, data) => {
            setTopUpRewardModal(false);
        },
        [setTopUpRewardModal]
    );

    const onAfterToppingUp = useCallback(async () => {
        setIsReloadafterTopUp(true);
        await loadTopUpLogs({ limit, skip: defaultSkip }, appliedFilters);
        const rewardDetailsResponse = await getRewardById(rewardId);
        const statsData = {
            totalRewards: rewardDetailsResponse.totalCount,
            redeemedRewards: rewardDetailsResponse.usedCount,
            claimedRewards: rewardDetailsResponse.claimedCount,
            remainingRewards:
                rewardDetailsResponse.totalCount -
                rewardDetailsResponse.usedCount,
        };
        setRewardStatData(statsData);
        setIsReloadafterTopUp(false);
    }, [
        rewardId,
        limit,
        appliedFilters,
        setIsReloadafterTopUp,
        setRewardStatData,
        loadTopUpLogs,
    ]);

    useEffect(() => {
        if (rewardId) {
            loadTopUpLogs({ limit, skip: defaultSkip }, appliedFilters);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [rewardId, appliedFilters]);

    return (
        <div className="redemption-logs-view">
            <BaseLayout
                bottom={
                    <div className="w-100 mt-5">
                        <div className=" d-flex flex-row justify-content-between mb-4">
                            <div className="d-flex align-items-center">
                                <Button
                                    className="btn shadow-none"
                                    variant="link"
                                    size="sm"
                                    disabled={isLoading || isReloading}
                                    onClick={reloadTopUpLogs}
                                >
                                    {!isReloading && (
                                        <div className="d-flex align-items-center">
                                            <IcIcon
                                                size="md"
                                                className="mr-2"
                                                icon={faSync}
                                            />
                                            Reload Top Up Logs
                                        </div>
                                    )}
                                </Button>
                                <span style={{ fontSize: "0.9rem" }}>
                                    {" "}
                                    {isReloading && (
                                        <div className="text-primary">
                                            Reloading...
                                        </div>
                                    )}{" "}
                                </span>
                            </div>
                            <div>
                                <Button
                                    className="mr-2"
                                    variant="primary"
                                    size="sm"
                                    disabled={isLoading || isReloading}
                                    onClick={onShowTopUp}
                                >
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            size="lg"
                                            className="mr-2"
                                            icon={faArrowUp}
                                        />
                                        Top Up
                                    </div>
                                </Button>
                                <Button
                                    variant="outline-primary"
                                    size="sm"
                                    disabled={isLoading || isReloading}
                                    onClick={toggleShowFilters}
                                >
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={
                                            showFilters
                                                ? faFilterSlash
                                                : faFilter
                                        }
                                    />
                                    {showFilters ? "Hide Filters" : "Filter By"}
                                </Button>
                            </div>
                        </div>
                        <div className="mt-3">
                            {!showFilters && appliedFilters.length !== 0 && (
                                <div className="d-flex align-items-center">
                                    <h3 className="mb-0 mr-2">
                                        {appliedFilters.length === 1
                                            ? "A filter is "
                                            : appliedFilters.length +
                                                " filters are "}
                                        applied.
                                    </h3>
                                    <Button
                                        variant="info"
                                        size="sm"
                                        disabled={isLoading || isReloading}
                                        onClick={toggleShowFilters}
                                    >
                                        Show Applied Filters
                                    </Button>
                                </div>
                            )}
                            {showFilters && (
                                <QueryParamFilters
                                    multipleFilters
                                    queryParamFilterMetadata={
                                        tabQueryFilterData.queryParamFilterMetadata
                                    }
                                    queryParamFilterOptions={
                                        tabQueryFilterData.queryParamFilterOptions
                                    }
                                    isLoading={isLoading}
                                    appliedFilters={appliedFilters}
                                    appliedFilterRows={appliedFilterRows}
                                    setAppliedFilters={setAppliedFilters}
                                    setAppliedFilterRows={setAppliedFilterRows}
                                />
                            )}
                            <hr />
                        </div>
                        <div className="mt-4">
                            <TopUpLogsTable
                                limit={limit}
                                skip={skip}
                                topUpLogList={topUpLogs}
                                totalCount={topUpLogsCount}
                                isLoading={isLoading || reloadAfterTopUp}
                                appliedFilters={appliedFilters}
                                setLimit={setLimit}
                                setSkip={setSkip}
                                loadTopUpLogs={loadTopUpLogs}
                            />
                        </div>
                        {showTopUp && (
                            <TopUpReward
                                showModal={showTopUp}
                                hideModal={onHideTopUp}
                                rewardId={rewardId}
                                onReloadAfterTopUp={onAfterToppingUp}
                            />
                        )}
                    </div>
                }
            />
        </div>
    );
};

TopUpLogs.defaultProps = {
    rewardId: "",
    reloadAfterTopUp: false,
    setRewardStatData: () => {},
    setIsReloadafterTopUp: () => {},
};

TopUpLogs.propTypes = {
    rewardId: PropTypes.string,
    reloadAfterTopUp: PropTypes.bool,
    setRewardStatData: PropTypes.func,
    setIsReloadafterTopUp: PropTypes.func,
};

export default TopUpLogs;
