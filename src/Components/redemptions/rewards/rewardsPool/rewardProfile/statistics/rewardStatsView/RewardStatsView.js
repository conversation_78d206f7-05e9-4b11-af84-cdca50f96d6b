import React from "react";
import { Card, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faAward } from "FaICIconMap";

import "./RewardStatsView.scss"

const RewardStatsView =({ 
    rewardStatistic, 
    loadingStats, 
    rewardStatQuantity, 
    rewardStatColor 
})=>{
    return (
        <Card className="reward-stats-view">
            <Card.Body>
                <div className="reward-stats-view-container">
                    <div className="container-left">
                        <div className="color-icon-div" style={{ backgroundColor:`${rewardStatColor}`}}>
                            <IcIcon className="mr-2" size="lg" icon={faAward} />
                        </div>
                        <div>
                            <span className="mr-2">{rewardStatistic}</span>
                        </div>
                    </div>
                    <div>
                        <span className="mr-2"> {
                            loadingStats ?
                                <small>Loading...</small>
                            : rewardStatQuantity
                        }</span>
                    </div>
                </div>
            </Card.Body>
        </Card>
    );

}

export default RewardStatsView;
