import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Button, Form, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext } from "Contexts";
import { MerchantLocationStatusObj, RewardPickUp } from "Data";
import { updateRewardById } from "Services";
import { isEmptyObject, isEqualObjects } from "Utils";
import { objectToKeyValueArray } from "Components/redemptions/rewards/util/RewardUtility";

const RewardPickUpOptions = objectToKeyValueArray(RewardPickUp, false);

const EditPickUpLocations = ({ show, onHide, currentDetails, rewardId }) => {
    const { merchants, merchantLocations } = useContext(DataContext);
    const [isUpdating, setIsUpdating] = useState(false);
    const [allowAllClaimLocations, setAllowAllClaimLocations] = useState();
    const [rewardPickup, setRewardPickUp] = useState("");
    const [currectSelected, setCurrentSelected] = useState([]);
    const [currentLocations, setCurrentLocations] = useState([]);
    const [pickUpLocations, setPickUpLocations] = useState([]);

    const allPickupLocations = useMemo(() => {
        const merchantsIdNameMap = merchants.reduce((result, merchant) => {
            result[merchant?._id] = merchant?.merchantName;
            return result;
        }, {});

        const allLocationsObj = Object.values(merchantLocations).reduce(
            (result, location) => {
                return { ...result, ...location };
            },
            {}
        );

        return Object.values(allLocationsObj)
            .filter(
                (item) =>
                    item?.status === MerchantLocationStatusObj.ACTIVE &&
                    item?.isPickupLocation
            )
            .map((locObj) => ({
                value: locObj._id,
                merchantName: merchantsIdNameMap[locObj?.merchantId],
                name: locObj.locationName,
            }));
    }, [merchantLocations, merchants]);

    const isLocationsEqual = useMemo(
        () =>
            pickUpLocations.length !== 0 && currentLocations.length !== 0
                ? isEqualObjects(currentLocations, pickUpLocations)
                : false,
        [currentLocations, pickUpLocations]
    );

    const onChangeRewardPickUp = useCallback(
        (e) => {
            setRewardPickUp(e.target.value);
            if (e.target.value === RewardPickUp.SELECTED_LOCATIONS) {
                setAllowAllClaimLocations(false);
            } else {
                setAllowAllClaimLocations(true);
            }
        },
        [setRewardPickUp]
    );
    const onChangePickUpLocation = useCallback(
        (e) => setPickUpLocations(e),
        []
    );

    const onUpdatePickupLocations = useCallback(
        async (e) => {
            e.preventDefault();
            let pickUpLocationsPayload = {};

            try {
                if (allowAllClaimLocations) {
                    pickUpLocationsPayload = {
                        rewardMetadata: { allowAllClaimLocations },
                    };
                } else {
                    pickUpLocationsPayload = {
                        rewardMetadata: {
                            allowAllClaimLocations,
                            claimLocations: pickUpLocations.map(
                                (location) => location.value
                            ),
                        },
                    };
                }

                setIsUpdating(true);
                const updatedReward = await updateRewardById(
                    rewardId,
                    pickUpLocationsPayload
                );
                setIsUpdating(false);
                toast.success("Successfully updated pickup locations.");
                onHide(null, updatedReward);
            } catch (e) {
                setIsUpdating(false);
                toast.error(
                    <div>
                        Failed to update pickup locations!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later"}
                    </div>
                );
            }
        },
        [
            rewardId,
            allowAllClaimLocations,
            pickUpLocations,
            onHide,
            setIsUpdating,
        ]
    );

    useEffect(() => {
        if (!isEmptyObject(currentDetails)) {
            setCurrentSelected(currentDetails.rewardMetadata.claimLocations);
            setAllowAllClaimLocations(
                currentDetails.rewardMetadata.allowAllClaimLocations
            );
            if (currentDetails.rewardMetadata.allowAllClaimLocations) {
                setRewardPickUp(RewardPickUp.ALL_LOCATIONS);
            } else {
                setRewardPickUp(RewardPickUp.SELECTED_LOCATIONS);
            }
        }
    }, [currentDetails]);

    useEffect(() => {
        if (currectSelected.length !== 0 && allPickupLocations.length !== 0) {
            const selectedPickUpLocations = currectSelected.map(
                (selected) =>
                    allPickupLocations.find(
                        (fetched) => fetched.value === selected
                    ) || {}
            );
            const existingSelectedLocations = selectedPickUpLocations.filter(
                (selected) => Object.keys(selected).length !== 0
            );

            setCurrentLocations(existingSelectedLocations);
            setPickUpLocations(existingSelectedLocations);
        }
    }, [allPickupLocations, currectSelected]);

    return (
        <Modal show={show} onHide={isUpdating ? () => {} : onHide} centered>
            <Modal.Header closeButton={!isUpdating}>
                <Modal.Title>Edit Pickup Locations</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form.Group className="input-group mt-3">
                    {RewardPickUpOptions.map((option) => (
                        <Form.Check
                            key={option.key}
                            className="rounded-0 input-check mr-3"
                            custom
                            checked={rewardPickup === option.key}
                            onChange={onChangeRewardPickUp}
                            value={option.key}
                            id={option.key}
                            label={option.value}
                            name="rewardPickup"
                            type="radio"
                            disabled={isUpdating}
                            required
                        />
                    ))}
                </Form.Group>
                {rewardPickup === RewardPickUp.SELECTED_LOCATIONS && (
                    <Form.Group>
                        <Form.Label className="d-flex align-items-center">
                            Pickup Locations
                            <div className="ml-1 text-danger">*</div>
                        </Form.Label>
                        <div className="w-100 d-flex">
                            <div className="w-100">
                                <Form.Select
                                    id="basic-typeahead-multi"
                                    labelKey="name"
                                    onChange={onChangePickUpLocation}
                                    options={allPickupLocations}
                                    selected={
                                        pickUpLocations[0] &&
                                        Object.keys(pickUpLocations[0])
                                            .length === 0
                                            ? []
                                            : pickUpLocations
                                    }
                                    placeholder={
                                        allPickupLocations.length === 0
                                            ? "No pickup locations found."
                                            : "Select pickup locations..."
                                    }
                                    disabled={
                                        (allPickupLocations.length === 0 &&
                                            pickUpLocations.length === 0) ||
                                        isUpdating
                                    }
                                    groupBy="merchantName"
                                    multiple
                                    clearButton
                                />
                            </div>
                        </div>
                    </Form.Group>
                )}
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={onHide}
                    disabled={isUpdating}
                >
                    Cancel
                </Button>
                <Button
                    size="sm"
                    variant="primary"
                    disabled={
                        isUpdating ||
                        (rewardPickup === RewardPickUp.SELECTED_LOCATIONS
                            ? isLocationsEqual &&
                                allowAllClaimLocations ===
                                    currentDetails?.rewardMetadata
                                        ?.allowAllClaimLocations
                            : allowAllClaimLocations ===
                                currentDetails?.rewardMetadata
                                    ?.allowAllClaimLocations) ||
                        pickUpLocations.length === 0
                    }
                    onClick={onUpdatePickupLocations}
                >
                    {isUpdating ? "Updating..." : "Update"}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

EditPickUpLocations.propTypes = {
    /**
     * Show edit view
     */
    show: PropTypes.bool.isRequired,
    /**
     * Callback on close
     */
    onHide: PropTypes.func.isRequired,
    /**
     * Current known data
     */
    currentDetails: PropTypes.object,
    /**
     * Reward Id
     */
    rewardId: PropTypes.string.isRequired,
};

export default EditPickUpLocations;
