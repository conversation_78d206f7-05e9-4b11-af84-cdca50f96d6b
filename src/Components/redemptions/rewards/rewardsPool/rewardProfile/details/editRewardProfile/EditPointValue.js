import React, { useCallback, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Button, Form, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { toast } from "react-toastify";
import { isEmptyObject } from "Utils";
import { updateRewardById } from "Services";

const EditPointValue = ({ show, onHide, currentDetails, rewardId }) => {
    const [validated, setValidated] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);
    const [pointValue, setPointValue] = useState();

    const onChangePointValue = useCallback(e => setPointValue(parseInt(e.target.value)), []);

    const onSubmit = useCallback(async e => {
        e.preventDefault();
        if (e.target.checkValidity()) {
            setIsUpdating(true);
            try {
                const pointValuePayload = { pointsStatic: pointValue };
                
                const updatedReward = await updateRewardById(rewardId, pointValuePayload);
                toast.success("Successfully updated point value.");
                setIsUpdating(false);
                onHide(null, updatedReward);              
            } catch (e) {
                setIsUpdating(false);
                toast.error("Could not update point value! Please try again.");
            }
        } else {
            setValidated(true);
        }
    }, [rewardId, pointValue, onHide, setIsUpdating, setValidated]);

    useEffect(() => {
        if (!isEmptyObject(currentDetails)) {
            setPointValue(currentDetails.pointValue);
        }
    }, [currentDetails]);

    return (
        <Modal show={show} onHide={isUpdating ? () => {} : onHide} centered>
          <Modal.Header closeButton={!isUpdating}>
            <Modal.Title>Edit Point Value</Modal.Title>
          </Modal.Header>
          <Form onSubmit={onSubmit} validated={validated} noValidate>
            <Modal.Body>
                <Form.Group>
                    <Form.Control
                        type="number"
                        required
                        value={pointValue}
                        onChange={onChangePointValue}
                        minLength={1}
                        placeholder="Enter Point Value"
                        disabled={isUpdating}
                    />
                </Form.Group>
            </Modal.Body>
            <Modal.Footer>
                <Button size="sm" variant="outline-primary" onClick={onHide} type="button" disabled={isUpdating}>
                    Cancel
                </Button>
                <Button 
                    size="sm" 
                    variant="primary" 
                    type="submit" 
                    disabled={isUpdating || !pointValue || pointValue === currentDetails.pointValue}
                >
                    {isUpdating ? "Updating..." : "Update"}
                </Button>
            </Modal.Footer>
          </Form>
        </Modal>
      );
};

EditPointValue.propTypes = {
    /**
     * Show edit view
     */
    show: PropTypes.bool.isRequired,
    /**
     * Callback on close
     */
    onHide: PropTypes.func.isRequired,
    /**
     * Current known data
     */
    currentDetails: PropTypes.object,
    /**
     * Reward Id
     */
    rewardId: PropTypes.string.isRequired,
};

export default EditPointValue;