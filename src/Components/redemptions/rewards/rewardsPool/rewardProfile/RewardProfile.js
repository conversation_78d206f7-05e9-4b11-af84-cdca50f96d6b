import React, { use<PERSON>allback, useContext, useEffect, useMemo, useState } from "react";
import { useHistory, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { Button, IcIcon, Heading, Tab, Tabs } from "@shoutout-labs/shoutout-themes-enterprise";
import { faAngleLeftB, faArrowUp, faAward } from "FaICIconMap";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { deleteReward, getRewardById } from "Services";
import BaseLayout from "Layout/BaseLayout";
import { UnauthorizedAccessMessage } from "Components/utils";
import RewardProfileDetails from "./details/RewardProfileDetails";
import RewardStatistics from "./statistics/RewardStatistics";
import ShowDeleteReward from "./details/other/ShowDeleteReward";
import RedemptionLogs from "./tabViews/redemptionLogs/RedemptionLogs";
import TopUpLogs from "./tabViews/topUpLog/TopUpLogs";

import "./RewardProfile.scss";

const TabEventKeys = {
    REDEMPTION_LOG: "redemption-log",
    TOPUP_LOG: "topup-log",
};

const RewardProfile = () => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const { id: rewardId } = useParams();
    const [activeTab, setActiveTab] = useState(TabEventKeys.REDEMPTION_LOG);
    const [isLoading, setIsLoading] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [rewardDetails, setRewardDetails] = useState({});
    const [rewardStatData, setRewardStatData] = useState({
        totalRewards: 0,
        redeemedRewards: 0,
        claimedRewards: 0,
        remainingRewards: 0,
    });
    const [showDeleteReward, setShowDeleteReward] = useState(false);
    const [reloadAfterTopUp, setIsReloadafterTopUp] = useState(false);
    const history = useHistory();

    const loadRewardProfile = useCallback(async (rewardId) => {
        try {
            setIsLoading(true);

            const rewardDetailsResponse = await getRewardById(rewardId);
            const statsData = {
                totalRewards: rewardDetailsResponse.totalCount,
                redeemedRewards: rewardDetailsResponse.usedCount,
                claimedRewards: rewardDetailsResponse.claimedCount,
                remainingRewards:
                    rewardDetailsResponse.totalCount -
                    rewardDetailsResponse.usedCount,
            };

            setRewardDetails(rewardDetailsResponse);
            setRewardStatData(statsData);
        } catch (e) {
            console.error(e);
            toast.error(e.message || "Could not load reward details! Please try again.");
        } finally {
            setIsLoading(false);
        }
    }, [setRewardDetails, setRewardStatData]);

    const onNavigatingBack = useCallback(() => history.push("/redemptions/rewards"), [history]);

    const onShowDeleteReward = useCallback(() => setShowDeleteReward(true), [setShowDeleteReward]);

    const onDeleteReward = useCallback(async () => {
        try {
            setIsDeleting(true);
            await deleteReward(rewardId);
            toast.success("Successfully archived the reward.");
            setShowDeleteReward(false);
            onNavigatingBack();
        } catch (e) {
            console.error(e);
            toast.error(e.message || "Failed to archive the reward! Please try again.");
        } finally {
            setIsDeleting(false);
        }
    }, [rewardId, setIsDeleting, onNavigatingBack]);

    const cancelDeleteReward = useCallback(() => setShowDeleteReward(false), [setShowDeleteReward]);

    const allowViewRedemptionLog = useMemo(() => {
        return isAuthorizedForAction(
            AccessPermissionModuleNames.REWARD,
            AccessPermissionModules[AccessPermissionModuleNames.REWARD].actions
                .ListRewardRedemptionLogs
        );
    }, [isAuthorizedForAction]);

    const allowViewTopupLog = useMemo(() => isAuthorizedForAction(
        AccessPermissionModuleNames.REWARD,
        AccessPermissionModules[AccessPermissionModuleNames.REWARD]
            .actions.ListRewardTopups
    ), [isAuthorizedForAction]);

    useEffect(() => {
        if (rewardId) {
            loadRewardProfile(rewardId);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [rewardId]);

    return (
        <div className="reward-profile">
            <BaseLayout
                topLeft={<Heading text="Reward Profile" />}
                topRight={
                    <div className="d-flex">
                        <Button
                            className="btn shadow-none"
                            variant="link"
                            size="sm"
                            disabled={isLoading}
                            onClick={onNavigatingBack}
                        >
                            <div className="d-flex align-items-center">
                                <IcIcon
                                    icon={faAngleLeftB}
                                    size="lg"
                                    className="mr-1"
                                />
                                Back
                            </div>
                        </Button>
                        {isAuthorizedForAction(
                            AccessPermissionModuleNames.REWARD,
                            AccessPermissionModules[AccessPermissionModuleNames.REWARD]
                                .actions.DeleteReward
                        ) &&
                            <div>
                                <Button
                                    className="my-2 ml-2"
                                    variant="danger"
                                    size="sm"
                                    disabled={isLoading}
                                    onClick={onShowDeleteReward}
                                >
                                    Archive Reward
                                </Button>
                            </div>
                        }
                    </div>
                }
                bottom={
                    <>
                        <RewardProfileDetails
                            rewardId={rewardId}
                            isLoading={isLoading}
                            rewardDetails={rewardDetails}
                            setRewardDetails={setRewardDetails}
                        />

                        <RewardStatistics
                            isLoading={isLoading}
                            reloadAfterTopUp={reloadAfterTopUp}
                            rewardStats={rewardStatData}
                        />

                        <Tabs
                            transition={false}
                            id="noanim-tab-example"
                            activeKey={activeTab}
                            onSelect={setActiveTab}
                            className="mt-3"
                        >
                            <Tab
                                disabled={!allowViewRedemptionLog}
                                eventKey={TabEventKeys.REDEMPTION_LOG}
                                title={
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            className="mr-2"
                                            size="lg"
                                            icon={faAward}
                                        />
                                        <span className="mr-2">
                                            Redemption Log
                                        </span>
                                    </div>
                                }
                            >
                                {allowViewRedemptionLog ?
                                    activeTab === TabEventKeys.REDEMPTION_LOG &&
                                        <RedemptionLogs rewardId={rewardId} />
                                    : <UnauthorizedAccessMessage />
                                }
                            </Tab>
                            <Tab
                                disabled={!allowViewTopupLog}
                                eventKey={TabEventKeys.TOPUP_LOG}
                                title={
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            className="mr-2"
                                            size="lg"
                                            icon={faArrowUp}
                                        />
                                        <span className="mr-2">Top Up Log</span>
                                    </div>
                                }
                            >
                                {allowViewTopupLog ? 
                                    activeTab === TabEventKeys.TOPUP_LOG && 
                                        <TopUpLogs 
                                            rewardId={rewardId}
                                            reloadAfterTopUp={reloadAfterTopUp}
                                            setRewardStatData={setRewardStatData}
                                            setIsReloadafterTopUp={setIsReloadafterTopUp}
                                        />                                    
                                    : <UnauthorizedAccessMessage />
                                }
                            </Tab>
                        </Tabs>

                        <ShowDeleteReward
                            show={showDeleteReward}
                            onHide={cancelDeleteReward}
                            isDeleting={isDeleting}
                            onDeleteReward={onDeleteReward}
                        />
                    </>
                }
            />
        </div>
    );
};

export default RewardProfile;
