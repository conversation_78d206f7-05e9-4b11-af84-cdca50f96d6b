import React, { useCallback, useContext } from "react";
import PropTypes from "prop-types";
import { <PERSON>dal, Button } from "@shoutout-labs/shoutout-themes-enterprise";
import { TopUpMethod } from "Data";
import { RewardPoolContext } from "../context/RewardPoolContext";
import TopUpView from "../shared/topUpView/TopUpView";

const TopUpReward = ({ 
    showModal, 
    hideModal, 
    rewardId,
    setRewardId, 
    onReloadAfterTopUp,
}) => {
    const { 
        topUpOption,
        quantity,
        fileInfo,
        setFileInfo,
        couponCodes,
        addCouponCode,
        removeCouponCode,
        setCouponCode,
        setAttributeValue, 
        toppingUp,
        reset,
        onTopUpReward,
        resetTopUp
    } = useContext(RewardPoolContext);

    const onHideTopUpReward = useCallback(() => {
        reset();
        setRewardId("");
        hideModal();
    }, [reset, setRewardId, hideModal]);

    const onChangeHandler  = useCallback(e => setAttributeValue(e), [setAttributeValue]);

    const onChangeTopUpOption = useCallback(e => {
        resetTopUp();
        setAttributeValue(e);
    }, [resetTopUp, setAttributeValue]);

    const onSetFile = useCallback(file => {
        if(file[0]) {
            setFileInfo(file[0]);
        }
        else {
            setFileInfo(""); 
        }
    }, [setFileInfo]);

    const onRewardTopUp = useCallback(async () => {
        const toppedUp = await onTopUpReward(rewardId);  
        if (toppedUp) {
            onReloadAfterTopUp();          
        }
    }, [rewardId, onTopUpReward, onReloadAfterTopUp]);
    
    return (
        <Modal 
            show={showModal} 
            onHide={toppingUp ? () => {} : onHideTopUpReward} 
            size="md"
            centered
        >
            <Modal.Header closeButton={!toppingUp}>
                <Modal.Title>Top Up Reward</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <TopUpView 
                    toppingUp={toppingUp}
                    topUpOption={topUpOption}
                    quantity={quantity}
                    fileInfo={fileInfo}
                    setFileInfo={setFileInfo}
                    onSetFile={onSetFile}
                    couponCodes={couponCodes}
                    addCouponCode={addCouponCode}
                    removeCouponCode={removeCouponCode}
                    setCouponCode={setCouponCode}
                    onChangeHandler={onChangeHandler}
                    onChangeTopUpOption={onChangeTopUpOption}
                />
            </Modal.Body>
            <Modal.Footer>
                <Button 
                    size="sm" 
                    variant="outline-primary" 
                    disabled={toppingUp}
                    onClick={onHideTopUpReward} 
                >
                    Cancel
                </Button>
                <Button 
                    size="sm" 
                    variant="primary" 
                    disabled={
                        toppingUp || 
                        !topUpOption ||
                        (topUpOption === TopUpMethod.GENERATED.methodValue && (isNaN(quantity) || quantity === 0)) || 
                        (topUpOption === TopUpMethod.UPLOAD.methodValue && !fileInfo) || 
                        (topUpOption === TopUpMethod.MANUAL.methodValue && !(couponCodes.every(item => item.code !== "")))
                    } 
                    onClick={onRewardTopUp}
                > 
                    {toppingUp ? "Topping Up Reward..." : "Top Up Reward"} 
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

TopUpReward.propTypes = {
    showModal: PropTypes.bool.isRequired,
    hideModal: PropTypes.func.isRequired,
    rewardId: PropTypes.string.isRequired,
    loadRewards: PropTypes.func,
};

export default TopUpReward;
