import React, { useContext, useState } from "react";
import { Heading, Tab, Tabs } from "@shoutout-labs/shoutout-themes-enterprise";
import {
    RewardsHeaderTabs,
    AccessPermissionModules,
    AccessPermissionModuleNames,
} from "Data";
import BaseLayout from "Layout/BaseLayout";
import RewardLogisticsPage from "./rewardLogistics/RewardLogisticsPage";
import RewardPoolPage from "./rewardsPool/RewardPoolPage";

import { UserContext } from "Contexts";
import { UnauthorizedAccessMessage } from "Components/utils";
import "./Rewards.scss";

const Rewards = () => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [tabIsLoading, setTabIsLoading] = useState(false);
    const [tab, setTab] = useState(RewardsHeaderTabs.REWARD_POOL);

    return (
        <div className="rewards">
            <BaseLayout
                topLeft={<Heading text="Rewards" />}
                bottom={
                    <Tabs
                        id="noanim-selectedTab-example"
                        className="border-solid-bottom"
                        onSelect={setTab}
                        activeKey={tab}
                    >
                        <Tab
                            eventKey={RewardsHeaderTabs.REWARD_POOL}
                            title="Reward Pool"
                            disabled={tabIsLoading}
                        >
                            {tab === RewardsHeaderTabs.REWARD_POOL && (
                                <RewardPoolPage
                                    tab={tab}
                                    setTabIsLoading={setTabIsLoading}
                                />
                            )}
                        </Tab>
                        <Tab
                            eventKey={RewardsHeaderTabs.REWARD_LOGISTICS}
                            title="Reward Logistics"
                            disabled={
                                tabIsLoading ||
                                !isAuthorizedForAction(
                                    AccessPermissionModuleNames.REWARD,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.REWARD
                                    ].actions.ListRewardRedemptionLogs
                                )
                            }
                        >
                            {tab === RewardsHeaderTabs.REWARD_LOGISTICS &&
                                (isAuthorizedForAction(
                                    AccessPermissionModuleNames.REWARD,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.REWARD
                                    ].actions.ListRewardRedemptionLogs
                                ) ? (
                                    <RewardLogisticsPage
                                        tab={tab}
                                        tabIsLoading={tabIsLoading}
                                        setTabIsLoading={setTabIsLoading}
                                    />
                                ) : (
                                    <UnauthorizedAccessMessage />
                                ))}
                        </Tab>
                    </Tabs>
                }
            />
        </div>
    );
};

export default Rewards;
