import React from "react";
import {
    But<PERSON>,
    IcIcon,
    FormSearch,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faSync } from "FaICIconMap";
import { RewardGenerationJobStatus } from "Data";
import RewardLogisticsActionButtons from "../actionButtons/RewardLogisticsActionButtons";

const RewardLogisticsTopPanel = ({
    selectedTab,
    isLoading,
    searchText,
    onSearch,
    isReloading,
    onReloadData,
    toggleShowFilters,
    selectedItems,
    resetAfterStartProcess,
}) => {
    return (
        <div className="mt-4 w-100">
            <div className=" d-flex flex-row justify-content-between mb-4">
                <div className="d-flex flex-row align-items-center">
                    {selectedTab !== RewardGenerationJobStatus.PENDING && (
                        <div className="custom-width mr-2">
                            <FormSearch
                                disabled={isLoading || isReloading}
                                placeholder="Search"
                                selected={searchText}
                                onChange={onSearch}
                                id="search-reward-logistics"
                            />
                        </div>
                    )}
                    <Button
                        variant="link"
                        size="sm"
                        disabled={isLoading || isReloading}
                        onClick={onReloadData}
                    >
                        {!isReloading && (
                            <div className="d-flex align-items-center">
                                <IcIcon
                                    size="md"
                                    className="mr-2"
                                    icon={faSync}
                                />
                                Reload Logistics
                            </div>
                        )}
                    </Button>
                    <span style={{ fontSize: "0.9rem" }}>
                        {" "}
                        {isReloading && (
                            <div className="text-primary">Reloading...</div>
                        )}{" "}
                    </span>
                </div>
                <div className="mr-2"></div>
                <RewardLogisticsActionButtons
                    selectedTab={selectedTab}
                    isLoading={isLoading}
                    toggleFilter={toggleShowFilters}
                    selectedItems={selectedItems}
                    resetAfterStartProcess={resetAfterStartProcess}
                />
            </div>
        </div>
    );
};

export default RewardLogisticsTopPanel;
