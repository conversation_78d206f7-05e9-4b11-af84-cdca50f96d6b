import React, { useContext } from "react";
import {
    Button,
    IcIcon,
    ShIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { ShFilter } from "@shoutout-labs/shoutout-themes-enterprise/es/ShoutOUTIcon";
import { faStart } from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    RewardGenerationJobStatus,
    AccessPermissionModuleNames,
    AccessPermissionModules,
} from "Data";
import { useToggle } from "Hooks";
import StartProcessModal from "./StartProcessModal";

const RewardLogisticsActionButtons = ({
    selectedTab,
    isLoading,
    toggleFilter,
    selectedItems,
    resetAfterStartProcess,
}) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [showStartProcessModal, toggleStartProcessModal] = useToggle(false);

    return (
        <div>
            {selectedTab === RewardGenerationJobStatus.PENDING && (
                <div>
                    <Button
                        size="sm"
                        variant="primary"
                        onClick={toggleStartProcessModal}
                        disabled={
                            !isAuthorizedForAction(
                                AccessPermissionModuleNames.REWARD,
                                AccessPermissionModules[
                                    AccessPermissionModuleNames.REWARD
                                ].actions.CreateRewardDistributionJob
                            ) ||
                            isLoading ||
                            selectedItems.length === 0
                        }
                    >
                        <div className="d-flex align-items-center">
                            <IcIcon size="lg" className="mr-2" icon={faStart} />
                            Start Process
                        </div>
                    </Button>
                    <Button
                        className="ml-3"
                        size="sm"
                        variant="outline-primary"
                        disabled={isLoading}
                        onClick={toggleFilter}
                    >
                        <ShIcon icon={ShFilter} className="py-2 mr-2" />
                        Filter By
                    </Button>
                </div>
            )}

            {showStartProcessModal && (
                <StartProcessModal
                    show={showStartProcessModal}
                    onHide={toggleStartProcessModal}
                    selectedItems={selectedItems}
                    resetAfterStartProcess={resetAfterStartProcess}
                />
            )}
        </div>
    );
};

export default RewardLogisticsActionButtons;
