import React, { useCallback, useMemo } from "react";
import paginationFactory, {
    PaginationProvider,
} from "react-bootstrap-table2-paginator";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import {
    BootstrapTable,
    Form,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    DefaultColumsObject,
    RewardGenerationJobGroups,
    RewardGenerationJobStatus,
} from "Data";
import { toTitleCase } from "Utils";
import RefundPoints from "Components/redemptions/shared/refundPoints/RefundPoints";
import ViewMember from "Components/redemptions/shared/viewMember/ViewMember";
import { BootstrapTableOverlay } from "Components/utils/UtilComponents";
import SizePerPageRenderer from "Components/utils/table/sizePerPageRenderer/SizePerPageRenderer";
import { convertToTableData } from "../../util/RewardUtility";
import ChangeProcess from "../shared/changeProcess/ChangeProcess";

const defaultColumnTemplate = ({ name, icon, ...rest }) => ({
    dataField: name,
    text: (
        <div className="d-flex align-items-center">
            {icon && (
                <IcIcon size="lg" icon={icon} className="mr-2 text-black" />
            )}
            {toTitleCase(name)}
        </div>
    ),
    sort: true,
    ...rest,
});

const NoData = ({ loading, status, selectedGroup }) => {
    if (loading) return null;

    if (status === RewardGenerationJobStatus.PENDING) {
        return <div>No pending rewards found.</div>;
    } else {
        if (selectedGroup === RewardGenerationJobGroups.INDIVIDUAL) {
            switch (status) {
                case RewardGenerationJobStatus.PROCESSING: {
                    return (
                        <div>
                            No processing redemptions found for this batch.
                        </div>
                    );
                }
                case RewardGenerationJobStatus.DISPATCHED: {
                    return (
                        <div>
                            No dispatched redemptions found for this batch.
                        </div>
                    );
                }
                case RewardGenerationJobStatus.COMPLETED: {
                    return (
                        <div>
                            No completed redemptions found for this batch.
                        </div>
                    );
                }
                case RewardGenerationJobStatus.FAILED: {
                    return (
                        <div>No failed redemptions found for this batch.</div>
                    );
                }
                default:
                    return null;
            }
        } else if (selectedGroup === RewardGenerationJobGroups.BATCHES) {
            switch (status) {
                case RewardGenerationJobStatus.PROCESSING: {
                    return <div>No processing reward batches found.</div>;
                }
                case RewardGenerationJobStatus.DISPATCHED: {
                    return <div>No dispatched reward batches found.</div>;
                }
                case RewardGenerationJobStatus.COMPLETED: {
                    return <div>No completed reward batches found.</div>;
                }
                case RewardGenerationJobStatus.FAILED: {
                    return <div>No failed reward batches found.</div>;
                }
                default:
                    return null;
            }
        }
    }
};

const defaultSkip = 1;

const RewardLogisticsTable = ({
    rewardDistributionList,
    redemptionLogsList,
    totalCount,
    loadRedemptionLogs,
    isLoading,
    rewardStatus,
    group,
    limit,
    skip,
    setLimit,
    setSkip,
    appliedFilters,
    searchText,
    loadRewardLogistics,
    setSelectedBatchId,
    setSelectedBatch,
    setBatchView,
    selectedItems,
    setSelectedItems,
    changeBatchProcess,
    batchProcessDetails,
    onBatchProcessStatusChange,
    onHideBatchProcessStatusChange,
    changeProcess,
    processDetails,
    onProcessStatusChange,
    onHideProcessStatusChange,
    batchRow,
    isExporting,
    onExportBatch,
    refundPointsModal,
    failedRedemption,
    onRefundPoints,
    onHideRefundPoints,
    viewMemberDetails,
    memberId,
    onShowMemberDetails,
    onHideMemberDetails,
    isAllowedUpdateBatchProcess,
}) => {
    const columns = useMemo(() => {
        let columns = [];

        if (rewardStatus === RewardGenerationJobStatus.PENDING) {
            DefaultColumsObject[rewardStatus].forEach((item) => {
                columns.push(defaultColumnTemplate(item));
            });

            columns.push({
                dataField: "",
                sort: false,
                headerStyle: { width: "12%" },
            });

            columns = columns.map((column) =>
                column.dataField === "status"
                    ? { ...column, headerStyle: { width: "10%" } }
                    : column
            );
        } else {
            DefaultColumsObject[rewardStatus][group].forEach((item) => {
                columns.push(defaultColumnTemplate(item));
            });

            if (group === RewardGenerationJobGroups.INDIVIDUAL) {
                switch (rewardStatus) {
                    case RewardGenerationJobStatus.PROCESSING:
                    case RewardGenerationJobStatus.DISPATCHED:
                        columns.push({
                            dataField: "",
                            sort: false,
                            headerStyle: { width: "25%" },
                        });
                        break;
                    case RewardGenerationJobStatus.COMPLETED:
                        columns.push({
                            dataField: "",
                            sort: false,
                            headerStyle: { width: "15%" },
                        });
                        break;
                    case RewardGenerationJobStatus.FAILED:
                        columns.push({
                            dataField: "",
                            sort: false,
                            headerStyle: { width: "20%" },
                        });
                        break;
                    default:
                        return columns;
                }

                columns = columns.map((column) =>
                    column.dataField === "status"
                        ? { ...column, headerStyle: { width: "10%" } }
                        : column
                );
            } else if (group === RewardGenerationJobGroups.BATCHES) {
                switch (rewardStatus) {
                    case RewardGenerationJobStatus.PROCESSING:
                    case RewardGenerationJobStatus.DISPATCHED:
                    case RewardGenerationJobStatus.FAILED:
                        columns.push({
                            dataField: "",
                            sort: false,
                            headerStyle: { width: "25%" },
                        });

                        columns = columns.map((column) =>
                            column.dataField === "status"
                                ? { ...column, headerStyle: { width: "15%" } }
                                : column
                        );
                        break;
                    case RewardGenerationJobStatus.COMPLETED:
                        columns.push({
                            dataField: "",
                            sort: false,
                            headerStyle: { width: "15%" },
                        });
                        break;
                    default:
                        return columns;
                }
            }
        }

        return columns;
    }, [rewardStatus, group]);

    const rewardRedemptionActions = useMemo(() => {
        const onBatchAction = (e) => {
            const selectedBatchObj = {
                _id: e.currentTarget.id,
                selectAction: e.currentTarget.name,
            };
            e.stopPropagation();
            onBatchProcessStatusChange(selectedBatchObj);
        };

        return {
            distributionJobActionBtn: onBatchAction,
            batchRow: batchRow,
            isExporting: isExporting,
            exportActionBtn: onExportBatch,
            redemptionJobActionBtn: onProcessStatusChange,
            refundPointsBtn: onRefundPoints,
            viewMemberBtn: onShowMemberDetails,
        };
    }, [
        batchRow,
        isExporting,
        onBatchProcessStatusChange,
        onExportBatch,
        onProcessStatusChange,
        onRefundPoints,
        onShowMemberDetails,
    ]);

    const data = useMemo(() => {
        if (rewardStatus === RewardGenerationJobStatus.PENDING) {
            const filteredRedemptionsList = redemptionLogsList.filter(
                (redemption) =>
                    redemption.processingStatus ===
                    RewardGenerationJobStatus.PENDING
            );
            return convertToTableData(
                rewardStatus,
                filteredRedemptionsList,
                group,
                rewardRedemptionActions,
                isAllowedUpdateBatchProcess
            );
        } else {
            if (group === RewardGenerationJobGroups.BATCHES) {
                const filteredRewardList = rewardDistributionList.filter(
                    (reward) => reward.status === rewardStatus
                );
                return convertToTableData(
                    rewardStatus,
                    filteredRewardList,
                    group,
                    rewardRedemptionActions,
                    isAllowedUpdateBatchProcess
                );
            } else if (group === RewardGenerationJobGroups.INDIVIDUAL) {
                return convertToTableData(
                    rewardStatus,
                    redemptionLogsList,
                    group,
                    rewardRedemptionActions,
                    isAllowedUpdateBatchProcess
                );
            }
        }
    }, [
        rewardStatus,
        rewardDistributionList,
        redemptionLogsList,
        group,
        rewardRedemptionActions,
        isAllowedUpdateBatchProcess,
    ]);

    const loadDataDynamically = useCallback(
        ({ limit, skip }) => {
            if (group === RewardGenerationJobGroups.INDIVIDUAL) {
                loadRedemptionLogs({ limit, skip }, appliedFilters);
            } else {
                loadRewardLogistics({ limit, skip }, searchText);
            }
        },
        [
            group,
            appliedFilters,
            searchText,
            loadRedemptionLogs,
            loadRewardLogistics,
        ]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadDataDynamically({ limit, skip: newSkip });
        },
        [limit, setSkip, loadDataDynamically]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadDataDynamically({ limit: newLimit, skip: defaultSkip });
        },
        [setLimit, setSkip, loadDataDynamically]
    );

    const onRowClick = useCallback(
        (e, rowData) => {
            setSelectedBatchId(rowData._id);
            setSelectedBatch(rowData);
            setBatchView(false);
        },
        [setSelectedBatchId, setSelectedBatch, setBatchView]
    );

    const rowClick = useCallback((e, row) => onRowClick(e, row), [onRowClick]);

    const tableRowEvents = { onClick: rowClick };

    const onSelectOneItem = useCallback(
        (row, isSelect) => {
            if (isSelect) {
                setSelectedItems([...selectedItems, row._id]);
            } else {
                setSelectedItems(
                    selectedItems.filter((item) => item !== row._id)
                );
            }
        },
        [selectedItems, setSelectedItems]
    );

    const onSelectAllItem = useCallback(
        (rows, isSelect) => {
            if (isSelect) {
                setSelectedItems(rows.map((row) => row._id));
            } else {
                setSelectedItems([]);
            }
        },
        [setSelectedItems]
    );

    const selectRow = {
        mode: "checkbox",
        clickToSelect: true,
        selectionRenderer: (props) => <Form.Check {...props} />,
        selectionHeaderRenderer: ({ indeterminate, ...rest }) => (
            <Form.Check
                {...rest}
                className={`td-header ${indeterminate ? "indet" : ""}`}
            />
        ),
        onSelect: (row, isSelect) => onSelectOneItem(row, isSelect),
        onSelectAll: (isSelect, rows) => onSelectAllItem(rows, isSelect),
    };

    const options = {
        page: skip,
        sizePerPageRenderer: SizePerPageRenderer,
        sizePerPage: limit,
        totalSize: totalCount,
        pageStartIndex: 1,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageList: [
            { text: "10", value: 10 },
            { text: "25", value: 25 },
            { text: "50", value: 50 },
        ],
        onPageChange: onChangePagination,
        onSizePerPageChange: onChangePageSize,
    };

    return (
        <>
            <div className="h-100 reward-logistsics-table">
                <PaginationProvider
                    pagination={paginationFactory(options)}
                    keyField="_id"
                    columns={columns}
                    data={data}
                >
                    {({ paginationTableProps }) => (
                        <ToolkitProvider
                            keyField="_id"
                            data={data}
                            columns={columns}
                            columnToggle
                        >
                            {(props) => (
                                <div>
                                    {rewardStatus ===
                                    RewardGenerationJobStatus.PENDING ? (
                                        <BootstrapTable
                                            {...paginationTableProps}
                                            remote={{
                                                search: true,
                                                pagination: true,
                                            }}
                                            loading={isLoading}
                                            rowEvents={
                                                rewardStatus !==
                                                    RewardGenerationJobStatus.PENDING &&
                                                group !==
                                                    RewardGenerationJobGroups.INDIVIDUAL &&
                                                tableRowEvents
                                            }
                                            noDataIndication={() => (
                                                <NoData
                                                    loading={isLoading}
                                                    status={rewardStatus}
                                                    selectedGroup={group}
                                                />
                                            )}
                                            overlay={BootstrapTableOverlay}
                                            {...props.baseProps}
                                            selectRow={selectRow}
                                        />
                                    ) : (
                                        <BootstrapTable
                                            {...paginationTableProps}
                                            remote={{
                                                search: true,
                                                pagination: true,
                                            }}
                                            loading={isLoading}
                                            rowEvents={
                                                rewardStatus !==
                                                    RewardGenerationJobStatus.PENDING &&
                                                group !==
                                                    RewardGenerationJobGroups.INDIVIDUAL &&
                                                tableRowEvents
                                            }
                                            noDataIndication={() => (
                                                <NoData
                                                    loading={isLoading}
                                                    status={rewardStatus}
                                                    selectedGroup={group}
                                                />
                                            )}
                                            overlay={BootstrapTableOverlay}
                                            {...props.baseProps}
                                        />
                                    )}
                                </div>
                            )}
                        </ToolkitProvider>
                    )}
                </PaginationProvider>
            </div>
            {processDetails && changeProcess && (
                <ChangeProcess
                    show={changeProcess}
                    limit={limit}
                    skip={skip}
                    processDetails={processDetails}
                    isNavigateBack={false}
                    onHide={onHideProcessStatusChange}
                    loadLogistics={loadDataDynamically}
                />
            )}
            {batchProcessDetails && changeBatchProcess && (
                <ChangeProcess
                    show={changeBatchProcess}
                    limit={limit}
                    skip={skip}
                    processDetails={batchProcessDetails}
                    isNavigateBack={false}
                    onHide={onHideBatchProcessStatusChange}
                    loadLogistics={loadDataDynamically}
                />
            )}
            {refundPointsModal && (
                <RefundPoints
                    show={refundPointsModal}
                    onHide={onHideRefundPoints}
                    refundItemDetails={failedRedemption}
                />
            )}
            {viewMemberDetails && (
                <ViewMember
                    show={viewMemberDetails}
                    onHide={onHideMemberDetails}
                    memberId={memberId}
                />
            )}
        </>
    );
};

export default RewardLogisticsTable;
