import React, { useCallback, useContext, useState,useMemo } from "react";
import { Button, Col, Form, Modal, Row, InputGroup, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext, UserContext } from "../../../../Contexts";
import { useToggle } from "../../../../Hooks";
import { getCardByCardNumber,transferPoints} from "../../../../Services";
import { toast } from "react-toastify";
import { CardStatus, MerchantLocationStatus } from "../../../../Data";
import { fsMinusCircle,faPlusCircle } from "../../../../FaICIconMap";

const SharePoints=({ show, onHide, memberId })=>{
    const {selectedRegion,organization} = useContext(UserContext);
    const { merchants, merchantLocations } = useContext(DataContext);
    const [showSharePoints, setShowSharePoints] = useToggle(false);
    const [isSearching, setIsSearching] = useToggle(false);
    const [validated, setValidated] = useState(false);
    const [validatedSharePoints, setValidatedSharePoints] = useState(false);
    const [loyaltyCard, setLoyaltyCard] = useState("");
    const [memberInfo, setMemberInfo] = useState({});
    const [isSharing, setIsSharing] = useState(false);
    const [note, setNote] = useState("");
    const [selectedMerchant, setSelectedMerchant] = useState("");
    const [selectedMerchantLocation, setSelectedMerchantLocation] = useState("");
    const [pointsAmount, setPointsAmount] = useState(
        (selectedRegion &&
            selectedRegion?.pointConfiguration?.minPointRedemptionAmount) ||
        1
    );

    const locations = useMemo(() => {
        if (selectedMerchant) {
            const locations = merchantLocations[selectedMerchant] ? Object.values(merchantLocations[selectedMerchant]) : [];
            const earnLocations = locations.filter(location => location.options?.earn && location.status === MerchantLocationStatus[1].value&& location?.isShownInCreateTransactions);
            return earnLocations || [];
        }
        return [];
    }, [merchantLocations, selectedMerchant]);


    const onChangeNote = useCallback(
        (e) => {
            setNote(e.target.value);
        },
        [setNote]
    );


    const onSelectMerchant = useCallback(
        (e) => {
            setSelectedMerchant(e.target.value);
        },
        [setSelectedMerchant]
    );


    const onSelectMerchantLocation = useCallback(
        (e) => {
            setSelectedMerchantLocation(e.target.value);
        },
        [setSelectedMerchantLocation]
    );


    const onChangeLoyaltyCard = useCallback(e => setLoyaltyCard(e.target.value.replace(/[^\dA-Z]/g, '').replace(/(.{4})/g, '$1 ').trim()), []);


    const searchCard = useCallback(async e => {
        e.preventDefault();
        if (e.target.checkValidity()) {
            try {
                setIsSearching();
                const card = await getCardByCardNumber(loyaltyCard.replace(/\s/g, ''), selectedRegion._id);
                if(card&&card.items.length!==0&&card.items[0].status===CardStatus.ASSIGNED){
                    setMemberInfo(card.items[0])
                    setShowSharePoints(true);
                }else {
                    toast.error("Only assigned cards can be used, Please pick a valid assigned card.");
                }
                setIsSearching();
            } catch (error) {
                console.error(error);
                toast.error(error.message||"Search failed! Please check the card number and try again.");
                setIsSearching();
            }
        }else {
            setValidated(true);
        }
    }, [setShowSharePoints,loyaltyCard, setValidated,setIsSearching, selectedRegion]);


    const minimumPointsToShare =
        useMemo(
            () =>
                selectedRegion &&
                selectedRegion?.pointConfiguration?.minPointRedemptionAmount,
            [selectedRegion]
        ) || 1;


    const maximumPointsToShare =
        useMemo(
            () =>
                selectedRegion &&
                selectedRegion?.pointConfiguration?.maxPointRedemptionAmount,
            [selectedRegion]
        ) || 100;


    const onChangePointAmount = useCallback(
        (e) =>
            setPointsAmount(
                e.target.value ? Number(e.target.value) : minimumPointsToShare
            ),
        [setPointsAmount,minimumPointsToShare]
    );


    const onAddPoints = useCallback(() => {
        if (
            Number(pointsAmount) + Number(minimumPointsToShare) >
            maximumPointsToShare
        ) {
            toast.warning(
                `Maximum points that can be shared per transaction is ${maximumPointsToShare}`
            );
        } else {
            setPointsAmount(Number(pointsAmount) + Number(minimumPointsToShare));
        }
    }, [minimumPointsToShare, maximumPointsToShare, pointsAmount, setPointsAmount]);

    const onDeductPoints = useCallback(() => {
        if (Number(pointsAmount) === Number(minimumPointsToShare)) {
            setPointsAmount(minimumPointsToShare);
        } else {
            setPointsAmount(Number(pointsAmount) - Number(minimumPointsToShare));
        }
    }, [minimumPointsToShare, pointsAmount, setPointsAmount]);


    const onClickSharePoints = useCallback(async e => {
        e.preventDefault();
        if (e.target.checkValidity()&&(pointsAmount>=minimumPointsToShare)) {
            try {
                setIsSharing(true);
                await transferPoints({
                    memberId,
                    charityMemberId:memberInfo.member._id,
                    pointsAmount,
                    merchantId: selectedMerchant,
                    merchantLocationId: selectedMerchantLocation,
                    notes: note,
                });
                toast.success("Successfully transferred points");
                setIsSharing(false);
                onHide();
            } catch (error) {
                setIsSharing(false);
                console.error(error);
                toast.error(error.message || "Failed to share points! Please try again.");
            }
        }else {
            setValidatedSharePoints(true);
        }
    }, [minimumPointsToShare,onHide,memberId,setIsSharing,setValidatedSharePoints, memberInfo, note, pointsAmount, selectedMerchant, selectedMerchantLocation]);


    return(
        <Modal centered show={show} onHide={onHide}>
            <Modal.Header closeButton>
                <Modal.Title>Points Sharing</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form
                    onSubmit={!showSharePoints?searchCard:onClickSharePoints} validated={!showSharePoints?validated:validatedSharePoints}
                    noValidate
                >
                    {!showSharePoints?<>
                        <Form.Group>
                            <Form.Label className="d-flex align-items-center">
                                Enter Loyalty Card Number
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            <Form.Control
                                type="text"
                                required
                                minLength={organization.configuration?organization.configuration.cardConfiguration.loyaltyCardNumberLength+(Math.round(organization.configuration.cardConfiguration.loyaltyCardNumberLength/4)-1):14}
                                maxLength={organization.configuration?organization.configuration.cardConfiguration.loyaltyCardNumberLength+(Math.round(organization.configuration.cardConfiguration.loyaltyCardNumberLength/4)-1):14}
                                value={loyaltyCard}
                                disabled={isSearching}
                                onChange={onChangeLoyaltyCard}
                                placeholder="Enter Loyalty Card Number"
                            />
                        </Form.Group>
                    </>:<>
                        <div className="d-flex flex-column justify-content-center">
                            <div className="d-flex flex-column align-items-center justify-content-center">
                            <span className="my-1 px-3 py-2">
                                Loyalty Card Number: {memberInfo?.cardNo||"-"}
                            </span>
                                <span className="my-1 px-3 py-2">
                               Loyalty Member Name: {memberInfo?.member?.preferredName||"-"}
                            </span>
                            </div>
                        </div>
                        <Form.Group>
                            <div className="d-flex flex-column mt-3">
                                <Form.Group controlId="merchant">
                                    <Form.Label className="d-flex align-items-center">
                                        Merchant
                                        <div className="ml-1 text-danger">*</div>
                                    </Form.Label>
                                    <Form.Control
                                        name="merchant"
                                        as="select"
                                        value={selectedMerchant}
                                        onChange={onSelectMerchant}
                                        required
                                    >
                                        <option disabled value="">
                                            -- Select Merchant --
                                        </option>
                                        {merchants.map((merchant) => (
                                            <option value={merchant._id} key={merchant._id}>
                                                {merchant.merchantName}
                                            </option>
                                        ))}
                                    </Form.Control>
                                </Form.Group>
                                <Form.Group controlId="merchantLocation">
                                    <Form.Label className="d-flex align-items-center">
                                        Merchant Location
                                        <div className="ml-1 text-danger">*</div>
                                    </Form.Label>
                                    <Form.Control
                                        name="merchantLocation"
                                        as="select"
                                        value={selectedMerchantLocation}
                                        onChange={onSelectMerchantLocation}
                                        required
                                    >
                                        <option disabled value="">
                                            -- Select Location --
                                        </option>
                                        {locations.map((merchantLocation) => (
                                            <option value={merchantLocation._id} key={merchantLocation._id}>
                                                {merchantLocation.locationName}
                                            </option>
                                        ))}
                                    </Form.Control>
                                </Form.Group>

                                <Form.Label>Points
                                    <div className="ml-1 text-danger">*</div>
                                </Form.Label>
                                <InputGroup size="">
                                    <Button
                                        variant="outline-primary"
                                        id="button-addon1"
                                        disabled={isSharing}
                                        onClick={onDeductPoints}
                                        size={'sm'}
                                    >
                                        <IcIcon
                                            icon={fsMinusCircle}
                                            size="sm"
                                            className="mr-1"
                                        />
                                    </Button>
                                    <Form.Control
                                        type="text"
                                        placeholder={pointsAmount}
                                        name="pointAmount"
                                        value={pointsAmount}
                                        className="mx-1"
                                        onChange={onChangePointAmount}
                                        aria-label="Example text with button addon"
                                        aria-describedby="basic-addon1"
                                        isInvalid={validatedSharePoints && (pointsAmount<minimumPointsToShare)}
                                    />

                                    <Button
                                        variant="outline-primary"
                                        id="button-addon2"
                                        onClick={onAddPoints}
                                        disabled={isSharing || pointsAmount === maximumPointsToShare}
                                        size={'sm'}
                                    >
                                        <IcIcon
                                            icon={faPlusCircle}
                                            size="lg"
                                            className="mr-1"
                                        />
                                    </Button>
                                </InputGroup>
                                {(pointsAmount<minimumPointsToShare)&&<p className="mt-1 mx-2 text-danger">{`Minimum Point value must be greater than ${minimumPointsToShare}`}</p>}
                                <Form.Group className="mt-2">
                                    <Form.Label className="d-flex align-items-center">
                                        Note
                                    </Form.Label>
                                    <Form.Control
                                        as="textarea"
                                        rows={10}
                                        value={note}
                                        onChange={onChangeNote}
                                        placeholder="Enter note here.."
                                    />
                                </Form.Group>
                            </div>
                        </Form.Group>
                    </>}
                    <Row>
                        <Col className='d-flex justify-content-end'>
                            <Button
                                size="sm"
                                variant="outline-primary"
                                disabled={isSearching}
                                className="m-1"
                                onClick={onHide}
                            >
                                Cancel
                            </Button>
                            <Button
                                size="sm"
                                variant="primary"
                                className="m-1"
                                disabled={isSearching || loyaltyCard === ""||isSharing}
                                type="submit"
                            >
                                {!showSharePoints?(isSearching ? "Searching..." : "Search"): (isSharing ? "Sharing" : "Share Points")}
                            </Button>
                        </Col>
                    </Row>
                </Form>
            </Modal.Body>
        </Modal>
    )
}

export default SharePoints;
