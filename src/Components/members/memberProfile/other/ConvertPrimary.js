import React, {useCallback, useState } from "react";
import { toast } from "react-toastify";
import {Button, Form, IcIcon, Modal} from "@shoutout-labs/shoutout-themes-enterprise";
import { faCheckCircle } from "FaICIconMap";
import { useToggle } from "Hooks";
import { requestToConvertPrimary, convertToPrimaryAccount } from "Services";

const ConvertPrimary = ({ show, onHide, memberId, loadProfile }) => {
    const [isConverting, toggleIsConverting] = useToggle(false);
    const [isRequesting, toggleIsRequesting] = useToggle(false);
    const [requestToken, setRequestToken] = useState();
    const [otp, setOtp] = useState();

    const onChangeOtp = useCallback(e => setOtp(e.target.value), [setOtp])

    const requestConvertPrimaryToken = useCallback(async () => {
        try{
            toggleIsRequesting();
            const requestPayload = { memberId };
            const convertPrimaryRequestResponse = await requestToConvertPrimary(requestPayload);
            setRequestToken(convertPrimaryRequestResponse.requestToken);
            toast.success("Token request successful. An OTP is being sent to the registered mobile number of this account.");
        } catch(e) {
            console.error(e);
            toast.error(e.message || "Failed to request a token! Please try again.");
        } finally {
            toggleIsRequesting();
        }   
    }, [memberId, toggleIsRequesting, setRequestToken]);

    const onRemoveSecondary = useCallback(async () => {
        try{
            toggleIsConverting();
            if(requestToken) {
                const addSecondaryAccountPayload = { 
                    requestToken: requestToken,
                    otpCode: otp
                };
                await convertToPrimaryAccount(addSecondaryAccountPayload);
                loadProfile();
                onHide();  
                toast.success("Successfully converted to a Primary account.");
            } 
        } catch(e) {
            console.error(e);
            toast.error(e.message || "Failed to convert to a Primary account! Please try again.");
        } finally {
            toggleIsConverting();
        }       
    },[requestToken, otp, onHide, loadProfile, toggleIsConverting]);

    return(
        <Modal show={show} onHide={(isRequesting || isConverting) ? () => {} : onHide} size="md" centered>
            <Modal.Header  closeButton={!(isRequesting || isConverting)}>
                <Modal.Title>Convert To Primary</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {requestToken ?   
                    <small className="d-flex align-items-center text-success mb-2">
                        <IcIcon size="md" className="mr-2" icon={faCheckCircle} />
                        Please enter the OTP sent to the registered mobile number of this account below.
                    </small>           
                    :
                    <p>Do you want to convert this account to a primary account ?</p>                
                }
                {requestToken &&
                    <>
                        <Form.Group controlId="input-otp-group">
                            <Form.Label className="d-flex align-items-center"> 
                                OTP
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            <Form.Control 
                                name="otp" 
                                type="number" 
                                placeholder="Enter OTP" 
                                value={otp} 
                                disabled={isConverting}
                                onChange={onChangeOtp}
                                required 
                            />
                        </Form.Group>                
                    </>
                }
            </Modal.Body>
            <Modal.Footer>
                <Button 
                    size="sm" 
                    variant="outline-primary" 
                    onClick={onHide} 
                    disabled={isRequesting || isConverting}
                >
                    Cancel
                </Button>
                {requestToken ? 
                    <Button 
                        size="sm" 
                        variant="primary"
                        onClick={onRemoveSecondary} 
                        disabled={isConverting || !otp}
                    >
                        {isConverting ? "Converting to Primary..." : "Convert"}
                    </Button>
                    :
                    <Button 
                        size="sm" 
                        variant="primary" 
                        onClick={requestConvertPrimaryToken} 
                        disabled={isRequesting}
                    >
                        {isRequesting ? "Requesting Token..." : "Request Token"}
                    </Button>
                }
            </Modal.Footer>
        </Modal>
    );
}

export default ConvertPrimary;