import React, { useCallback, useContext, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Button, Modal, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext, UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    CardStatus,
    MemberStatus,
    MemberTypes,
} from "Data";
import {
    changeAccountStatus,
    changeCharityAccountStatus,
    addMemberNotes,
} from "Services";

const SuspendAccount = ({
    show,
    memberId,
    profileType,
    onHide,
    loadProfile,
    loadCardData,
    loadSecondaryMembersData,
}) => {
    const { isAuthorizedForAction, selectedRegion } = useContext(UserContext);
    const { loadCharities } = useContext(DataContext);
    const [isSuspending, setIsSuspending] = useState(false);
    const [reason, setReason] = useState("");
    const [showConfirmation, setShowConfirmation] = useState(false);

    const handleSuspendClick = () => {
        setShowConfirmation(true);
    };

    const onClickSuspend = useCallback(async () => {
        const payload = {
            memberId,
            status: MemberStatus.SUSPENDED,
            notes: reason.trim() ?? "No reason provided",
        };

        try {
            setIsSuspending(true);
            setShowConfirmation(false);

            profileType !== MemberTypes.CHARITY
                ? await changeAccountStatus(payload)
                : await changeCharityAccountStatus(payload);

            // * Add a note to the SUSPENDED member's profile.
            if (
                isAuthorizedForAction(
                    AccessPermissionModuleNames.MEMBER,
                    AccessPermissionModules[AccessPermissionModuleNames.MEMBER]
                        .actions.CreateMemberNote
                )
            ) {
                try {
                    const notePayload = {
                        memberId,
                        content: `Suspended Reason: ${
                            reason.trim() ?? "No reason provided"
                        }`,
                    };
                    await addMemberNotes(notePayload);
                } catch (err) {
                    console.error(
                        `Failed to add a note to the ${MemberStatus.SUSPENDED} member's profile`,
                        err
                    );
                }
            }

            toast.success("Successfully suspended the account.");

            if (profileType === MemberTypes.CHARITY)
                loadCharities(selectedRegion._id);

            loadProfile();
            loadCardData();
            loadSecondaryMembersData();
            onHide();
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to suspend the account!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsSuspending(false);
        }
    }, [
        profileType,
        onHide,
        memberId,
        loadProfile,
        loadCardData,
        loadSecondaryMembersData,
        setIsSuspending,
        isAuthorizedForAction,
        loadCharities,
        selectedRegion,
        reason,
    ]);

    // TODO: [SHTT-1442] - Add inputs (radio and/or input) to allow the user to select or enter a reason to SUSPEND member profile.
    return (
        <>
            <Modal show={show} onHide={onHide} centered backdrop="static">
                <Modal.Header closeButton={!isSuspending}>
                    <Modal.Title>Suspend Account</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form.Group controlId="suspendReason">
                        <Form.Label>Reason for Suspension</Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={3}
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                            placeholder="Enter the reason for suspending this account"
                            disabled={isSuspending}
                        />
                    </Form.Group>
                    <h4 className="text-orange my-3">
                        {`This action will also suspend this account's `}
                        <ul>
                            <li>
                                <span className="text-secondary">
                                    {CardStatus.ASSIGNED}
                                </span>
                                {` card (if any).`}
                            </li>
                            {profileType === MemberTypes.PRIMARY ? (
                                <li>
                                    <span>
                                        {`Secondary members (if any) and thier `}
                                        <span className="text-secondary">
                                            {CardStatus.ASSIGNED}
                                        </span>
                                        {` cards (if any).`}
                                    </span>
                                </li>
                            ) : null}
                        </ul>
                    </h4>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        size="sm"
                        variant="outline-primary"
                        onClick={onHide}
                        disabled={isSuspending}
                    >
                        Cancel
                    </Button>
                    <Button
                        size="sm"
                        variant="orange"
                        onClick={handleSuspendClick}
                        disabled={isSuspending || !reason.trim()}
                    >
                        {isSuspending ? "Suspending..." : "Suspend"}
                    </Button>
                </Modal.Footer>
            </Modal>

            <Modal
                show={showConfirmation}
                onHide={() => setShowConfirmation(false)}
                centered
            >
                <Modal.Header closeButton={!isSuspending}>
                    <Modal.Title>Confirm Suspending</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    Are you sure you want to suspend this member's account?
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        size="sm"
                        variant="outline-primary"
                        onClick={() => setShowConfirmation(false)}
                        disabled={isSuspending}
                    >
                        No
                    </Button>
                    <Button
                        size="sm"
                        variant="orange"
                        onClick={onClickSuspend}
                        disabled={isSuspending}
                    >
                        Yes, Suspend
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
};

SuspendAccount.defaultProps = {
    show: false,
    memberId: "",
    profileType: "",
    onHide: () => {},
    loadProfile: () => {},
    loadCardData: () => {},
    loadSecondaryMembersData: () => {},
};

SuspendAccount.propTypes = {
    show: PropTypes.bool,
    memberId: PropTypes.string,
    profileType: PropTypes.string,
    onHide: PropTypes.func,
    loadProfile: PropTypes.func,
    loadCardData: PropTypes.func,
    loadSecondaryMembersData: PropTypes.func,
};

export default SuspendAccount;
