import React, { useCallback, useContext, useMemo, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext, UserContext } from "Contexts";
import {
    MemberPrimaryAttributeObjectPropertyName,
    MemberPrimaryAttributesAsObjAttributes,
    MemberTypes,
} from "Data";
import { archiveCharity, archiveMember } from "Services";
import { toTitleCase } from "Utils";

const ArchiveAccount = ({
    show,
    memberId,
    member,
    profileType,
    onHide,
    onNavigatingBack,
}) => {
    const { organization, selectedRegion } = useContext(UserContext);
    const { loadCharities } = useContext(DataContext);
    const [isArchiving, setIsArchiving] = useState(false);

    const memberPrimaryAttribute = useMemo(
        () => organization?.configuration?.memberPrimaryAttribute ?? "",
        [organization?.configuration?.memberPrimaryAttribute]
    );

    const memberIdentifierLabel = useMemo(
        () =>
            MemberPrimaryAttributeObjectPropertyName[memberPrimaryAttribute] ??
            "primary attribute",
        [memberPrimaryAttribute]
    );

    const onClickArchive = useCallback(async () => {
        try {
            setIsArchiving(true);
            if (profileType !== "CHARITY") {
                await archiveMember(memberId);
            } else {
                await archiveCharity(memberId);
                loadCharities(selectedRegion._id);
            }
            setIsArchiving(false);
            toast.success(
                `Successfully archived the ${
                    profileType && profileType === MemberTypes.CHARITY
                        ? MemberTypes.CHARITY.toLowerCase()
                        : profileType?.toLowerCase()
                }'s loyalty account`
            );
            onHide();
            onNavigatingBack();
        } catch (e) {
            console.error(e);
            setIsArchiving(false);
            toast.error(
                <div>
                    {`Failed to archive
                    ${
                        profileType && profileType === MemberTypes.CHARITY
                            ? MemberTypes.CHARITY.toLowerCase()
                            : profileType?.toLowerCase()
                    }'s loyalty account!`}
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        memberId,
        profileType,
        onHide,
        onNavigatingBack,
        setIsArchiving,
        selectedRegion?._id,
        loadCharities,
    ]);

    return (
        <Modal show={show} onHide={onHide} centered backdrop="static">
            <Modal.Header closeButton={!isArchiving}>
                <Modal.Title>
                    Archive {profileType ? `${toTitleCase(profileType)} ` : ""}
                    Account
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div className="mb-4">
                    {`Do you want to archive this ${
                        profileType && profileType === MemberTypes.CHARITY
                            ? MemberTypes.CHARITY.toLowerCase()
                            : profileType?.toLowerCase()
                    } account?`}
                </div>
                <div className="mb-3 p-2 rounded text-center text-danger danger-div">
                    <h2 className="p-0 m-0">WARNING!</h2>
                    <div className="font-weight-bold mb-2">
                        {`Archiving the ${
                            profileType && profileType === MemberTypes.CHARITY
                                ? MemberTypes.CHARITY.toLowerCase()
                                : profileType?.toLowerCase()
                        }'s loyalty account will invalidate
                        it forever.`}
                    </div>
                    <div className="pb-3 font-weight-bold">
                        This action cannot be undone.
                    </div>
                </div>
                <div className="text-danger">
                    This action will:
                    <ul className="text-danger">
                        <li className="my-2">
                            {`Remove the ${
                                profileType &&
                                profileType === MemberTypes.CHARITY
                                    ? MemberTypes.CHARITY.toLowerCase()
                                    : profileType?.toLowerCase()
                            }'s loyalty account.`}
                        </li>
                        <li className="my-2">
                            {`Prevent the ${memberIdentifierLabel} `}
                            <span className="font-weight-bold">
                                {member?.[
                                    MemberPrimaryAttributesAsObjAttributes[
                                        memberPrimaryAttribute
                                    ]
                                ] ?? "~unknown"}
                            </span>
                            {
                                " from creating a new loyalty account in the Loyalty System."
                            }
                        </li>
                        <li className="my-2">
                            {`Deactivate all assigned loyalty cards (if any) of the ${
                                profileType &&
                                profileType === MemberTypes.CHARITY
                                    ? MemberTypes.CHARITY.toLowerCase()
                                    : profileType?.toLowerCase()
                            }.`}
                        </li>
                        <li className="my-2">
                            {"Delete the "}
                            <span className="font-weight-bold">
                                Customer Portal
                            </span>
                            {` (if any) account of the ${memberIdentifierLabel} `}
                            <span className="font-weight-bold">
                                {member?.[
                                    MemberPrimaryAttributesAsObjAttributes[
                                        memberPrimaryAttribute
                                    ]
                                ] ?? "~unknown"}
                            </span>
                            .
                        </li>
                    </ul>
                </div>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    variant="outline-primary"
                    size="sm"
                    onClick={onHide}
                    disabled={isArchiving}
                >
                    Cancel
                </Button>
                <Button
                    variant="danger"
                    size="sm"
                    onClick={onClickArchive}
                    disabled={isArchiving}
                >
                    {isArchiving ? "Archiving..." : "Archive"}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

ArchiveAccount.defaultProps = {
    show: false,
    memberId: "",
    member: {},
    profileType: "",
    onHide: () => {},
    onNavigatingBack: () => {},
};

ArchiveAccount.propTypes = {
    show: PropTypes.bool,
    memberId: PropTypes.string,
    member: PropTypes.object,
    profileType: PropTypes.string,
    onHide: PropTypes.func,
    onNavigatingBack: PropTypes.func,
};

export default ArchiveAccount;
