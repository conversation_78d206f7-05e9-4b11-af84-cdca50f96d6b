// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Customer Portal component snapshot Matches the snapshot 1`] = `
<div
  className="mb-3 card"
>
  <div
    className="d-flex flex-row jestify-content-between pt-2 px-3 mb-2"
  >
    <h4
      className="mb-0"
    >
      Customer Portal
    </h4>
  </div>
  <div
    className="d-flex flex-row jestify-content-between pt-2 px-3"
  >
    <svg
      className="ic-icon icon-w-10 mr-2 text-primary"
      fill="currentColor"
      height="24"
      viewBox="0 0 24 24"
      width="24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21,14H20V7a3,3,0,0,0-3-3H7A3,3,0,0,0,4,7v7H3a1,1,0,0,0-1,1v2a3,3,0,0,0,3,3H19a3,3,0,0,0,3-3V15A1,1,0,0,0,21,14ZM6,7A1,1,0,0,1,7,6H17a1,1,0,0,1,1,1v7H6ZM20,17a1,1,0,0,1-1,1H5a1,1,0,0,1-1-1V16H20Z"
      />
    </svg>
    <div
      className="d-flex flex-row mr-auto"
    >
      <p
        className="text-muted mb-0 pb-0"
      >
        Web Portal
      </p>
    </div>
  </div>
  <p
    className="mb-0 pb-0 pr-3 customer-portal-web"
  >
    -
  </p>
  <div
    className="d-flex flex-row jestify-content-between pt-2 px-3 mt-2"
  >
    <svg
      className="ic-icon icon-w-10 mr-3 text-primary"
      fill="currentColor"
      height="24"
      viewBox="0 0 24 24"
      width="24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16,2H8A3,3,0,0,0,5,5V19a3,3,0,0,0,3,3h8a3,3,0,0,0,3-3V5A3,3,0,0,0,16,2Zm1,17a1,1,0,0,1-1,1H8a1,1,0,0,1-1-1V18H17Zm0-3H7V5A1,1,0,0,1,8,4h8a1,1,0,0,1,1,1Z"
      />
    </svg>
    <div
      className="d-flex flex-row mr-auto"
    >
      <p
        className="text-muted mb-0 pb-0"
      >
        Mobile App
      </p>
    </div>
  </div>
  <p
    className="mb-3 pb-0 pr-3 customer-portal-web"
  >
     - 
  </p>
</div>
`;
