import React from "react";
import { create } from "react-test-renderer";
import { UserContext } from "Contexts";
import Tags from "./Tags";

const regionId = "6128e3537ed841e246e2e396";
const memberId = "6128e3537ed841e246e2e667";

describe("Tags component snapshot", () => {
    test("Matches the snapshot", () => {
        const loadProfile = jest.fn();

        const props = {
            profileType: "MEMBER",
            memberId,
            memberTags: ["Test", "Test123", "Loyalty Member"],
            hasEditPermission: true,
            loadProfile,
        };

        const component = create(
            <UserContext.Provider value={regionId}>
                <Tags props={props} />
            </UserContext.Provider>
        );
        expect(component.toJSON()).toMatchSnapshot();
    });
});
