import React, { useCallback, useMemo, useState } from "react";
import paginationFactory, {
    PaginationProvider,
} from "react-bootstrap-table2-paginator";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import { BootstrapTable } from "@shoutout-labs/shoutout-themes-enterprise";
import { BootstrapTableOverlay } from "Components/utils";

import "./ViewMemberAboutData.scss";

const NoData = ({ loading }) => {
    if (loading) return null;
    return <div className="text-center">No data found.</div>;
};

const columns = [
    { dataField: "id", name: "id", hidden: true },
    { dataField: "item", name: "", text: "" },
];

const defaultSkip = 1,
    defaultLimit = 3;

const ViewMemberAboutData = ({ items = [], total = 0 }) => {
    const [skip, setSkip] = useState(defaultSkip);

    const data = useMemo(
        () =>
            items
                .map((item) => ({
                    id: item?._id,
                    item,
                }))
                .slice(
                    (skip - 1) * defaultLimit,
                    (skip - 1) * defaultLimit + defaultLimit
                ),
        [items, skip]
    );

    const onChangePagination = useCallback(
        (newSkip) => setSkip(newSkip),
        [setSkip]
    );

    const options = {
        page: skip,
        sizePerPage: defaultLimit,
        totalSize: total,
        pageStartIndex: 1,
        paginationSize: defaultLimit,
        withFirstAndLast: true,
        sizePerPageList: [],
        onPageChange: onChangePagination,
    };

    const onTableChange = (_type, _newState) => {
        // * When "remote" is enabled, the "onTableChange" prop is activated automatically, so if we don't provide this method react-bootstrap will throw an error.
    };

    return (
        <div className="view-member-about-data-view">
            <PaginationProvider
                pagination={paginationFactory(options)}
                keyField="id"
                columns={columns}
                data={data}
            >
                {({ paginationTableProps }) => (
                    <ToolkitProvider
                        keyField="id"
                        data={data}
                        columns={columns}
                        columnToggle
                    >
                        {(props) => (
                            <div>
                                <BootstrapTable
                                    {...paginationTableProps}
                                    remote={{
                                        search: true,
                                        pagination: true,
                                    }}
                                    keyField="id"
                                    onTableChange={onTableChange}
                                    noDataIndication={
                                        <NoData loading={false} />
                                    }
                                    overlay={BootstrapTableOverlay}
                                    {...props.baseProps}
                                />
                            </div>
                        )}
                    </ToolkitProvider>
                )}
            </PaginationProvider>
        </div>
    );
};

export default ViewMemberAboutData;
