import React, { useState, useCallback, useEffect } from "react";
import { useHistory } from "react-router-dom";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    <PERSON><PERSON>,
    Card,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faSync } from "FaICIconMap";
import { getMemberById } from "Services";
import { LoadingComponent } from "Components/utils/UtilComponents";
import AccountCard from "../../common/accountCard/AccountCard";

const PrimaryAccount = ({ parentMemberId }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [isReloading, setIsReloading] = useState(false);
    const [primaryAccount, setPrimaryAccount] = useState();
    const history = useHistory();

    const loadParentAccount = useCallback(async () => {
        try {
            setIsLoading(true);
            const memberProfileResponse = await getMemberById(parentMemberId);
            setPrimaryAccount(memberProfileResponse);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load primary member!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoading(false);
        }
    }, [parentMemberId, setPrimaryAccount, setIsLoading]);

    const reloadPrimaryAccount = useCallback(async () => {
        setIsReloading(true);
        await loadParentAccount();
        setIsReloading(false);
    }, [setIsReloading, loadParentAccount]);

    const onNavigateProfile = useCallback(
        () => history.push(`/members/${primaryAccount?._id}`),
        [history, primaryAccount?._id]
    );

    useEffect(() => {
        if (parentMemberId) {
            loadParentAccount();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [parentMemberId]);

    return (
        <Card className="primary-member-view mb-3">
            <div className="d-flex justify-content-between align-items-center pt-2 px-3 mb-2">
                <div className="d-flex mr-auto">
                    <h4 className="mb-0">Primary Account</h4>
                </div>
                <div>
                    {isReloading ? (
                        <small className="text-primary">Reloading...</small>
                    ) : (
                        <Button
                            className="m-0 p-0 shadow-none"
                            variant="link"
                            size="sm"
                            disabled={isLoading || isReloading}
                            onClick={reloadPrimaryAccount}
                        >
                            <IcIcon size="lg" icon={faSync} />
                        </Button>
                    )}
                </div>
            </div>
            {isLoading ? (
                <LoadingComponent />
            ) : (
                <>
                    {primaryAccount ? (
                        <div className="mx-1">
                            <AccountCard
                                _id={primaryAccount?._id}
                                type={primaryAccount?.type}
                                status={primaryAccount?.status}
                                firstName={primaryAccount?.firstName}
                                lastName={primaryAccount?.lastName}
                                cardNumber={primaryAccount?.cardNumber}
                                isLoading={isLoading}
                                isReloading={isReloading}
                                onNavigateProfile={onNavigateProfile}
                            />
                        </div>
                    ) : (
                        <div className="text-center grey-bg rounded mx-3 mb-3 p-2">
                            Primary member not found.
                        </div>
                    )}
                </>
            )}
        </Card>
    );
};

PrimaryAccount.defaultProps = { parentMemberId: "" };

PrimaryAccount.propTypes = { parentMemberId: PropTypes.string };

export default PrimaryAccount;
