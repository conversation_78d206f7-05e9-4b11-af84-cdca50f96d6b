import React, { useState, useCallback, useContext, useMemo } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Button, Modal, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { assignCardToALoyaltyId } from "Services";

const AddLoyaltyCard = ({
    show,
    onHide,
    memberId,
    loadCardData,
    loadProfile,
}) => {
    const { organization } = useContext(UserContext);
    const [loyaltyCardNo, setLoyaltyCardNo] = useState("");
    const [waiveCardReplacementFee, setWaiveCardReplacementFee] =
        useState(false);
    const [isValidated, setIsValidated] = useState(false);
    const [isAdding, setIsAdding] = useState(false);

    const loyaltyCardNumberLength = useMemo(() => {
        const cardNoLength =
            organization.configuration.cardConfiguration
                .loyaltyCardNumberLength || null;
        if (cardNoLength) {
            return cardNoLength + (Math.round(cardNoLength / 4) - 1);
        } else {
            return 14;
        }
    }, [organization.configuration.cardConfiguration.loyaltyCardNumberLength]);

    const onChangeLoyaltyCard = useCallback(
        (e) =>
            setLoyaltyCardNo(
                e.target.value
                    .replace(/[^\dA-Z]/g, "")
                    .replace(/(.{4})/g, "$1 ")
                    .trim()
            ),
        [setLoyaltyCardNo]
    );

    const onChangeWaiveCardReplacementFeeStatus = useCallback(
        (e) => setWaiveCardReplacementFee(e.target.checked),
        [setWaiveCardReplacementFee]
    );

    const onCancel = useCallback(
        (e, data) => {
            setLoyaltyCardNo("");
            setWaiveCardReplacementFee(false);
            onHide(e, data);
        },
        [setWaiveCardReplacementFee, setLoyaltyCardNo, onHide]
    );

    const addCard = useCallback(
        async (e) => {
            e.preventDefault();
            if (e.target.checkValidity()) {
                const queryObj = {
                    memberId,
                    cardNumber: loyaltyCardNo.replace(/\s/g, ""),
                    waiveCardReplacementFee,
                };

                try {
                    setIsAdding(true);
                    const addCardResponse = await assignCardToALoyaltyId(
                        queryObj
                    );
                    setIsAdding(false);
                    toast.success(
                        `Successfully assigned the loyalty card ${loyaltyCardNo} to member.`
                    );
                    setLoyaltyCardNo("");
                    onCancel(null, addCardResponse);
                    loadCardData();
                    loadProfile();
                } catch (e) {
                    console.error(e);
                    setIsAdding(false);
                    toast.error(
                        <div>
                            Failed to assign card to member!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setIsValidated(true);
            }
        },
        [
            loadProfile,
            waiveCardReplacementFee,
            setLoyaltyCardNo,
            onCancel,
            setIsValidated,
            loyaltyCardNo,
            memberId,
            loadCardData,
        ]
    );

    return (
        <Modal show={show} onHide={onCancel} backdrop="static" centered>
            <Modal.Header closeButton={!isAdding}>
                <Modal.Title>Add Loyalty Card</Modal.Title>
            </Modal.Header>
            <Form onSubmit={addCard} validated={isValidated} noValidate>
                <Modal.Body>
                    <Form.Group>
                        <Form.Label className="d-flex align-items-center">
                            New Loyalty Card Number
                            <div className="ml-1 text-danger">*</div>
                        </Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter new loyalty card number"
                            value={loyaltyCardNo}
                            required
                            onChange={onChangeLoyaltyCard}
                            minLength={loyaltyCardNumberLength}
                            maxLength={loyaltyCardNumberLength}
                        />
                    </Form.Group>
                    <Form.Group>
                        <Form.Check
                            className="member-checkbox-margin"
                            name="waiveCardReplacementFee"
                            id="checkbox"
                            type="checkbox"
                            checked={waiveCardReplacementFee}
                            onChange={onChangeWaiveCardReplacementFeeStatus}
                            label="Waive card replacement fee"
                        />
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        size="sm"
                        variant="outline-primary"
                        onClick={onCancel}
                        type="button"
                        disabled={isAdding}
                    >
                        Cancel
                    </Button>
                    <Button
                        size="sm"
                        variant="primary"
                        type="submit"
                        disabled={isAdding}
                    >
                        {isAdding ? "Assigning Card..." : "Add Card"}
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal>
    );
};

AddLoyaltyCard.propTypes = {
    memberId: PropTypes.string.isRequired,
    show: PropTypes.bool.isRequired,
    onHide: PropTypes.func,
};

export default AddLoyaltyCard;
