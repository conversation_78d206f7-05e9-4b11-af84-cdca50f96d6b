import React, { useState, useCallback, useContext } from "react";
import DropdownButton from "react-bootstrap/DropdownButton";
import { useHistory } from "react-router-dom";
import { toast } from "react-toastify";
import {
    Card,
    IcIcon,
    Button,
    DropdownItem,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faAngleDown,
    faAngleUp,
    faEllipsisV,
    faPlus,
    faSync,
} from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    CardSuspendReasons,
    CardStatus,
    AccessPermissionModuleNames,
    AccessPermissionModules,
} from "Data";
import { useToggle } from "Hooks";
import { updateCardStatus } from "Services";
import ChangeLoyaltyCardStatus from "Components/common/changeLoyaltyCardStatus/ChangeLoyaltyCardStatus";
import LoyaltyCard from "Components/common/loyaltyCard/LoyaltyCard";
import { LoadingComponent } from "Components/utils/UtilComponents";
import ViewMemberAboutData from "../shared/ViewMemberAboutData";
import AddLoyaltyCard from "./AddLoyaltyCard";
import CreateEmbossed from "./CreateEmbossed";

const generatePayload = ({
    status = "",
    note = "",
    suspendedReason = "",
    waiveCardReplacementFee = false,
}) => {
    const payload = {
        status,
        note: `Status updated to ${status}`,
        ...(status === CardStatus.DEACTIVATED
            ? { waiveCardReplacementFee }
            : {}),
    };

    if (status === CardStatus.SUSPENDED) {
        payload.note =
            suspendedReason === CardSuspendReasons.OTHER
                ? note
                : suspendedReason;
    }

    return payload;
};

const LoyaltyCards = ({
    isLoading,
    address,
    name,
    memberId,
    loyaltyCards,
    totalCards,
    setLoyaltyCards,
    loadProfile,
    loadCardData,
}) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [waiveCardReplacementFee, setWaiveCardReplacementFee] =
        useState(false);
    const [isShowMore, setIsShowMore] = useToggle(false);
    const [showModel, setShowModel] = useState(false);
    const [showEmbossedModal, setShowEmbossedModal] = useState(false);
    const [showChangeStatus, setShowChangeStatus] = useState(false);
    const [selectedCardId, setSelectedCardId] = useState("");
    const [validated, setValidated] = useState(false);
    const [updatedStatus, setUpdatedStatus] = useState("");
    const [selectedCardType, setSelectedCardType] = useState("");
    const [suspendedReason, setSuspendedReason] = useState("");
    const [note, setNote] = useState("");
    const [isReloadingCards, setIsReloadingCards] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);
    const [embossedRequestedCardId, setEmbossedRequestedCardId] = useState("");
    const history = useHistory();

    const onShowStatusChangeModal = useCallback(
        (e) => {
            const statusToUpdate = e.currentTarget.dataset.statusToUpdate || "";
            const cardType = e.currentTarget.dataset.cardType || "";

            setSelectedCardId(e.currentTarget.id || "");
            setUpdatedStatus(statusToUpdate);
            setSelectedCardType(cardType);
            setShowChangeStatus(true);
        },
        [
            setShowChangeStatus,
            setSelectedCardId,
            setUpdatedStatus,
            setSelectedCardType,
        ]
    );

    const onHideStatusChangeModal = useCallback(
        (e, data) => {
            setSelectedCardId("");
            setValidated(false);
            setNote("");
            setSuspendedReason("");
            setShowChangeStatus(false);
            setUpdatedStatus("");
            setSelectedCardType("");
            setIsUpdating(false);

            if (data) {
                loadCardData();
                loadProfile();
            }
        },
        [
            loadProfile,
            loadCardData,
            setValidated,
            setNote,
            setSuspendedReason,
            setShowChangeStatus,
            setSelectedCardId,
            setUpdatedStatus,
            setSelectedCardType,
            setIsUpdating,
        ]
    );

    const updateCard = useCallback(
        async (e) => {
            e.preventDefault();
            if (
                e.target.checkValidity() ||
                (updatedStatus === CardStatus.SUSPENDED && note)
            ) {
                const payload = generatePayload({
                    status: updatedStatus,
                    note,
                    suspendedReason,
                    waiveCardReplacementFee,
                });

                try {
                    setIsUpdating(true);
                    await updateCardStatus(selectedCardId, payload);
                    setIsUpdating(false);
                    toast.success(
                        <div>
                            {`Successfully `}
                            <span className="font-weight-bold">
                                {updatedStatus}
                            </span>
                            {` the card`}
                            {updatedStatus === CardStatus.DEACTIVATED &&
                            waiveCardReplacementFee
                                ? " and waived off the replacement fee."
                                : "."}
                        </div>
                    );
                    onHideStatusChangeModal(null, "Updated");
                } catch (e) {
                    setIsUpdating(false);
                    console.error(e);
                    toast.error(
                        <div>
                            Failed to update card status!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setValidated(true);
            }
        },
        [
            selectedCardId,
            updatedStatus,
            suspendedReason,
            note,
            waiveCardReplacementFee,
            setValidated,
            setIsUpdating,
            onHideStatusChangeModal,
        ]
    );

    const showAddLoyaltyCard = useCallback(() => {
        setShowModel(true);
    }, [setShowModel]);

    const onHideModel = useCallback(
        (e, newCardDetails) => {
            setShowModel(false);
            if (newCardDetails) {
                setLoyaltyCards([newCardDetails, ...loyaltyCards]);
            }
        },
        [setShowModel, setLoyaltyCards, loyaltyCards]
    );

    const embossedModalShow = useCallback(
        (e) => {
            setEmbossedRequestedCardId(e.currentTarget.dataset.id);
            setShowEmbossedModal(true);
        },
        [setShowEmbossedModal, setEmbossedRequestedCardId]
    );

    const onHideEmbossedModal = useCallback(
        (e, requestSuccessful) => {
            if (requestSuccessful) {
                loadCardData();
            }
            setShowEmbossedModal(false);
            setEmbossedRequestedCardId("");
        },
        [setShowEmbossedModal, setEmbossedRequestedCardId, loadCardData]
    );

    const onReloadCards = useCallback(async () => {
        setIsReloadingCards(true);
        await loadCardData();
        setIsReloadingCards(false);
    }, [loadCardData, setIsReloadingCards]);

    const navigateToCardGenerate = useCallback(
        (e) => {
            e.stopPropagation();

            history.push({
                pathname: "/cards/embossed-cards",
                state: e.currentTarget.dataset.id,
            });
        },
        [history]
    );

    return (
        <>
            {loyaltyCards && (
                <Card className="mb-3">
                    <div className="d-flex justify-content-between my-2 px-3">
                        <div className="d-flex mr-auto">
                            <h4 className="mb-0">Loyalty Cards</h4>
                        </div>
                        <div className="d-flex align-items-center">
                            <div>
                                {isReloadingCards && (
                                    <small className="text-primary">
                                        Reloading...
                                    </small>
                                )}
                            </div>
                            {!isReloadingCards && (
                                <DropdownButton
                                    data-testid="add-loyalty-card-dropdown"
                                    bsPrefix="text-capitalize dropdown-btn single-dropdown-toggle"
                                    title={
                                        <IcIcon size="lg" icon={faEllipsisV} />
                                    }
                                >
                                    <DropdownItem
                                        eventKey="addLoyaltyCard"
                                        key="addLoyaltyCard"
                                    >
                                        <Button
                                            className="mb-2 w-100"
                                            variant="primary"
                                            size="sm"
                                            disabled={
                                                !isAuthorizedForAction(
                                                    AccessPermissionModuleNames.CARD,
                                                    AccessPermissionModules[
                                                        AccessPermissionModuleNames
                                                            .CARD
                                                    ].actions.AssignCard
                                                ) ||
                                                isLoading ||
                                                isReloadingCards
                                            }
                                            onClick={showAddLoyaltyCard}
                                        >
                                            <div className="d-flex align-items-center">
                                                <IcIcon
                                                    className="mr-2"
                                                    size="lg"
                                                    icon={faPlus}
                                                />
                                                <div className="py-1">
                                                    Add Loyalty Card
                                                </div>
                                            </div>
                                        </Button>
                                    </DropdownItem>
                                    <DropdownItem
                                        eventKey="reloadCards"
                                        key="reloadCards"
                                        className="border-top pt-2"
                                    >
                                        <Button
                                            className="shadow-none mb-2 w-100"
                                            variant="link"
                                            size="sm"
                                            disabled={
                                                isLoading || isReloadingCards
                                            }
                                            onClick={onReloadCards}
                                        >
                                            <div className="d-flex justify-content-center align-items-center">
                                                <IcIcon
                                                    className="mr-2"
                                                    size="md"
                                                    icon={faSync}
                                                />
                                                Reload Loyalty Cards
                                            </div>
                                        </Button>
                                    </DropdownItem>
                                </DropdownButton>
                            )}
                        </div>
                    </div>
                    {isLoading ? (
                        <LoadingComponent />
                    ) : (
                        <div className="mx-1">
                            {totalCards > 0 ? (
                                <>
                                    {isShowMore && totalCards > 1 ? (
                                        <ViewMemberAboutData
                                            items={loyaltyCards.map((card) => (
                                                <div
                                                    key={card?._id}
                                                    className="my-3"
                                                >
                                                    <LoyaltyCard
                                                        key={card?._id}
                                                        isLoading={isLoading}
                                                        card={card}
                                                        onShowStatusChangeModal={
                                                            onShowStatusChangeModal
                                                        }
                                                        embossedModalShow={
                                                            embossedModalShow
                                                        }
                                                        navigateToCardGenerate={
                                                            navigateToCardGenerate
                                                        }
                                                    />
                                                </div>
                                            ))}
                                            total={totalCards}
                                        />
                                    ) : (
                                        <div className="my-3">
                                            <LoyaltyCard
                                                key={loyaltyCards[0]?._id}
                                                isLoading={isLoading}
                                                card={loyaltyCards[0] || {}}
                                                onShowStatusChangeModal={
                                                    onShowStatusChangeModal
                                                }
                                                embossedModalShow={
                                                    embossedModalShow
                                                }
                                                navigateToCardGenerate={
                                                    navigateToCardGenerate
                                                }
                                            />
                                        </div>
                                    )}
                                </>
                            ) : (
                                <div className="text-center grey-bg rounded mx-3 mb-3 p-2">
                                    No loyalty cards found.
                                </div>
                            )}
                        </div>
                    )}
                    {!isShowMore && totalCards > 1 && (
                        <Button
                            className="btn shadow-none show-click"
                            size="md"
                            variant="link"
                            disabled={isLoading}
                            onClick={setIsShowMore}
                        >
                            <div className="d-flex justify-content-center align-items-center">
                                {`See ${(totalCards - 1)
                                    .toString()
                                    .padStart(2, "0")} more`}
                                <IcIcon
                                    size="lg"
                                    className="ml-1"
                                    icon={faAngleDown}
                                />
                            </div>
                        </Button>
                    )}
                    {isShowMore && totalCards > 1 && (
                        <Button
                            className="btn shadow-none show-click"
                            size="md"
                            variant="link"
                            disabled={isLoading}
                            onClick={setIsShowMore}
                        >
                            <div className="d-flex justify-content-center align-items-center">
                                See less
                                <IcIcon
                                    size="lg"
                                    className="ml-1"
                                    icon={faAngleUp}
                                />
                            </div>
                        </Button>
                    )}
                </Card>
            )}
            {showModel && (
                <AddLoyaltyCard
                    show={showModel}
                    onHide={onHideModel}
                    memberId={memberId}
                    loadCardData={loadCardData}
                    loadProfile={loadProfile}
                />
            )}
            {showEmbossedModal && (
                <CreateEmbossed
                    show={showEmbossedModal}
                    onHide={onHideEmbossedModal}
                    address={address}
                    name={name}
                    embossedRequestedCardId={embossedRequestedCardId}
                />
            )}
            {showChangeStatus && (
                <ChangeLoyaltyCardStatus
                    show={showChangeStatus}
                    cardType={selectedCardType}
                    updatedStatus={updatedStatus}
                    suspendedReason={suspendedReason}
                    note={note}
                    isUpdating={isUpdating}
                    validated={validated}
                    waiveCardReplacementFee={waiveCardReplacementFee}
                    onHide={onHideStatusChangeModal}
                    updateCard={updateCard}
                    setSuspendedReason={setSuspendedReason}
                    setNote={setNote}
                    setWaiveCardReplacementFee={setWaiveCardReplacementFee}
                />
            )}
        </>
    );
};

export default LoyaltyCards;
