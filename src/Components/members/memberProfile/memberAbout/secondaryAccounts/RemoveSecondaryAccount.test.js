import React from 'react'
import { create } from "react-test-renderer";
import RemoveSecondaryAccount from './RemoveSecondaryAccount';

describe('RemoveSecondaryAccount component snapshot', () => {
    test('Matches the snapshot', ()=> {

        const onHide = jest.fn();
    
        const props =  {show : true, onHide  : onHide, loyaltyId: "1234445"};

        const component = create(<RemoveSecondaryAccount props={props}/>);
        expect(component.toJSON()).toMatchSnapshot();

    });
});