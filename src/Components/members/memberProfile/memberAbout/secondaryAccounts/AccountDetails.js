import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    Form,
    IcIcon,
    Modal,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faSync } from "FaICIconMap";
import { UserContext } from "Contexts";
import { useToggle } from "Hooks";
import {
    requestAccountSecondary,
    addAccountSecondary,
    getSecondaryAccounts,
} from "Services";
import { getMemberFullName } from "Utils";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";

import "./AccountDetails.scss";

const SecondaryMembersView = ({
    viewToShow = "",
    secondaryMembersOfPrimaryMember = [],
}) => {
    SecondaryMembersView.defaultProps = {
        viewToShow: "",
        secondaryMembersOfPrimaryMember: [],
    };

    SecondaryMembersView.propTypes = {
        viewToShow: PropTypes.string,
        secondaryMembersOfPrimaryMember: PropTypes.array,
    };

    switch (viewToShow) {
        case "SECONDARY_MEMBERS_VIEW": {
            const secondaryMembersCount =
                secondaryMembersOfPrimaryMember.length;
            return (
                <div className="secondary-members-view rounded p-3 my-3">
                    {"Current member has "}
                    <span className="font-weight-bold">
                        {secondaryMembersCount}
                        {" secodary"}
                        {secondaryMembersCount === 1 ? " member" : " members"}
                    </span>
                    .
                    <div className="secondary-members-list rounded my-2">
                        <ul className="mb-0">
                            {secondaryMembersOfPrimaryMember.map((sMOPM) => {
                                return (
                                    <li key={sMOPM?._id} className="my-1">
                                        {getMemberFullName({
                                            firstName: sMOPM?.firstName,
                                            lastName: sMOPM?.lastName,
                                        })}
                                    </li>
                                );
                            })}
                        </ul>
                    </div>
                    Please remove all secodary members from current member to
                    continue.
                </div>
            );
        }
        case "FAILED_TO_LOAD_SECONDARY_MEMBERS_VIEW":
            return (
                <div className="secondary-members-error-view rounded text-center p-3 my-3">
                    <h3>Please reload the secodary members to continue.</h3>
                    If the issue persists, please contact support.
                </div>
            );
        default:
            return null;
    }
};

const AccountDetails = ({
    member,
    show,
    parentId,
    onHide,
    loadSecondaryAccounts,
}) => {
    const { selectedRegion } = useContext(UserContext);
    const [isRequesting, toggleIsRequesting] = useToggle(false);
    const [isAdding, toggleIsAdding] = useToggle(false);
    const [isLoadingSecondaryAccounts, setIsLoadingSecondaryAccounts] =
        useState(false);
    const [
        secondaryAccountsOfCurrentMember,
        setSecondaryAccountsOfCurrentMember,
    ] = useState([]);
    const [failedToGetSecondaryAccounts, setFailedToGetSecondaryAccounts] =
        useState(false);
    const [isReloadingSecondaryAccounts, setIsReloadingSecondaryAccounts] =
        useState(false);
    const [skipSecondaryVerification, setSkipSecondaryVerification] =
        useState(false);
    const [requestToken, setRequestToken] = useState();
    const [secondaryOtp, setSecondaryOtp] = useState();

    const viewToShow = useMemo(() => {
        if (failedToGetSecondaryAccounts) {
            return "FAILED_TO_LOAD_SECONDARY_MEMBERS_VIEW";
        } else if (secondaryAccountsOfCurrentMember.length !== 0) {
            return "SECONDARY_MEMBERS_VIEW";
        } else {
            return "";
        }
    }, [failedToGetSecondaryAccounts, secondaryAccountsOfCurrentMember.length]);

    const onChangeOtp = useCallback(
        (e) => {
            e.stopPropagation();
            setSecondaryOtp(e.target.value);
        },
        [setSecondaryOtp]
    );

    const onCancel = useCallback(() => {
        setRequestToken();
        setSecondaryOtp();
        onHide();
    }, [setRequestToken, setSecondaryOtp, onHide]);

    const onChangeSkipSecondaryVerification = useCallback(
        (e) => {
            e.stopPropagation();
            setSkipSecondaryVerification(e.target.checked);
        },
        [setSkipSecondaryVerification]
    );

    const loadSecondaryAccountsOfCurrentMember = useCallback(async () => {
        try {
            if (!member?._id) throw new Error("Member id not found");

            const queryObj = {
                limit: 100,
                skip: 0,
                regionId: selectedRegion?._id,
                parentMemberId: member._id,
            };

            setIsLoadingSecondaryAccounts(true);
            const secondary = await getSecondaryAccounts(queryObj);
            setFailedToGetSecondaryAccounts(false);
            setIsLoadingSecondaryAccounts(false);
            setSecondaryAccountsOfCurrentMember(secondary.items);

            if (requestToken && secondary.items?.length !== 0)
                setRequestToken();
        } catch (e) {
            setIsLoadingSecondaryAccounts(false);
            setFailedToGetSecondaryAccounts(true);
            console.error(e);
            toast.error(
                <div>
                    Failed to load current member's secondary accounts!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        member?._id,
        selectedRegion?._id,
        requestToken,
        setIsLoadingSecondaryAccounts,
        setFailedToGetSecondaryAccounts,
        setSecondaryAccountsOfCurrentMember,
        setRequestToken,
    ]);

    const requestSecondaryToken = useCallback(async () => {
        const requestPayload = {
            primaryMemberId: parentId,
            secondaryMemberId: member._id,
        };

        try {
            toggleIsRequesting();
            const secondaryRequestResponse = await requestAccountSecondary(
                requestPayload
            );
            setRequestToken(secondaryRequestResponse.requestToken);
            toast.success(
                "OTPs are being sent to the registered mobile numbers of the primary and secondary accounts."
            );
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to request tokens!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            toggleIsRequesting();
        }
    }, [parentId, member?._id, toggleIsRequesting, setRequestToken]);

    const addAccount = useCallback(async () => {
        try {
            toggleIsAdding();
            if (requestToken) {
                const addSecondaryAccountPayload = {
                    requestToken: requestToken,
                    secondaryOtpCode: secondaryOtp,
                    skipSecondaryVerification,
                };
                await addAccountSecondary(addSecondaryAccountPayload);
                loadSecondaryAccounts();
                onCancel();
                toast.success("Successfully added a secondary account.");
            }
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load secodary members of current member!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            toggleIsAdding();
        }
    }, [
        skipSecondaryVerification,
        requestToken,
        secondaryOtp,
        toggleIsAdding,
        loadSecondaryAccounts,
        onCancel,
    ]);

    const onReloadSecondaryMembers = useCallback(async () => {
        setIsReloadingSecondaryAccounts(true);
        await loadSecondaryAccountsOfCurrentMember();
        setIsReloadingSecondaryAccounts(false);
    }, [loadSecondaryAccountsOfCurrentMember, setIsReloadingSecondaryAccounts]);

    useEffect(() => {
        if (member?._id) {
            loadSecondaryAccountsOfCurrentMember();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [member?._id]);

    return (
        <Modal
            className="account-details-view"
            show={show}
            onHide={onCancel}
            centered
            backdrop="static"
        >
            <Modal.Header
                closeButton={
                    !(
                        isLoadingSecondaryAccounts ||
                        isReloadingSecondaryAccounts ||
                        isRequesting ||
                        isAdding
                    )
                }
            >
                <Modal.Title>Add Secondary Member</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div className="w-100 mb-3 d-flex justify-content-between align-items-center">
                    <div className="w-100 mr-2">
                        <DetailsAsLabelValue
                            label="Card Number"
                            value={member?.cardNumber || "~ unknown"}
                        />
                    </div>
                    <div className="w-100 ml-2">
                        <DetailsAsLabelValue
                            label="Points"
                            value={member?.points?.toString() || "~ unknown"}
                        />
                    </div>
                </div>
                <div className="w-100 mb-3 d-flex justify-content-between align-items-center">
                    <div className="w-100 mr-2">
                        <DetailsAsLabelValue
                            label="First Name"
                            value={member?.firstName || "~ unknown"}
                        />
                    </div>
                    <div className="w-100 ml-2">
                        <DetailsAsLabelValue
                            label="Last Name"
                            value={member?.lastName || "~ unknown"}
                        />
                    </div>
                </div>
                <div className="w-100 mb-3 d-flex justify-content-between align-items-center">
                    <div className="w-100 mr-2">
                        <DetailsAsLabelValue
                            label="Email"
                            value={member?.email || "~ unknown"}
                        />
                    </div>
                    <div className="w-100 ml-2">
                        <DetailsAsLabelValue
                            label="Mobile Number"
                            value={member?.mobileNumber || "~ unknown"}
                        />
                    </div>
                </div>
                {isLoadingSecondaryAccounts ? (
                    <h4 className="my-3 text-center">
                        Loading secodary members of current member...
                    </h4>
                ) : (
                    <SecondaryMembersView
                        viewToShow={viewToShow}
                        secondaryMembersOfPrimaryMember={
                            secondaryAccountsOfCurrentMember
                        }
                    />
                )}
                {!isLoadingSecondaryAccounts && requestToken && (
                    <>
                        <Form.Group className="mb-3">
                            <Form.Check
                                className="member-checkbox-margin"
                                name="skipSecondaryVerification"
                                id="checkbox"
                                type="checkbox"
                                checked={skipSecondaryVerification}
                                onChange={onChangeSkipSecondaryVerification}
                                label="Skip Secondary OTP"
                            />
                        </Form.Group>
                        {!skipSecondaryVerification && (
                            <Form.Group controlId="input-secondary-otp-group">
                                <Form.Label>
                                    <span>Secondary OTP</span>
                                    <span className="ml-2 text-danger">*</span>
                                </Form.Label>
                                <Form.Control
                                    name="secondary-otp"
                                    type="number"
                                    placeholder="Enter secondary otp..."
                                    value={secondaryOtp}
                                    disabled={isRequesting}
                                    onChange={onChangeOtp}
                                    required
                                />
                                <Form.Text className="text-info">
                                    * Please enter the OTP sent to the
                                    registered mobile number of this account.
                                </Form.Text>
                            </Form.Group>
                        )}
                    </>
                )}
            </Modal.Body>
            <Modal.Footer className="d-flex justify-content-between align-items-center">
                <div>
                    <Button
                        className="m-0 p-0 shadow-none"
                        variant="link"
                        size="sm"
                        disabled={
                            isLoadingSecondaryAccounts ||
                            isReloadingSecondaryAccounts ||
                            isRequesting ||
                            isAdding
                        }
                        onClick={onReloadSecondaryMembers}
                    >
                        {!isReloadingSecondaryAccounts && (
                            <div className="d-flex align-items-center">
                                <IcIcon
                                    size="md"
                                    className="mr-2"
                                    icon={faSync}
                                />
                                Reload Secondary Members
                            </div>
                        )}
                    </Button>
                    <div>
                        {isReloadingSecondaryAccounts && (
                            <small className="text-primary">Reloading...</small>
                        )}
                    </div>
                </div>
                <div>
                    <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={onCancel}
                        disabled={
                            isLoadingSecondaryAccounts ||
                            isReloadingSecondaryAccounts ||
                            isRequesting ||
                            isAdding
                        }
                    >
                        Cancel
                    </Button>
                    {!(
                        isLoadingSecondaryAccounts ||
                        isReloadingSecondaryAccounts
                    ) &&
                        !failedToGetSecondaryAccounts &&
                        secondaryAccountsOfCurrentMember.length === 0 && (
                            <>
                                {requestToken ? (
                                    <Button
                                        className="ml-2"
                                        variant="primary"
                                        size="sm"
                                        onClick={addAccount}
                                        disabled={
                                            isAdding ||
                                            (!secondaryOtp &&
                                                !skipSecondaryVerification)
                                        }
                                    >
                                        {isAdding
                                            ? "Adding Account..."
                                            : "Add Account"}
                                    </Button>
                                ) : (
                                    <Button
                                        className="ml-2"
                                        variant="primary"
                                        size="sm"
                                        onClick={requestSecondaryToken}
                                        disabled={isRequesting}
                                    >
                                        {isRequesting
                                            ? "Requesting Tokens..."
                                            : "Request Tokens"}
                                    </Button>
                                )}
                            </>
                        )}
                </div>
            </Modal.Footer>
        </Modal>
    );
};

AccountDetails.defaultProps = {
    member: {},
    show: false,
    parentId: "",
    onHide: () => {},
    loadSecondaryAccounts: () => {},
};

AccountDetails.propTypes = {
    member: PropTypes.object,
    show: PropTypes.bool,
    parentId: PropTypes.string,
    onHide: PropTypes.func,
    loadSecondaryAccounts: PropTypes.func,
};

export default AccountDetails;
