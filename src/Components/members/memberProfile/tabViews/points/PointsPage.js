import React, { useCallback, useContext, useMemo, useState } from "react";
import PropTypes from "prop-types";
import { Tab, Tabs } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    TransactionTypes,
} from "Data";
import { toTitleCase } from "Utils";
import PredefinedDateRangeSelector from "Components/common/predefinedDateRangeSelector/PredefinedDateRangeSelector";
import Points from "./Points";

const PointsPage = ({ memberId, rewards, setIsDisabledTab }) => {
    const { isAuthorizedForAction = () => {} } = useContext(UserContext);
    const [tab, setTab] = useState(TransactionTypes.COLLECTION);
    const [isLoadingSubTab, setIsLoadingSubTab] = useState(false);
    const [selectedPeriod, setSelectedPeriod] = useState("7 Days");
    const [showCustomDatePicker, setShowCustomDatePicker] = useState(false);
    const [customFromDate, setCustomFromDate] = useState("");
    const [customToDate, setCustomToDate] = useState("");

    const partnerRewardRedemptionsTab = useMemo(() => {
        if (
            !isAuthorizedForAction(
                AccessPermissionModuleNames.REWARD,
                AccessPermissionModules[AccessPermissionModuleNames.REWARD]
                    .actions.ListRewardRedemptionLogs
            ) ||
            rewards?.length === 0
        )
            return null;

        return rewards?.map((item) => (
            <Tab
                key={item._id}
                eventKey={item.name}
                title={item.name ? toTitleCase(item.name) : "Partner Reward"}
                disabled={isLoadingSubTab}
            >
                {tab === item.name && (
                    <Points
                        memberId={memberId}
                        rewardId={item._id}
                        tab={item.name}
                        selectedPeriod={selectedPeriod}
                        customFromDate={customFromDate}
                        customToDate={customToDate}
                        setIsDisabledTab={setIsDisabledTab}
                        setIsLoadingSubTab={setIsLoadingSubTab}
                    />
                )}
            </Tab>
        ));
    }, [
        memberId,
        rewards,
        isAuthorizedForAction,
        tab,
        selectedPeriod,
        customFromDate,
        customToDate,
        isLoadingSubTab,
        setIsDisabledTab,
        setIsLoadingSubTab,
    ]);

    const onSelectDatePeriod = useCallback(
        (e) => {
            if (selectedPeriod !== "Custom") {
                setCustomFromDate("");
                setCustomToDate("");
            }
            if (e === "Custom") setShowCustomDatePicker(true);

            setSelectedPeriod(e);
        },
        [
            selectedPeriod,
            setSelectedPeriod,
            setShowCustomDatePicker,
            setCustomFromDate,
            setCustomToDate,
        ]
    );

    return (
        <div>
            {isAuthorizedForAction(
                AccessPermissionModuleNames.TRANSACTION,
                AccessPermissionModules[AccessPermissionModuleNames.TRANSACTION]
                    .actions.ListTransactions
            ) ||
            isAuthorizedForAction(
                AccessPermissionModuleNames.REWARD,
                AccessPermissionModules[AccessPermissionModuleNames.REWARD]
                    .actions.ListRewardRedemptionLogs
            ) ? (
                <>
                    <PredefinedDateRangeSelector
                        selectedPeriod={selectedPeriod}
                        isLoading={isLoadingSubTab}
                        showCustomDatePicker={showCustomDatePicker}
                        customFromDate={customFromDate}
                        customToDate={customToDate}
                        onSelectDatePeriod={onSelectDatePeriod}
                        setCustomFromDate={setCustomFromDate}
                        setCustomToDate={setCustomToDate}
                    />
                    <hr />
                    <div className="mt-3">
                        <Tabs
                            transition={false}
                            id="noanim-tab-example"
                            className="border-solid-bottom"
                            activeKey={tab}
                            onSelect={setTab}
                        >
                            {isAuthorizedForAction(
                AccessPermissionModuleNames.TRANSACTION,
                AccessPermissionModules[AccessPermissionModuleNames.TRANSACTION]
                    .actions.ListTransactions
            ) ? (
                                <Tab
                                    eventKey={TransactionTypes.COLLECTION}
                                    title="Point Collection"
                                    disabled={isLoadingSubTab}
                                >
                                    {tab === TransactionTypes.COLLECTION && (
                                        <Points
                                            memberId={memberId}
                                            tab={TransactionTypes.COLLECTION}
                                            selectedPeriod={selectedPeriod}
                                            customFromDate={customFromDate}
                                            customToDate={customToDate}
                                            setIsDisabledTab={setIsDisabledTab}
                                            setIsLoadingSubTab={
                                                setIsLoadingSubTab
                                            }
                                        />
                                    )}
                                </Tab>
                            ) : null}
                           {isAuthorizedForAction(
                AccessPermissionModuleNames.TRANSACTION,
                AccessPermissionModules[AccessPermissionModuleNames.TRANSACTION]
                    .actions.ListTransactions
            ) ? (
                                <Tab
                                    eventKey={TransactionTypes.REDEMPTION}
                                    title="Point Redemption"
                                    disabled={isLoadingSubTab}
                                >
                                    {tab === TransactionTypes.REDEMPTION && (
                                        <Points
                                            memberId={memberId}
                                            tab={TransactionTypes.REDEMPTION}
                                            selectedPeriod={selectedPeriod}
                                            customFromDate={customFromDate}
                                            customToDate={customToDate}
                                            setIsDisabledTab={setIsDisabledTab}
                                            setIsLoadingSubTab={
                                                setIsLoadingSubTab
                                            }
                                        />
                                    )}
                                </Tab>
                            ) : null}
                            {isAuthorizedForAction(
                AccessPermissionModuleNames.TRANSACTION,
                AccessPermissionModules[AccessPermissionModuleNames.TRANSACTION]
                    .actions.ListTransactions
            ) ? (
                                <Tab
                                    eventKey={TransactionTypes.ADJUSTMENT}
                                    title="Point Adjustment"
                                    disabled={isLoadingSubTab}
                                >
                                    {tab === TransactionTypes.ADJUSTMENT && (
                                        <Points
                                            memberId={memberId}
                                            tab={TransactionTypes.ADJUSTMENT}
                                            selectedPeriod={selectedPeriod}
                                            customFromDate={customFromDate}
                                            customToDate={customToDate}
                                            setIsDisabledTab={setIsDisabledTab}
                                            setIsLoadingSubTab={
                                                setIsLoadingSubTab
                                            }
                                        />
                                    )}
                                </Tab>
                            ) : null}
                            {partnerRewardRedemptionsTab}
                        </Tabs>
                    </div>
                </>
            ) : (
                <h3 className="text-danger text-center">
                    You are not authorized to view this content!
                </h3>
            )}
        </div>
    );
};

PointsPage.defaultProps = {
    memberId: "",
    rewards: [],
    setIsDisabledTab: () => {},
};

PointsPage.propTypes = {
    memberId: PropTypes.string,
    rewards: PropTypes.array,
    setIsDisabledTab: PropTypes.func,
};

export default PointsPage;
