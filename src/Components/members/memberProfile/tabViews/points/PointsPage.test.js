import React from "react";
import { create } from "react-test-renderer";
import { DataContext, UserContext } from "Contexts";
import PointsPage from "./PointsPage";

const regionId = "6128e3537ed841e246e2e396";
const merchants = [
    {
        _id: "619541b1423d24e396dbf07e",
        organizationId: "6128e3537ed841e246e2e394",
        regionId: "6137a9fff48b8eb1845c78cf",
        merchantName: "Price's Electronics",
        merchantLogoImageUrl:
            "https://api.loyaltybeta.cxforge.com/api/coreservice/files/52b15b00-47cf-11ec-8acd-ddef44280f8a.jpeg",
        contact: {
            name: "Price's Electronics",
            mobileNumber: "***********",
            email: "<EMAIL>",
            address: {
                line1: "21",
                line3: "12",
                city: "Greenland",
                stateOrProvince: "Saint Andrew",
            },
        },
        status: "ACTIVE",
        type: "EXTERNAL",
        locationsCount: 4,
        countryName: "Barbados",
        businessRegistrationNumber: "102360023",
        options: {
            enroll: true,
            earn: true,
            redeemPoints: true,
            redeemRewards: true,
            refund: true,
            void: true,
            claimReward: false,
        },
        createdBy: "616680f35958bc8372a1ee88",
        billingContacts: [
            {
                name: "Gongalegoda Banda",
                mobileNumber: "250234",
                email: "<EMAIL>",
                address: {
                    line1: "1234",
                    line2: "1234",
                    line3: "1234",
                    city: "black heart",
                    stateOrProvince: "black heart SP",
                    zipOrPostcode: "12345432",
                },
            },
        ],
        technicalContacts: [
            {
                name: "Gongalegoda Banda",
                mobileNumber: "250234",
                email: "<EMAIL>",
                address: {
                    line1: "1234",
                    line2: "1234",
                    line3: "1234",
                    city: "black heart",
                    stateOrProvince: "black heart SP",
                    zipOrPostcode: "12345432",
                },
            },
        ],
        createdOn: "2021-11-17T17:53:53.763Z",
        updatedOn: "2022-04-11T06:57:00.785Z",
        __v: 0,
        updatedBy: "616680f35958bc8372a1ee88",
        visibleForNearestLocations: true,
        countryISO2Code: "BB",
    },
];

const rewards = [
    {
        _id: "61958c9059938d8c20b956c3",
        organizationId: "6128e3537ed841e246e2e394",
        regionId: "6137a9fff48b8eb1845c78cf",
        name: "Caribbean Miles",
        description:
            "Redeem your Points for Caribbean Miles and fly to any of Caribbean Airlines destinations. \n250 Points = 500 Caribbean Miles.",
        type: "TANGIBLE",
        subType: "PARTNER",
        imageUrls: [
            "https://d1umi9h6hi9bqe.cloudfront.net/images/989b04f0-83dd-11ec-835e-876e8cbb6c3c.png",
        ],
        pointValueType: "BUNDLE",
        pointsBundles: [
            {
                points: 15,
                bundleName: "30 CAL Miles",
                bundleValue: 30,
                _id: "6374cb5969ca7e559af52362",
            },
            {
                points: 25,
                bundleName: "50 CAL Miles",
                bundleValue: 50,
                _id: "6374cb5969ca7e559af52363",
            },
            {
                points: 50,
                bundleName: "100 CAL Miles",
                bundleValue: 100,
                _id: "6374cb5969ca7e559af52364",
            },
            {
                points: 100,
                bundleName: "200 CAL Miles",
                bundleValue: 200,
                _id: "6374cb5969ca7e559af52365",
            },
            {
                points: 150,
                bundleName: "300 CAL Miles",
                bundleValue: 300,
                _id: "6374cb5969ca7e559af52366",
            },
            {
                points: 200,
                bundleName: "400 CAL Miles",
                bundleValue: 400,
                _id: "6374cb5969ca7e559af52367",
            },
            {
                points: 250,
                bundleName: "500 CAL Miles",
                bundleValue: 500,
                _id: "6374cb5969ca7e559af52368",
            },
            {
                points: 500,
                bundleName: "1000 CAL Miles",
                bundleValue: 1000,
                _id: "6374cb5969ca7e559af52369",
            },
        ],
        validityPeriod: "FIXED",
        validFrom: "2021-10-29T00:00:00.000Z",
        validTo: "2025-12-29T00:00:00.000Z",
        dailyRedemptionLimit: "UNLIMITED",
        dailyRedemptionAmount: 0,
        createdBy: "613dfc56eac5369848619fe0",
        status: "ENABLED",
        rewardMetadata: {
            allowAllClaimLocations: true,
            claimLocations: [],
            _id: "61958c9059938d8c20b956c6",
        },
        portalVisibility: "NONE",
        redemptionSteps: [
            {
                step: 1,
                details:
                    "Share your points with your loved ones as a gift.Now you can transfer you loyalty points to your loyalty points to your family and friends who use Massy Loyalty Program members",
            },
            {
                step: 2,
                details:
                    "Share your points with your loved ones as a gift.Now you can transfer you loyalty points to your loyalty points to your family and friends who use Massy the Loyalty Program members",
            },
        ],
        partnerRewardMetadata: {
            partnerRewardConfig: "CAL_MILES_CONFIGS",
            customFileName: "handback file",
            partnerContactNumber: ["18684567880"],
            partnerBundleUnitOfMeasure: "Miles",
        },
        totalCount: 0,
        usedCount: 175,
        claimedCount: 0,
        createdOn: "2021-11-17T23:13:20.397Z",
        updatedOn: "2024-01-18T11:43:45.393Z",
        __v: 0,
        updatedBy: "616680f35958bc8372a1ee88",
        invalidatedCount: 1,
    },
];

describe("PointsPage component snapshot", () => {
    const setIsDisabledTab = jest.fn();

    const props = {
        memberId: "616680f35958bc8372a1ee32",
        rewards,
        setIsDisabledTab,
    };

    test("Matches the snapshot", () => {
        const component = create(
            <UserContext.Provider value={regionId}>
                <DataContext.Provider value={{ merchants }}>
                    <PointsPage props={props} />
                </DataContext.Provider>
            </UserContext.Provider>
        );
        expect(component.toJSON()).toMatchSnapshot();
    });
});
