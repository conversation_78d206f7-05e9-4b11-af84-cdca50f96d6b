import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    FormSearch,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faFilter, faFilterSlash, faSync } from "FaICIconMap";
import { DataContext, UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    PredefinedDatePeriods,
    RewardSubType,
    TransactionTypes,
} from "Data";
import { useToggle } from "Hooks";
import {
    getTransactions,
    getRedemptionLogs,
    getTransactionsCount,
} from "Services";
import { getDefaultValuesOfFilters, getQueryFilters, toTitleCase } from "Utils";
import QueryParamFilters from "Components/common/queryParamFilters/QueryParamFilters";
import SizePerPageRenderer from "Components/utils/table/sizePerPageRenderer/SizePerPageRenderer";
import PointsTable from "./PointsTable";

const PointsRelatedTabs = Object.keys(TransactionTypes);

const transactionFilters = [
    { label: "Merchant", value: "merchantId" },
    { label: "Merchant Location", value: "merchantLocationId" },
];

const inputKeys = [
    { key: "select", values: ["merchantId", "merchantLocationId"] },
];

const defaultLimit = 25,
    defaultSkip = 1;
let searchStateUpdateTimeout;

const Points = ({
    memberId,
    tab,
    rewardId,
    selectedPeriod,
    customFromDate,
    customToDate,
    setIsDisabledTab,
    setIsLoadingSubTab,
}) => {
    const { selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const {
        allMerchantsForDropdown = [],
        allMerchantLocationsForDropdown = [],
    } = useContext(DataContext);
    const [transactions, setTransactions] = useState([]);
    const [transactionCount, setTransactionCount] = useState(0);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [isLoading, setIsLoading] = useState(false);
    const [searchText, setSearchText] = useState("");
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [isReloading, setIsReloading] = useState(false);
    const [appliedFilterRows, setAppliedFilterRows] = useState([]);
    const [appliedFilters, setAppliedFilters] = useState([]);

    const loadPointsTransctions = useCallback(
        async (
            {
                limit,
                skip,
                transactionType,
                selectedPeriod = "",
                searchText = "",
                filters = [],
            },
            reloadCount = true
        ) => {
            try {
                if (
                    !isAuthorizedForAction(
                        AccessPermissionModuleNames.TRANSACTION,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.TRANSACTION
                        ].actions.ListTransactions
                    )
                ) {
                    throw new Error(
                        "You are unauthorized to view points related transactions"
                    );
                }

                let queryObj = {
                    regionId: selectedRegion?._id,
                    memberId,
                    transactionType,
                    transactionOnFromDate:
                        selectedPeriod !== "Custom"
                            ? PredefinedDatePeriods[selectedPeriod]?.fromDate
                            : customFromDate,
                    transactionOnToDate:
                        selectedPeriod !== "Custom"
                            ? PredefinedDatePeriods[selectedPeriod]?.toDate
                            : customToDate,
                    searchKey: searchText,
                };

                if (filters.length !== 0) {
                    queryObj = { ...queryObj, ...getQueryFilters(filters) };
                }

                const promises = [
                    (async () => {
                        const transactions = await getTransactions(
                            queryObj,
                            limit,
                            (skip - 1) * limit
                        );
                        setTransactions(transactions.items);
                        return Promise.resolve();
                    })(),
                ];
                if (reloadCount) {
                    promises.push(
                        (async () => {
                            setTransactionCount(0);
                            const transactionCountResponse =
                                await getTransactionsCount(queryObj);

                            setTransactionCount(transactionCountResponse.count);
                            return Promise.resolve();
                        })()
                    );
                }
                await Promise.all(promises);
            } catch (error) {
                return Promise.reject(error);
            }
        },
        [
            memberId,
            customFromDate,
            customToDate,
            isAuthorizedForAction,
            selectedRegion?._id,
            setTransactions,
            setTransactionCount,
        ]
    );

    const loadPartnerRewardTransactions = useCallback(
        async ({ limit, skip, selectedPeriod = "", searchText = "" }) => {
            try {
                if (!rewardId)
                    throw new Error("Partner reward id cannot be empty");

                if (
                    !isAuthorizedForAction(
                        AccessPermissionModuleNames.REWARD,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.REWARD
                        ].actions.ListRewardRedemptionLogs
                    )
                ) {
                    throw new Error(
                        "You are unauthorized to view partner reward related transactions"
                    );
                }
                setTransactionCount(0);

                const response = await getRedemptionLogs({
                    limit,
                    skip: (skip - 1) * limit,
                    regionId: selectedRegion?._id,
                    memberId,
                    rewardId,
                    rewardSubType: RewardSubType.PARTNER,
                    fromDate:
                        selectedPeriod !== "Custom"
                            ? PredefinedDatePeriods[selectedPeriod]?.fromDate
                            : customFromDate,
                    toDate:
                        selectedPeriod !== "Custom"
                            ? PredefinedDatePeriods[selectedPeriod]?.toDate
                            : customToDate,
                    searchKey: searchText,
                });
                setTransactions(response.items);
                setTransactionCount(response.total);
            } catch (error) {
                return Promise.reject(error);
            }
        },
        [
            rewardId,
            memberId,
            customFromDate,
            customToDate,
            isAuthorizedForAction,
            selectedRegion?._id,
            setTransactions,
            setTransactionCount,
        ]
    );

    const loadTransactions = useCallback(
        async (
            {
                limit,
                skip,
                transactionType,
                selectedPeriod = "",
                searchText = "",
                filters = [],
            },
            reloadCount = true
        ) => {
            try {
                setIsDisabledTab(true);
                setIsLoadingSubTab(true);
                setIsLoading(true);

                if (~PointsRelatedTabs.indexOf(tab)) {
                    await loadPointsTransctions(
                        {
                            limit,
                            skip,
                            transactionType,
                            selectedPeriod,
                            searchText,
                            filters,
                        },
                        reloadCount
                    );
                } else {
                    await loadPartnerRewardTransactions({
                        limit,
                        skip,
                        selectedPeriod,
                        searchText,
                    });
                }
            } catch (error) {
                console.error(error);
                toast.error(
                    <div>
                        {`Failed to load ${
                            tab && ~PointsRelatedTabs.indexOf(tab)
                                ? `"Point ` + toTitleCase(tab) + `" `
                                : `"` + toTitleCase(tab) + `" `
                        }transactions!`}
                        <br />
                        {error.message
                            ? `Error: ${error.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setIsDisabledTab(false);
                setIsLoadingSubTab(false);
                setIsLoading(false);
            }
        },
        [
            tab,
            setIsDisabledTab,
            setIsLoadingSubTab,
            setIsLoading,
            loadPointsTransctions,
            loadPartnerRewardTransactions,
        ]
    );

    const onSearch = useCallback(
        (searchText) => {
            if (searchStateUpdateTimeout) {
                clearTimeout(searchStateUpdateTimeout);
            }
            setSearchText(searchText);

            searchStateUpdateTimeout = setTimeout(async () => {
                loadTransactions(
                    {
                        limit,
                        skip: defaultSkip,
                        transactionType: tab,
                        selectedPeriod,
                        searchText,
                        filters: appliedFilters,
                    },
                    true
                );
            }, 2000);
            setSkip(defaultSkip);
        },
        [
            tab,
            selectedPeriod,
            limit,
            appliedFilters,
            loadTransactions,
            setSearchText,
            setSkip,
        ]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadTransactions(
                {
                    limit,
                    skip: newSkip,
                    transactionType: tab,
                    selectedPeriod,
                    searchText,
                    filters: appliedFilters,
                },
                false
            );
        },
        [
            tab,
            selectedPeriod,
            limit,
            searchText,
            appliedFilters,
            loadTransactions,
            setSkip,
        ]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadTransactions(
                {
                    limit: newLimit,
                    skip: defaultSkip,
                    transactionType: tab,
                    selectedPeriod,
                    searchText,
                    filters: appliedFilters,
                },
                false
            );
        },
        [
            tab,
            selectedPeriod,
            searchText,
            appliedFilters,
            loadTransactions,
            setSkip,
            setLimit,
        ]
    );

    const sizePerPageRenderer = useCallback(
        (props) => (
            <SizePerPageRenderer
                {...props}
                disabled={
                    isLoading ||
                    (selectedPeriod === "Custom" &&
                        !customFromDate &&
                        !customToDate)
                }
            />
        ),
        [selectedPeriod, customFromDate, customToDate, isLoading]
    );

    const options = {
        page: skip,
        sizePerPage: limit,
        totalSize: transactionCount,
        paginationSize: 5,
        pageStartIndex: 1,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageList: [
            { text: "25", value: 25 },
            { text: "50", value: 50 },
            { text: "100", value: 100 },
        ],
        sizePerPageRenderer,
        onPageChange: onChangePagination,
        onSizePerPageChange: onChangePageSize,
    };

    const onReloadTransactions = useCallback(async () => {
        setIsReloading(true);
        await loadTransactions({
            limit,
            skip: defaultSkip,
            transactionType: tab,
            selectedPeriod,
            searchText,
            filters: appliedFilters,
        });
        setSkip(defaultSkip);
        setIsReloading(false);
    }, [
        tab,
        selectedPeriod,
        limit,
        searchText,
        appliedFilters,
        loadTransactions,
        setSkip,
        setIsReloading,
    ]);

    const getQueryParamData = useCallback(
        (arrayToReduce) =>
            arrayToReduce.reduce((result, item) => {
                const filterInput =
                    inputKeys.find((iK) => {
                        if (iK.key === "date-range") {
                            return Object.keys(iK.values).includes(item.value);
                        } else {
                            return iK.values.includes(item.value);
                        }
                    })?.key || "";
                let options = [];
                let labelKey = "label";
                let valueKey = "value";
                let groupBy = "";
                let placeholder = "";

                const defaultValues = getDefaultValuesOfFilters(
                    { filterInput, filterKey: item.value },
                    inputKeys
                );

                if (filterInput === "select") {
                    switch (item.value) {
                        case "merchantId":
                            options = allMerchantsForDropdown;
                            labelKey = "merchantName";
                            valueKey = "_id";
                            break;
                        case "merchantLocationId":
                            options = allMerchantLocationsForDropdown;
                            labelKey = "locationName";
                            valueKey = "_id";
                            groupBy = "merchantName";
                            break;
                        default:
                            break;
                    }
                }

                result[item.value] = {
                    filterInput,
                    key: item.value,
                    id: item.value,
                    name: item.value,
                    valueKey,
                    placeholder:
                        placeholder || item?.label?.toLowerCase() || "",
                    ...defaultValues,
                    ...(filterInput === "select"
                        ? { options, labelKey, groupBy }
                        : {}),
                };

                return result;
            }, {}),
        [allMerchantsForDropdown, allMerchantLocationsForDropdown]
    );

    const tabQueryFilterData = useMemo(
        () => ({
            queryParamFilterOptions: transactionFilters,
            queryParamFilterMetadata: getQueryParamData(transactionFilters),
        }),
        [getQueryParamData]
    );

    useEffect(() => {
        if (
            selectedPeriod !== "Custom" ||
            (selectedPeriod === "Custom" && customFromDate && customToDate)
        ) {
            loadTransactions({
                limit,
                skip: defaultSkip,
                transactionType: tab,
                selectedPeriod,
                searchText,
                filters: appliedFilters,
            });
        } else {
            setTransactions([]);
            setTransactionCount(0);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tab, selectedPeriod, customFromDate, customToDate, appliedFilters]);

    useEffect(() => {
        if (selectedPeriod === "Custom" && showFilters)
            toggleShowFilters(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedPeriod || showFilters]);

    return (
        <div>
            <div className="my-3 d-flex justify-content-between">
                <div className="w-75 d-flex justify-content-start align-items-center">
                    {~PointsRelatedTabs.indexOf(tab) ? (
                        <div className="search-bar-width-sm mr-2">
                            <FormSearch
                                id="search-by-card-number"
                                placeholder="Search by card number..."
                                selected={searchText}
                                disabled={
                                    isLoading ||
                                    (selectedPeriod === "Custom" &&
                                        !customFromDate &&
                                        !customToDate)
                                }
                                onChange={
                                    selectedPeriod === "Custom" &&
                                    !customFromDate &&
                                    !customToDate
                                        ? () => {}
                                        : onSearch
                                }
                            />
                        </div>
                    ) : null}
                    <div className="w-50 d-flex justify-content-start align-items-center">
                        <Button
                            className="p-0 shadow-none"
                            variant="link"
                            size="sm"
                            disabled={
                                isLoading ||
                                isReloading ||
                                (selectedPeriod === "Custom" &&
                                    !customFromDate &&
                                    !customToDate)
                            }
                            onClick={onReloadTransactions}
                        >
                            {!isReloading && (
                                <div className="d-flex align-items-center">
                                    <IcIcon
                                        size="md"
                                        className="mr-2"
                                        icon={faSync}
                                    />
                                    {`Reload ${
                                        tab ? toTitleCase(tab) : ""
                                    } Transactions`}
                                </div>
                            )}
                        </Button>
                        <div style={{ fontSize: "0.9rem" }}>
                            {isReloading && (
                                <div className="text-primary">Reloading...</div>
                            )}
                        </div>
                    </div>
                </div>
                {~PointsRelatedTabs.indexOf(tab) ? (
                    <div>
                        <Button
                            variant={`${!showFilters ? "outline-" : ""}primary`}
                            size="sm"
                            disabled={
                                isLoading ||
                                isReloading ||
                                (selectedPeriod === "Custom" &&
                                    !customFromDate &&
                                    !customToDate)
                            }
                            onClick={toggleShowFilters}
                        >
                            <IcIcon
                                className="mr-2"
                                size="lg"
                                icon={showFilters ? faFilterSlash : faFilter}
                            />
                            {showFilters ? "Hide Filters" : "Filter By"}
                        </Button>
                    </div>
                ) : null}
            </div>
            {~PointsRelatedTabs.indexOf(tab) ? (
                <div className="mt-3">
                    {!showFilters && appliedFilters.length !== 0 && (
                        <div className="d-flex align-items-center">
                            <h3 className="mb-0 mr-2">
                                {appliedFilters.length === 1
                                    ? "A filter is "
                                    : appliedFilters.length + " filters are "}
                                applied.
                            </h3>
                            <Button
                                variant="info"
                                size="sm"
                                disabled={
                                    isLoading ||
                                    isReloading ||
                                    (selectedPeriod === "Custom" &&
                                        !customFromDate &&
                                        !customToDate)
                                }
                                onClick={toggleShowFilters}
                            >
                                Show Applied Filters
                            </Button>
                        </div>
                    )}
                    {showFilters && (
                        <QueryParamFilters
                            multipleFilters
                            queryParamFilterMetadata={
                                tabQueryFilterData.queryParamFilterMetadata
                            }
                            queryParamFilterOptions={
                                tabQueryFilterData.queryParamFilterOptions
                            }
                            isLoading={isLoading}
                            appliedFilters={appliedFilters}
                            appliedFilterRows={appliedFilterRows}
                            setAppliedFilters={setAppliedFilters}
                            setAppliedFilterRows={setAppliedFilterRows}
                        />
                    )}
                </div>
            ) : null}
            <hr />
            <PointsTable
                data={transactions}
                tableOptions={options}
                isLoading={isLoading}
                tab={tab}
                selectedPeriod={selectedPeriod}
                customFromDate={customFromDate}
                customToDate={customToDate}
            />
        </div>
    );
};

Points.defaultProps = {
    memberId: "",
    tab: TransactionTypes.COLLECTION,
    rewardId: "",
    selectedPeriod: PredefinedDatePeriods["7 Days"],
    customFromDate: "",
    customToDate: "",
    setIsDisabledTab: () => {},
    setIsLoadingSubTab: () => {},
};

Points.propTypes = {
    memberId: PropTypes.string,
    tab: PropTypes.string,
    rewardId: PropTypes.string,
    selectedPeriod: PropTypes.string,
    customFromDate: PropTypes.string,
    customToDate: PropTypes.string,
    setIsDisabledTab: PropTypes.func,
    setIsLoadingSubTab: PropTypes.func,
};

export default Points;
