import React, { useContext, useEffect, useState } from "react";
import { Card,IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faHistory } from "../../../../../FaICIconMap";
import { formatToCommonReadableFormat } from "../../../../../Utils";
import { DataContext } from "../../../../../Contexts";
import "./HistoryEvents.scss";

const HistoryEvents=({historyEvents})=>{
    const {usersList} = useContext(DataContext);
    const [usersListObj, setUsersListObj] = useState({});
    useEffect(() => {
       if(usersList&&Object.keys(usersList).length!==0){
           const usersListObj ={};
           usersList.forEach((user)=>{
               usersListObj[`${user?._id}`]=user.hasOwnProperty("userData")?user?.userData?.firstName+" "+user?.userData?.lastName:"-"
           });
           setUsersListObj(usersListObj);
       }
    }, [usersList]);
    return(
        <>
            <div className="history_events px-3">
                {historyEvents && historyEvents.length > 0 ? historyEvents.map(history => {
                        return(
                            <Card className="my-3 p-2" key={history?._id}>
                                <Card.Header className="p-3 font-weight-bold d-flex justify-content-between align-items-center">
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            className="mr-3 text-primary"
                                            size="lg"
                                            icon={faHistory}
                                        />
                                        History Event
                                    </div>
                                    {history?.eventDate ? formatToCommonReadableFormat(history?.eventDate) : "~ date unknown"}
                                </Card.Header>
                                <Card.Body className="p-2">
                                    {
                                      history.hasOwnProperty('eventBy')&& <>
                                            <div
                                                className="d-flex flex-column w-100 py-3 px-3 border-bottom">
                                                <div className="d-flex justify-content-between">
                                                    <div
                                                        className="text-muted">User Name
                                                    </div>
                                                    <div>{usersListObj[history?.eventBy]}</div>
                                                </div>
                                            </div>
                                            <div
                                                className="d-flex flex-column w-100 py-3 px-3 border-bottom">
                                                <div className="d-flex justify-content-between">
                                                    <div
                                                        className="text-muted">User Id
                                                    </div>
                                                    <div>{history?.eventBy|| "-"}</div>
                                                </div>
                                            </div>
                                        </>
                                    }
                                    {
                                        history.hasOwnProperty('eventDetails')&& <>
                                            <div
                                                className="d-flex flex-column w-100 py-3 px-3">
                                                <div className="d-flex justify-content-between">
                                                    <div
                                                        className="text-muted">Event Details</div>
                                                    <div>{history?.eventDetails}</div>
                                                </div>
                                            </div>
                                        </>
                                    }
                                </Card.Body>
                            </Card>
                        )
                    })
                    :
                    <div className="mx-auto my-4">
                        <p className="no-rewards-text text-center"> No history events found.</p>
                    </div>}
            </div>
        </>
    )
}

export default HistoryEvents;