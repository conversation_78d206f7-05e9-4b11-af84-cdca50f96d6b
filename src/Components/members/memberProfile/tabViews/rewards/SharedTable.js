import React, {
    use<PERSON><PERSON>back,
    useMemo,
    useState,
    useContext,
    useEffect,
} from "react";
import { toast } from "react-toastify";
import { useHistory } from "react-router";
import paginationFactory, {
    PaginationProvider,
} from "react-bootstrap-table2-paginator";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import moment from "moment";
import PropTypes from "prop-types";
import {
    BootstrapTable,
    Badge,
    Button,
    Row,
    Col,
    FormSearch,
    Image,
    DropdownItem,
    Dropdown,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import {
    RewardStatus,
    ValidityPeriod,
    RewardValidity,
    RewardSubType,
    RewardGenerationJobStatusColorCode,
    RedemptionStatus,
    RewardGenerationJobStatus,
    AccessPermissionModuleNames,
    AccessPermissionModules,
    RewardType,
} from "Data";
import { getRewards, getRedemptionLogs, getMemberById } from "Services";
import { toTitleCase, toTitleCaseFromCamelCase } from "Utils";
import RewardValidityDetails from "Components/common/rewardValidityDetails/RewardValidityDetails";
import { BootstrapTableOverlay } from "Components/utils/UtilComponents";
import SizePerPageRenderer from "Components/utils/table/sizePerPageRenderer/SizePerPageRenderer";
import ConfirmationModal from "./confirmationModal/ConfirmationModal";
import { RewardTabs } from "./MemberRewardsData";
import RedeemedRewardsActions from "./RedeemedRewardsActions";

const RewardsTabs = [RewardTabs.LOCKED, RewardTabs.UNLOCKED];
const RedemptionTabs = [RewardTabs.CLAIMED, RewardTabs.REDEEMED];
const ViewRewardStates = [RewardValidity.SCHEDULED, RewardValidity.EXPIRED];

const RedeemedRewardStatuses = Object.values(RedemptionStatus).filter(
    (status) => status !== RedemptionStatus.CLAIMED
);

const UnlockedRewardsColumns = [
    { name: "rewardName", headerStyle: { width: "25%" } },
    { name: "rewardValidity", headerStyle: { width: "23%" } },
    { name: "points", headerStyle: { width: "10%" } },
    { name: "type" },
    { name: "", headerStyle: { width: "25%" } },
];

const LockedRewardColumns = [
    { name: "rewardName", headerStyle: { width: "30%" } },
    { name: "rewardValidity" },
    { name: "points" },
    { name: "type" },
];

const ClaimedRewardsColumns = [
    { name: "rewardName" },
    { name: "redeemedDate" },
    { name: "points", headerStyle: { width: "10%" } },
    { name: "location" },
];

const RedeemedRewardsColumns = [
    { name: "rewardName", headerStyle: { width: "25%" } },
    { name: "points", headerStyle: { width: "10%" } },
    { name: "location" },
    { name: "status" },
    { name: "", headerStyle: { width: "25%" } },
];

const defaultColumnTemplate = ({ name, ...rest }) => ({
    dataField: name,
    text: <>{toTitleCaseFromCamelCase(name)}</>,
    sort: false,
    ...rest,
});

const NoData = ({ loading }) => {
    NoData.defaultProps = { loading: false };

    NoData.propTypes = { loading: PropTypes.bool };

    if (loading) return null;
    return <div>No rewards found.</div>;
};

const getRewardStatus = (processingStatus = "") => {
    switch (processingStatus) {
        case RewardGenerationJobStatus.COMPLETED:
            return "Available at store";
        case RewardGenerationJobStatus.FAILED:
            return "Cancelled";
        default:
            return processingStatus
                ? toTitleCase(processingStatus)
                : "~ unknown";
    }
};

const defaultSkip = 1;
let searchStateUpdateTimeout;

const SharedTable = ({
    tab,
    limit,
    skip,
    memberId,
    isLoading,
    isReloadingAfterRedeem,
    rewards,
    rewardsCount,
    availablePoints,
    setLimit,
    setSkip,
    setIsDisabledTab,
    setIsLoading,
    setIsReloadingAfterRedeem,
    setMember,
    setRewards,
    setRewardsCount,
}) => {
    const { selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const [showRedemptionModal, setShowRedemptionModal] = useState(false);
    const [selectedReward, setSelectedReward] = useState();
    const [currentRedemption, setCurrentRedemption] = useState();
    const [actionType, setActionType] = useState("");
    const [searchText, setSearchText] = useState("");
    const [showRedeemedActionModal, setShowRedeemedActionModal] =
        useState(false);
    const history = useHistory();

    const loadRewards = useCallback(
        async ({ limit, skip, searchText = "" }) => {
            const queryObj = {
                limit: limit,
                skip: (skip - 1) * limit,
                regionId: selectedRegion._id,
                status: RewardStatus.ENABLED,
                searchKey: searchText,
            };

            try {
                switch (tab) {
                    case RewardTabs.LOCKED: {
                        queryObj.pointsLowerMargin = availablePoints || 1;
                        break;
                    }
                    case RewardTabs.UNLOCKED: {
                        queryObj.pointsUpperMargin = availablePoints || 1;
                        break;
                    }
                    default:
                        return queryObj;
                }

                setIsDisabledTab(true);
                setIsLoading(true);
                const rewardResponse = await getRewards(queryObj);
                setRewards(rewardResponse.items);
                setRewardsCount(rewardResponse.total);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load rewards!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setIsDisabledTab(false);
                setIsLoading(false);
            }
        },
        [
            selectedRegion._id,
            tab,
            availablePoints,
            setIsDisabledTab,
            setRewards,
            setRewardsCount,
            setIsLoading,
        ]
    );

    const loadRedemptionLogs = useCallback(
        async ({ limit, skip }) => {
            let queryObj = {
                limit: limit,
                skip: (skip - 1) * limit,
                memberId: memberId,
                rewardSubType: RewardSubType.VOUCHER,
                regionId: selectedRegion._id,
                ...(tab === RewardTabs.CLAIMED
                    ? { status: RedemptionStatus.CLAIMED }
                    : { statusArray: RedeemedRewardStatuses }),
            };

            try {
                setIsDisabledTab(true);
                setIsLoading(true);
                const redemptionLogsResponse = await getRedemptionLogs(
                    queryObj
                );
                setRewards(redemptionLogsResponse.items);
                setRewardsCount(redemptionLogsResponse.total);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load reward redemptions!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setIsDisabledTab(false);
                setIsLoading(false);
            }
        },
        [
            selectedRegion._id,
            memberId,
            tab,
            setIsDisabledTab,
            setIsLoading,
            setRewards,
            setRewardsCount,
        ]
    );

    const onShowRedemptionModal = useCallback(
        (e) => {
            setSelectedReward(
                rewards.find(
                    (reward) => reward._id === e.currentTarget.dataset.id
                )
            );
            setShowRedemptionModal(true);
        },
        [rewards, setSelectedReward, setShowRedemptionModal]
    );

    const onHideRedemptionModal = useCallback(
        async (e, data) => {
            setShowRedemptionModal(false);
            setSelectedReward("");
            if (data) {
                setIsReloadingAfterRedeem(true);
                await loadRewards({ limit, skip });
                const memberProfileResponse = await getMemberById(memberId);
                setMember(memberProfileResponse);
                setIsReloadingAfterRedeem(false);
            }
        },
        [
            limit,
            skip,
            memberId,
            setSelectedReward,
            setIsReloadingAfterRedeem,
            setMember,
            loadRewards,
        ]
    );

    const viewReward = useCallback(
        (e) =>
            history.push(`/redemptions/rewards/${e.currentTarget.dataset.id}`),
        [history]
    );

    const onShowRedeemedActionModal = useCallback(
        (e) => {
            try {
                const selectedReward = rewards.find(
                    (reward) => reward._id === e.currentTarget.dataset.id
                );
                if (!selectedReward) {
                    throw new Error(
                        `Record not found for the id - ${e.currentTarget.dataset.id}`
                    );
                }
                setCurrentRedemption(selectedReward);
                setActionType(e.currentTarget.name);
                setShowRedeemedActionModal(true);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to show redeem action modal!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            rewards,
            setCurrentRedemption,
            setActionType,
            setShowRedeemedActionModal,
        ]
    );

    const onHideRedeemedActionModal = useCallback(
        (e, data) => {
            if (data) {
                loadRedemptionLogs({ limit, skip });
            }
            setCurrentRedemption();
            setActionType("");
            setShowRedeemedActionModal(false);
        },
        [
            limit,
            skip,
            setCurrentRedemption,
            setActionType,
            setShowRedeemedActionModal,
            loadRedemptionLogs,
        ]
    );

    const columns = useMemo(() => {
        const columns = [];

        switch (tab) {
            case RewardTabs.UNLOCKED:
                UnlockedRewardsColumns.forEach((item) => {
                    columns.push(defaultColumnTemplate(item));
                });
                break;
            case RewardTabs.REDEEMED:
                RedeemedRewardsColumns.forEach((item) => {
                    columns.push(defaultColumnTemplate(item));
                });
                break;
            case RewardTabs.CLAIMED:
                ClaimedRewardsColumns.forEach((item) => {
                    columns.push(defaultColumnTemplate(item));
                });
                break;
            case RewardTabs.LOCKED:
            default:
                LockedRewardColumns.forEach((item) => {
                    columns.push(defaultColumnTemplate(item));
                });
                break;
        }

        return columns.sort((a, b) => a.order - b.order);
    }, [tab]);

    const data = useMemo(
        () =>
            rewards.map((reward) => {
                const disableCancelRedemptionStatuses = [
                    RewardGenerationJobStatus.COMPLETED,
                    RewardGenerationJobStatus.FAILED,
                ];
                const isRewardImg = ~RewardsTabs.indexOf(tab)
                    ? reward?.imageUrls.length !== 0 &&
                        reward?.imageUrls[0] !== ""
                    : reward?.reward?.imageUrls.length !== 0 &&
                        reward?.reward?.imageUrls[0] !== "";
                let rewardValidityState;
                let pickupLocation;
                let remainingCount;

                if (reward?.totalCount >= 0 && reward?.usedCount >= 0) {
                    if (reward.totalCount === 0) {
                        remainingCount = 0;
                    } else {
                        remainingCount = reward.totalCount - reward.usedCount;
                    }
                }

                switch (reward?.validityPeriod) {
                    case ValidityPeriod.OPEN:
                        rewardValidityState = RewardValidity.NO_EXPIRY;
                        break;
                    case ValidityPeriod.FIXED:
                        if (
                            moment().isBetween(
                                moment(reward?.validFrom),
                                moment(reward?.validTo)
                            )
                        ) {
                            rewardValidityState = RewardValidity.ACTIVE;
                        } else if (
                            moment(reward?.validTo).diff(moment(), "days") <= 0
                        ) {
                            rewardValidityState = RewardValidity.EXPIRED;
                        } else {
                            rewardValidityState = RewardValidity.SCHEDULED;
                        }
                        break;
                    default:
                        rewardValidityState = "";
                }

                if (tab === RewardTabs.REDEEMED) {
                    if (reward?.rewardType === RewardType.DIGITAL) {
                        pickupLocation = (
                            <Badge className="py-2 px-3" variant="info">
                                No locations for <br />
                                digital types.
                            </Badge>
                        );
                    } else {
                        pickupLocation = reward?.pickupLocation
                            ?.locationName || (
                            <Badge className="py-2 px-3" variant="default">
                                Location not found.
                            </Badge>
                        );
                    }
                }

                return {
                    id: ~RewardsTabs.indexOf(tab)
                        ? reward?._id
                        : reward?.rewardId,
                    rewardName: (
                        <div className="d-flex align-items-center">
                            <div className="p-2 d-flex align-items-center reward-img-container rounded">
                                {isRewardImg ? (
                                    <Image
                                        className="reward-img"
                                        src={
                                            ~RewardsTabs.indexOf(tab)
                                                ? reward?.imageUrls[0]
                                                : reward?.reward?.imageUrls[0]
                                        }
                                        alt={
                                            ~RewardsTabs.indexOf(tab)
                                                ? reward?.imageUrls[0]
                                                : reward?.reward?.imageUrls[0]
                                        }
                                    />
                                ) : (
                                    <div className="d-flex align-items-center justify-content-center text-center reward-not-specified">
                                        No image found
                                    </div>
                                )}
                            </div>
                            <div className="ml-3 text-truncate">
                                <div>
                                    {~RewardsTabs.indexOf(tab)
                                        ? reward?.name
                                        : reward?.reward?.name}
                                </div>
                                {~RewardsTabs.indexOf(tab) ? (
                                    <small
                                        className={`font-weight-bold ${
                                            reward?.remainingCount === 0
                                                ? "text-danger"
                                                : "text-muted"
                                        }`}
                                    >
                                        {`In Stock: `}
                                        {remainingCount === undefined
                                            ? "No stock data found."
                                            : remainingCount}
                                    </small>
                                ) : null}
                            </div>
                        </div>
                    ),
                    rewardValidity: ~RewardsTabs.indexOf(tab) && (
                        <RewardValidityDetails
                            usedIn="memberProfile"
                            rewardValidityState={rewardValidityState}
                            reward={reward}
                        />
                    ),
                    points: (
                        <div className="reward-col d-flex align-items-center">
                            {~RewardsTabs.indexOf(tab)
                                ? reward?.pointsStatic
                                : reward?.pointsRedeemed}
                        </div>
                    ),
                    type: ~RewardsTabs.indexOf(tab) && (
                        <div className="reward-col d-flex align-items-center">
                            <Badge
                                className="py-2 px-3"
                                variant={reward?.type || "default"}
                            >
                                {toTitleCase(reward?.type || "Type not found.")}
                            </Badge>
                        </div>
                    ),
                    redeemedDate: (
                        <div className="reward-col d-flex align-items-center">
                            {moment(reward?.updatedOn).format("LLL")}
                        </div>
                    ),
                    location: (
                        <div className="reward-col d-flex align-items-center text-truncate">
                            {pickupLocation}
                        </div>
                    ),
                    status: tab === RewardTabs.REDEEMED && (
                        <div className="reward-col d-flex align-items-center">
                            <Badge
                                className="py-2 px-3"
                                style={
                                    RewardGenerationJobStatusColorCode[
                                        reward?.processingStatus
                                    ]
                                }
                            >
                                {getRewardStatus(reward?.processingStatus)}
                            </Badge>
                        </div>
                    ),
                    "": (
                        <div>
                            {tab === RewardTabs.UNLOCKED && (
                                <div className="reward-col d-flex align-items-center justify-content-around">
                                    {isAuthorizedForAction(
                                        AccessPermissionModuleNames.REWARD,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.REWARD
                                        ].actions.RedeemReward
                                    ) && (
                                        <Button
                                            size="sm"
                                            variant="primary"
                                            onClick={onShowRedemptionModal}
                                            data-id={reward?._id}
                                            data-reward={reward}
                                            disabled={
                                                ~ViewRewardStates.indexOf(
                                                    rewardValidityState
                                                ) || remainingCount === 0
                                            }
                                        >
                                            Redeem
                                        </Button>
                                    )}
                                    <Button
                                        className="btn shadow-none"
                                        size="sm"
                                        variant="link"
                                        onClick={viewReward}
                                        data-id={reward?._id}
                                    >
                                        View Reward
                                    </Button>
                                </div>
                            )}
                            {tab === RewardTabs.REDEEMED && reward?.voucher && (
                                <div className="reward-col d-flex align-items-center">
                                    <Dropdown>
                                        <Dropdown.Toggle
                                            size="sm"
                                            variant="outline-primary"
                                            onSelect={() => {
                                                /* Placeholder for empty arrow funtion. */
                                            }}
                                        >
                                            <span className="mr-1">
                                                Actions
                                            </span>
                                        </Dropdown.Toggle>
                                        <Dropdown.Menu className="d-flex flex-column align-items-center">
                                            {isAuthorizedForAction(
                                                AccessPermissionModuleNames.REWARD,
                                                AccessPermissionModules[
                                                    AccessPermissionModuleNames
                                                        .REWARD
                                                ].actions.ClaimReward
                                            ) && (
                                                <DropdownItem
                                                    eventKey="claimReward"
                                                    key="claimReward"
                                                >
                                                    <Button
                                                        className="w-100"
                                                        name="Claim"
                                                        size="sm"
                                                        variant="outline-secondary"
                                                        disabled={
                                                            reward?.rewardType ===
                                                                RewardType.TANGIBLE &&
                                                            reward?.processingStatus !==
                                                                RewardGenerationJobStatus.COMPLETED
                                                        }
                                                        onClick={
                                                            onShowRedeemedActionModal
                                                        }
                                                        data-id={reward?._id}
                                                    >
                                                        Claim Reward
                                                    </Button>
                                                </DropdownItem>
                                            )}
                                            <DropdownItem
                                                eventKey="cancelReward"
                                                key="cancelReward"
                                            >
                                                <Button
                                                    className="w-100"
                                                    name="Cancel"
                                                    size="sm"
                                                    variant="outline-danger"
                                                    disabled={
                                                        ~disableCancelRedemptionStatuses.indexOf(
                                                            reward?.processingStatus
                                                        )
                                                    }
                                                    onClick={
                                                        onShowRedeemedActionModal
                                                    }
                                                    data-id={reward?._id}
                                                >
                                                    Cancel Reward
                                                </Button>
                                            </DropdownItem>
                                            <DropdownItem
                                                eventKey="viewReward"
                                                key="viewReward"
                                            >
                                                <Button
                                                    className="w-100 btn shadow-none"
                                                    size="sm"
                                                    variant="link"
                                                    onClick={viewReward}
                                                    data-id={reward?.rewardId}
                                                >
                                                    View Reward
                                                </Button>
                                            </DropdownItem>
                                        </Dropdown.Menu>
                                    </Dropdown>
                                </div>
                            )}
                        </div>
                    ),
                };
            }),
        [
            rewards,
            tab,
            onShowRedemptionModal,
            viewReward,
            onShowRedeemedActionModal,
            isAuthorizedForAction,
        ]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            if (~RewardsTabs.indexOf(tab)) {
                loadRewards({ limit: limit, skip: newSkip, searchText });
            } else if (~RedemptionTabs.indexOf(tab)) {
                loadRedemptionLogs({ limit: limit, skip: newSkip });
            }
        },
        [tab, limit, searchText, setSkip, loadRewards, loadRedemptionLogs]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            if (~RewardsTabs.indexOf(tab)) {
                loadRewards({ limit: newLimit, skip: defaultSkip, searchText });
            } else if (~RedemptionTabs.indexOf(tab)) {
                loadRedemptionLogs({ limit: newLimit, skip: defaultSkip });
            }
        },
        [tab, searchText, setLimit, setSkip, loadRewards, loadRedemptionLogs]
    );

    const options = {
        page: skip,
        sizePerPage: limit,
        totalSize: rewardsCount,
        sizePerPageRenderer: SizePerPageRenderer,
        pageStartIndex: 1,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageList: [
            { text: "10", value: 10 },
            { text: "25", value: 25 },
            { text: "50", value: 50 },
        ],
        onPageChange: onChangePagination,
        onSizePerPageChange: onChangePageSize,
    };

    const setSearch = useCallback(
        (search) => {
            if (searchStateUpdateTimeout) {
                clearTimeout(searchStateUpdateTimeout);
            }
            setSearchText(search);
            searchStateUpdateTimeout = setTimeout(async () => {
                if (~RewardsTabs.indexOf(tab)) {
                    loadRewards({
                        limit,
                        skip: defaultSkip,
                        searchText: search,
                    });
                    setSkip(defaultSkip);
                }
            }, 2000);
        },
        [tab, limit, setSkip, setSearchText, loadRewards]
    );

    useEffect(() => {
        if (
            ~RewardsTabs.indexOf(tab) &&
            isAuthorizedForAction(
                AccessPermissionModuleNames.REWARD,
                AccessPermissionModules[AccessPermissionModuleNames.REWARD]
                    .actions.ListRewards
            )
        ) {
            loadRewards({ limit, skip: defaultSkip });
        } else if (
            ~RedemptionTabs.indexOf(tab) &&
            isAuthorizedForAction(
                AccessPermissionModuleNames.REWARD,
                AccessPermissionModules[AccessPermissionModuleNames.REWARD]
                    .actions.ListRewardRedemptionLogs
            )
        ) {
            loadRedemptionLogs({ limit, skip: defaultSkip });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tab]);

    return (
        <>
            {~RewardsTabs.indexOf(tab) ? (
                <>
                    <Row>
                        <Col>
                            <FormSearch
                                placeholder="Search"
                                selected={searchText}
                                onChange={setSearch}
                                id="search-cards"
                                size="sm"
                                disabled={isLoading}
                            />
                        </Col>
                    </Row>
                    <br />
                </>
            ) : (
                <></>
            )}
            <div className="h-100">
                <PaginationProvider
                    pagination={paginationFactory(options)}
                    keyField="id"
                    columns={columns}
                    data={data}
                >
                    {({ paginationTableProps }) => (
                        <ToolkitProvider
                            keyField="id"
                            data={data}
                            columns={columns}
                            columnToggle
                        >
                            {(props) => (
                                <div>
                                    <BootstrapTable
                                        {...paginationTableProps}
                                        remote={{
                                            search: true,
                                            pagination: true,
                                        }}
                                        loading={
                                            isLoading || isReloadingAfterRedeem
                                        }
                                        keyField="id"
                                        noDataIndication={
                                            <NoData loading={isLoading} />
                                        }
                                        overlay={BootstrapTableOverlay}
                                        {...props.baseProps}
                                    />
                                </div>
                            )}
                        </ToolkitProvider>
                    )}
                </PaginationProvider>
            </div>

            {tab === RewardTabs.UNLOCKED && showRedemptionModal && (
                <ConfirmationModal
                    show={showRedemptionModal}
                    onHide={onHideRedemptionModal}
                    memberId={memberId}
                    selectedReward={selectedReward}
                />
            )}

            {tab === RewardTabs.REDEEMED && showRedeemedActionModal && (
                <RedeemedRewardsActions
                    actionType={actionType}
                    show={showRedeemedActionModal}
                    onHide={onHideRedeemedActionModal}
                    currentRedemption={currentRedemption}
                />
            )}
        </>
    );
};

SharedTable.defaultProps = {
    tab: "",
    memberId: "",
    isLoading: false,
    isReloadingAfterRedeem: false,
    rewards: [],
    rewardsCount: 0,
    availablePoints: 0,
    setLimit: () => {},
    setSkip: () => {},
    setIsDisabledTab: () => {},
    setIsLoading: () => {},
    setIsReloadingAfterRedeem: () => {},
    setMember: () => {},
    setRewards: () => {},
    setRewardsCount: () => {},
};

SharedTable.propTypes = {
    tab: PropTypes.string,
    limit: PropTypes.number.isRequired,
    skip: PropTypes.number.isRequired,
    memberId: PropTypes.string,
    isLoading: PropTypes.bool,
    isReloadingAfterRedeem: PropTypes.bool,
    rewards: PropTypes.array,
    rewardsCount: PropTypes.number,
    availablePoints: PropTypes.number,
    baseProps: PropTypes.any,
    setLimit: PropTypes.func,
    setSkip: PropTypes.func,
    setIsDisabledTab: PropTypes.func,
    setIsLoading: PropTypes.func,
    setIsReloadingAfterRedeem: PropTypes.func,
    setMember: PropTypes.func,
    setRewards: PropTypes.func,
    setRewardsCount: PropTypes.func,
};

export default SharedTable;
