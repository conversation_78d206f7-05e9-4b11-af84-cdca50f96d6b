import React, { useC<PERSON>back, useContext, useMemo, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    Modal,
    Form,
    Image,
    Badge,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext, UserContext } from "Contexts";
import { MerchantLocationStatusObj, RewardType } from "Data";
import { redeemReward } from "Services";
import { toTitleCase } from "Utils";

import "./ConfirmationModal.scss";

const ConfirmationModal = ({ show, onHide, memberId, selectedReward }) => {
    const { merchantLocations } = useContext(DataContext);
    const { selectedRegion } = useContext(UserContext);
    const [isRedeeming, setIsRedeeming] = useState(false);
    const [selectedLocation, setSelectedLocation] = useState([]);

    const pickupLocations = useMemo(() => {
        if (selectedReward?.type === RewardType.TANGIBLE) {
            const allMerchantLocations = (
                Object.values(merchantLocations)
                    .reduce((result, item) => {
                        result.push(Object.values(item));
                        return result;
                    }, [])
                    .flat(1) || []
            )
                .filter(
                    (location) =>
                        location?.isPickupLocation &&
                        location?.status === MerchantLocationStatusObj.ACTIVE
                )
                .map((filteredLocation) => ({
                    locationId: filteredLocation?._id || "Unknown Id",
                    locationName: filteredLocation?.locationName,
                }));

            if (!selectedReward?.rewardMetadata?.allowAllClaimLocations) {
                return allMerchantLocations.filter((mappedLocation) =>
                    selectedReward?.rewardMetadata?.claimLocations?.find(
                        (location) => location === mappedLocation.locationId
                    )
                );
            } else {
                return allMerchantLocations;
            }
        } else {
            return [];
        }
    }, [
        merchantLocations,
        selectedReward?.type,
        selectedReward?.rewardMetadata?.allowAllClaimLocations,
        selectedReward?.rewardMetadata?.claimLocations,
    ]);

    const remainingCount = useMemo(() => {
        if (selectedReward?.totalCount >= 0 && selectedReward?.usedCount >= 0) {
            if (selectedReward.totalCount === 0) {
                return 0;
            } else {
                return selectedReward.totalCount - selectedReward.usedCount;
            }
        }
    }, [selectedReward?.totalCount, selectedReward?.usedCount]);

    const onSelectPickupLocation = useCallback(
        (e) => setSelectedLocation(e),
        [setSelectedLocation]
    );

    const onClickRedeem = useCallback(async () => {
        const payload = {
            memberId,
            rewardId: selectedReward?._id,
            regionId: selectedRegion?._id,
            metadata: {
                ...(selectedReward?.type === RewardType.TANGIBLE
                    ? { claimLocationId: selectedLocation[0]?.locationId }
                    : {}),
            },
        };

        try {
            setIsRedeeming(true);
            const response = await redeemReward(payload);
            toast.success("Reward redemption successful.");
            setIsRedeeming(false);
            onHide(null, response);
        } catch (e) {
            setIsRedeeming(false);
            console.error(e);
            toast.error(
                <div>
                    Failed to redeem the reward!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        selectedRegion?._id,
        memberId,
        selectedReward?._id,
        selectedReward?.type,
        selectedLocation,
        setIsRedeeming,
        onHide,
    ]);

    return (
        <Modal
            className="confirmation-modal-view"
            show={show}
            onHide={onHide}
            size="md"
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!isRedeeming}>
                <Modal.Title>Confirm Redemption</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div className="d-flex flex-column align-items-center mb-4">
                    <div className="text-center p-2 border rounded reward-img-container p-">
                        {selectedReward?.imageUrls.length !== 0 &&
                        selectedReward?.imageUrls[0] !== "" ? (
                            <Image
                                className="reward-img p-3"
                                src={selectedReward?.imageUrls[0]}
                                alt={selectedReward?.name || "Unknown reward"}
                            />
                        ) : (
                            <div className="d-flex align-items-center justify-content-center reward-not-specified">
                                No image found
                            </div>
                        )}
                    </div>
                    <h1 className="mb-0">
                        {selectedReward?.name || "Unknown reward"}
                    </h1>
                    <div className="d-flex align-items-center">
                        <h3 className="mb-0">Type: </h3>
                        <Badge
                            className="ml-2 py-2 px-3"
                            variant={selectedReward?.type || "default"}
                        >
                            {toTitleCase(
                                selectedReward?.type || "Type not found."
                            )}
                        </Badge>
                    </div>
                    <h3
                        className={`font-weight-bold ${
                            selectedReward?.remainingCount === 0
                                ? "text-danger"
                                : "text-muted"
                        }`}
                    >
                        {"In Stock: "}
                        {remainingCount === undefined
                            ? "No stock data found."
                            : remainingCount}
                    </h3>
                </div>
                {selectedReward?.type === RewardType.TANGIBLE && (
                    <Form.Group>
                        <Form.Label className="d-flex align-items-center">
                            Select a pickup location
                            <div className="ml-1 text-danger">*</div>
                        </Form.Label>
                        <Form.Select
                            id="locationId"
                            labelKey="locationName"
                            placeholder="Select pickup location..."
                            options={pickupLocations}
                            selected={selectedLocation}
                            disabled={isRedeeming}
                            onChange={onSelectPickupLocation}
                            required
                        />
                    </Form.Group>
                )}
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={onHide}
                    disabled={isRedeeming}
                >
                    Cancel
                </Button>
                <Button
                    size="sm"
                    variant={
                        selectedReward?.type === RewardType.TANGIBLE &&
                        selectedLocation.length === 0
                            ? "outline-dark"
                            : "success"
                    }
                    onClick={onClickRedeem}
                    disabled={
                        isRedeeming ||
                        (selectedReward?.type === RewardType.TANGIBLE &&
                            selectedLocation.length === 0)
                    }
                >
                    {isRedeeming ? "Redeeming..." : "Confirm Redemption"}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

ConfirmationModal.propTypes = {
    show: PropTypes.bool.isRequired,
    onHide: PropTypes.func.isRequired,
};

export default ConfirmationModal;
