import React, {
    useState,
    useCallback,
    useEffect,
    useContext,
    useMemo,
} from "react";
import { useParams, useLocation, useHistory } from "react-router-dom";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    IcIcon,
    Dropdown,
    Button,
    Avatar,
    Row,
    Col,
    Heading,
    SubHeading,
    Tab,
    Tabs,
    DropdownButton,
    DropdownItem,
    Form,
    Badge,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faPhone,
    faEnvelope,
    faEllipsisV,
    faTachometerAlt,
    faFileBookmarkAlt,
    faCoins,
    faGift,
    faFile,
    faMedal,
    faSync,
    faAngleLeftB,
    faHistory,
    faExport,
    faUserRemove,
    faUserForget,
    faSetting,
    faUserCheck,
    faCheckCircle,
    faArchive,
} from "FaICIconMap";
import { MembersContext, DataContext, UserContext } from "Contexts";
import {
    MemberTypes,
    MemberStatus,
    AccessPermissionModules,
    AccessPermissionModuleNames,
    CardStatus,
    MemberVerificationTypes,
} from "Data";
import {
    getAllCardsOfMember,
    getMemberById,
    getSecondaryAccounts,
} from "Services";
import {
    formatToCommonReadableFormat,
    toTitleCase,
    formatToCommonFormat,
    getMemberFullName,
    getTruncatedStringWithTooltip,
} from "Utils";
import BaseLayout from "Layout/BaseLayout";
import { LoadingComponent } from "Components/utils/UtilComponents";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import { MemberReactivateContext } from "./memberReactivate/context/MemberReactivateContext";
import LoyaltyCards from "./memberAbout/loyaltyCards/LoyaltyCards";
import SecondaryAccounts from "./memberAbout/secondaryAccounts/SecondaryAccounts";
import PrimaryAccount from "./memberAbout/primaryAccount/PrimaryAccount";
import Tags from "./memberAbout/tags/Tags";
import CustomerPortal from "./memberAbout/customerPortal/CustomerPortal";
import MemberDetails from "./memberAbout/memberDetails/MemberDetails";
import Overview from "./tabViews/overview/Overview";
import ActivityLog from "./tabViews/activityLog/ActivityLog";
import PointsPage from "./tabViews/points/PointsPage";
import Rewards from "./tabViews/rewards/Rewards";
import Notes from "./tabViews/notes/Notes";
import Donate from "./other/Donate";
import AddPoints from "./other/AddPoints";
import SuspendAccount from "./other/SuspendAccount";
import ConvertPrimary from "./other/ConvertPrimary";
import EditBasicInfo from "./other/EditBasicInfo";
import ChangeAffinityGroup from "./other/ChangeAffinityGroup";
import ArchiveAccount from "./other/ArchiveAccount";
import RedeemPoints from "./other/RedeemPoints";
import AdjustPoints from "./other/AdjustPoints";
import RedeemPartnerReward from "./other/RedeemPartnerReward";
import ForgetAccount from "./other/ForgetAccount";
import SharePoints from "./other/SharePoints";
import ReverseBill from "./other/ReverseBill";
import AccountExportModal from "./other/AccountExportModal";
import UnauthorizedAccessControl from "../../utils/unauthorizedAccessControl/UnauthorizedAccessControl";
import { CreateMemberContextProvider } from "../shared/enrollMemberWizard/context/EnrollMembersContext";
import { MemberReactivateWizard } from "./memberReactivate/memberReactivateWizard/MemberReactivateWizard";
import HistoryEvents from "./tabViews/historyEvents/HistoryEvents";
import UpdateMemberVerification from "./other/UpdateMemberVerification";

import "./MemberProfile.scss";

const tabs = {
    overview: "Overview",
    activityLog: "ActivityLog",
    points: "Points",
    rewards: "Rewards",
    notes: "Notes",
    history: "History",
};

const DetailsTab = ({
    tab,
    member,
    selectedRegion,
    rewards,
    memberRefreshMetadata,
    setIsDisabledTab,
}) => {
    DetailsTab.defaultProps = {
        setIsDisabledTab: () => {},
    };

    DetailsTab.propTypes = {
        tab: PropTypes.string,
        member: PropTypes.object,
        selectedRegion: PropTypes.object,
        rewards: PropTypes.array,
        memberRefreshMetadata: PropTypes.object,
        setIsDisabledTab: PropTypes.func,
    };

    if (!member._id) {
        return null;
    }

    switch (tab) {
        case tabs.overview: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.ACTIVITY}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.ACTIVITY
                            ].actions.ListActivities,
                        ],
                    }}
                    logic={"AND"}
                >
                    <Overview
                        memberId={member._id}
                        memberInsight={member?.memberInsight}
                        points={member.points}
                        regionId={selectedRegion._id}
                        setIsDisabledTab={setIsDisabledTab}
                        pointStats={member?.pointStats}
                    />
                </UnauthorizedAccessControl>
            );
        }
        case tabs.history: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.MEMBER}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.MEMBER
                            ].actions.GetMember,
                        ],
                    }}
                    logic={"AND"}
                >
                    <HistoryEvents historyEvents={member?.historyEvents} />
                </UnauthorizedAccessControl>
            );
        }
        case tabs.activityLog: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.ACTIVITY}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.ACTIVITY
                            ].actions.ListActivities,
                        ],
                    }}
                    logic={"AND"}
                >
                    <ActivityLog
                        memberId={member._id}
                        setIsDisabledTab={setIsDisabledTab}
                    />
                </UnauthorizedAccessControl>
            );
        }
        case tabs.points: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.TRANSACTION}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.TRANSACTION
                            ].actions.ListTransactions,
                        ],
                        [`${AccessPermissionModuleNames.REWARD}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.REWARD
                            ].actions.ListRewardRedemptionLogs,
                        ],
                    }}
                    logic={"OR"}
                >
                    <PointsPage
                        memberId={member._id}
                        rewards={rewards}
                        setIsDisabledTab={setIsDisabledTab}
                    />
                </UnauthorizedAccessControl>
            );
        }
        case tabs.rewards: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.REWARD}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.REWARD
                            ].actions.ListRewards,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.REWARD
                            ].actions.ListRewardRedemptionLogs,
                        ],
                    }}
                    logic={"OR"}
                >
                    <Rewards
                        memberId={member?._id}
                        loyaltyId={member?.loyaltyId}
                        availablePoints={member.points}
                        isReloadingAfterRedeem={
                            memberRefreshMetadata.isReloadingAfterRedeem
                        }
                        setIsReloadingAfterRedeem={
                            memberRefreshMetadata.setIsReloadingAfterRedeem
                        }
                        setMember={memberRefreshMetadata.setMember}
                        setIsDisabledTab={setIsDisabledTab}
                    />
                </UnauthorizedAccessControl>
            );
        }
        case tabs.notes: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.MEMBER_NOTE}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.MEMBER_NOTE
                            ].actions.ListMemberNotes,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.MEMBER_NOTE
                            ].actions.CreateMemberNote,
                        ],
                    }}
                    logic={"OR"}
                >
                    <Notes
                        memberId={member._id}
                        setIsDisabledTab={setIsDisabledTab}
                    />
                </UnauthorizedAccessControl>
            );
        }
        default: {
            return null;
        }
    }
};

const MemberProfile = () => {
    const { selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const { affinityGroups, loadAffinityGroups, rewards, tiers } =
        useContext(DataContext);
    const { updateMember, loadMembers } = useContext(MembersContext);
    const { showMemberReactivateWizard, setShowMemberReactivateWizard } =
        useContext(MemberReactivateContext);
    const [member, setMember] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingCards, setIsLoadingCards] = useState(false);
    const [isReloadingAffinityGroups, setIsReloadingAffinityGroups] =
        useState(false);
    const [updatedAffinityGroup, setUpdatedAffinityGroup] = useState([]);
    const [accountStatus, setAccountStatus] = useState("");
    const [accountType, setAccountType] = useState("");
    const [showConvertPrimary, setShowConvertPrimary] = useState(false);
    const [showEdit, setShowEdit] = useState(false);
    const [pointsActionType, setPointsActionType] = useState("");
    const [showSuspendAccount, setShowSuspendAccount] = useState(false);
    const [showNameEdit, setShowNameEdit] = useState(false);
    const [showMobileEdit, setShowMobileEdit] = useState(false);
    const [showAffinityGroup, setShowAffinityGroup] = useState(false);
    const [showArchiveModal, setShowArchiveModal] = useState(false);
    const [
        showUpdateMemberVerificationModal,
        setShowUpdateMemberVerificationModal,
    ] = useState(false);
    const [tab, setTab] = useState(tabs.overview);
    const [isReloadingAfterRedeem, setIsReloadingAfterRedeem] = useState(false);
    const [showForgetModel, setShowForgetModel] = useState(false);
    const [showExportMemberModal, setShowExportMemberModal] = useState(false);
    const [isDisabledTab, setIsDisabledTab] = useState(false);
    const [loyaltyCards, setLoyaltyCards] = useState([]);
    const [totalCards, setTotalCards] = useState(0);
    const [memberSuspendedCards, setMemberSuspendedCards] = useState([]);
    const [memberPrevCard, setMemberPrevCard] = useState({});
    const [isLoadingSecondaryAccounts, setIsLoadingSecondaryAccounts] =
        useState(false);
    const [secondaryAccounts, setSecondaryAccounts] = useState([]);
    const [isReloading, setIsReloading] = useState(false);
    const { id: memberId } = useParams();
    const history = useHistory();
    const location = useLocation();

    const pointActionsMap = useMemo(() => {
        const options = {};
        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.CollectPointsBill
            ) ||
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.CollectPointsAmount
            )
        ) {
            options.addPoints = {
                id: "addPoints",
                name: "Add Points",
            };
        }

        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.RedeemPoints
            )
        ) {
            options.redeemPoints = {
                id: "redeemPoints",
                name: "Redeem Points",
            };
        }
        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.TransferPoints
            ) &&
            member &&
            member.type === MemberTypes.PRIMARY
        ) {
            options.sharePoint = {
                id: "sharePoint",
                name: "Share Point",
            };
        }
        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.AdjustPoints
            )
        ) {
            options.adjustPoints = {
                id: "adjustPoints",
                name: "Adjust Points",
            };
        }

        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.TransferPoints
            )
        ) {
            options.donate = {
                id: "donate",
                name: "Donate",
            };
        }
        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.ReverseBill
            )
        ) {
            options.reverseBill = {
                id: "reverseBill",
                name: "Reverse Bill",
            };
        }

        return options;
    }, [isAuthorizedForAction, member]);

    const showPartnerReward = useMemo(
        () =>
            isAuthorizedForAction(
                AccessPermissionModuleNames.REWARD,
                AccessPermissionModules[AccessPermissionModuleNames.REWARD]
                    .actions.RedeemReward
            )
                ? rewards.reduce((result, item) => {
                    result[item._id] = { name: item.name };
                    return result;
                }, pointActionsMap)
                : pointActionsMap,
        [rewards, isAuthorizedForAction, pointActionsMap]
    );

    const userHaveAnAssignedCard = useMemo(
        () =>
            loyaltyCards.some(
                (card) =>
                    card.status === CardStatus.ASSIGNED && CardStatus.ASSIGNED
            ),
        [loyaltyCards]
    );

    const affinityOptions = useMemo(
        () =>
            affinityGroups?.map((group) => ({
                value: group._id,
                name: group.name,
            })) || [],
        [affinityGroups]
    );

    const selectedAffinityGroup = useMemo(
        () =>
            affinityOptions.filter(
                (group) =>
                    group.value === member?.affinityGroup?.affinityGroupId
            ) || [],
        [affinityOptions, member?.affinityGroup?.affinityGroupId]
    );

    const onClickReloadAffinityGroups = useCallback(async () => {
        setIsReloadingAffinityGroups(true);
        await loadAffinityGroups(selectedRegion._id);
        setIsReloadingAffinityGroups(false);
    }, [selectedRegion._id, loadAffinityGroups, setIsReloadingAffinityGroups]);

    const showConvertToPrimary = useCallback(() => {
        setShowConvertPrimary(true);
    }, [setShowConvertPrimary]);

    const loadProfile = useCallback(async () => {
        try {
            setIsLoading(true);
            const memberProfileResponse = await getMemberById(memberId);
            setMember(memberProfileResponse);
            setAccountType(memberProfileResponse.type);
            setAccountStatus(memberProfileResponse.status);
            updateMember(memberProfileResponse);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load member profile!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoading(false);
        }
    }, [
        memberId,
        updateMember,
        setMember,
        setAccountType,
        setAccountStatus,
        setIsLoading,
    ]);

    const loadCardData = useCallback(async () => {
        const queryObj = { regionId: selectedRegion._id, memberId };
        try {
            setLoyaltyCards([]);
            setTotalCards(0);
            setIsLoadingCards(true);

            const cards = await getAllCardsOfMember(queryObj);

            const activeCards = cards?.items.filter(
                (card) =>
                    card.status === CardStatus.ACTIVE ||
                    card.status === CardStatus.ASSIGNED
            );
            const otherCards = cards?.items
                .filter(
                    (card) =>
                        card.status === CardStatus.DEACTIVATED ||
                        card.status === CardStatus.SUSPENDED
                )
                .sort((a, b) => a?.status.localeCompare(b?.status))
                .reverse();
            let cardList = [];

            if (activeCards.length > 0 && otherCards.length > 0) {
                cardList = [...activeCards, ...otherCards];
                setLoyaltyCards([...activeCards, ...otherCards]);
            } else if (activeCards.length > 0 && otherCards.length === 0) {
                cardList = [...activeCards];
                setLoyaltyCards(activeCards);
            } else {
                setLoyaltyCards(otherCards);
                cardList = [...otherCards];
            }
            setTotalCards(cards.total);

            // * Card data is prepared for member reactivation logic.
            const cardsSortedByLastUsed = cardList.sort(
                (a, b) => new Date(b?.updatedOn) - new Date(a?.updatedOn)
            );
            const prevCard =
                cardsSortedByLastUsed.find(
                    (sC) => sC?.status === CardStatus.ASSIGNED
                ) ||
                cardsSortedByLastUsed[0] ||
                {};

            setMemberSuspendedCards(
                cardsSortedByLastUsed.filter(
                    (cSBLU) => cSBLU?.status === CardStatus.SUSPENDED
                )
            );
            setMemberPrevCard(prevCard);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    {`Failed to load `}
                    {member.firstName || member.lastName
                        ? `${member.firstName} ${member.lastName}`
                        : "Member"}
                    's card data!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoadingCards(false);
        }
    }, [
        selectedRegion._id,
        memberId,
        member.firstName,
        member.lastName,
        setIsLoadingCards,
        setLoyaltyCards,
        setTotalCards,
        setMemberSuspendedCards,
        setMemberPrevCard,
    ]);

    const loadSecondaryAccounts = useCallback(async () => {
        const queryObj = {
            limit: 100,
            skip: 0,
            regionId: selectedRegion._id,
            parentMemberId: memberId,
        };
        try {
            setIsLoadingSecondaryAccounts(true);
            const secondary = await getSecondaryAccounts(queryObj);
            setSecondaryAccounts(secondary.items);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load secondary accounts!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoadingSecondaryAccounts(false);
        }
    }, [setSecondaryAccounts, selectedRegion?._id, memberId]);

    const showEditEmail = useCallback(() => {
        setShowEdit(true);
    }, [setShowEdit]);

    const onHideEditEmail = useCallback(
        (e, data) => {
            if (data) {
                setMember(data);
            }
            setShowEdit(false);
        },
        [setShowEdit, setMember]
    );

    const showEditName = useCallback(() => {
        setShowNameEdit(true);
    }, [setShowNameEdit]);

    const onHideEditName = useCallback(
        (e, data) => {
            if (data) {
                setMember(data);
            }
            setShowNameEdit(false);
        },
        [setMember, setShowNameEdit]
    );

    const showEditMobile = useCallback(() => {
        setShowMobileEdit(true);
    }, [setShowMobileEdit]);

    const onHideEditMobile = useCallback(
        (e, data) => {
            if (data) {
                setMember(data);
            }
            setShowMobileEdit(false);
        },
        [setShowMobileEdit, setMember]
    );

    const onShowMemberReactivateWizard = useCallback(
        () => setShowMemberReactivateWizard(true),
        [setShowMemberReactivateWizard]
    );

    const onHideArchiveModal = useCallback(() => {
        setShowArchiveModal(false);
    }, [setShowArchiveModal]);

    const onHideForgetModal = useCallback(() => {
        setShowForgetModel(false);
    }, [setShowForgetModel]);

    const onHideExportMemberModal = useCallback(() => {
        setShowExportMemberModal(false);
    }, [setShowExportMemberModal]);

    const onHideMemberVerificationModal = useCallback(
        (e, data) => {
            if (data) {
                loadProfile();
            }
            setShowUpdateMemberVerificationModal(false);
        },
        [setShowUpdateMemberVerificationModal, loadProfile]
    );

    const onHidePointModal = useCallback(
        (e, data) => {
            if (data) {
                loadProfile();
            }
            setPointsActionType("");
        },
        [setPointsActionType, loadProfile]
    );

    const onShowSuspendAccount = useCallback(
        () => setShowSuspendAccount(true),
        [setShowSuspendAccount]
    );

    const onHideSuspendAccount = useCallback(
        () => setShowSuspendAccount(false),
        [setShowSuspendAccount]
    );

    const onHideConvertPrimary = useCallback(() => {
        setShowConvertPrimary(false);
    }, [setShowConvertPrimary]);

    const onChangeAffinityGroup = useCallback(
        (selected) => setUpdatedAffinityGroup(selected),
        [setUpdatedAffinityGroup]
    );

    const onShowAffinityGroup = useCallback(
        () => setShowAffinityGroup(true),
        [setShowAffinityGroup]
    );

    const onHideAffinityGroup = useCallback(
        (e, data) => {
            if (data) {
                setMember(data);
            }
            setShowAffinityGroup(false);
            setUpdatedAffinityGroup([]);
        },
        [setShowAffinityGroup, setUpdatedAffinityGroup]
    );

    const onNavigatingBack = useCallback(() => {
        history.push("/members");
        loadMembers({ shouldReset: true, skip: 1, countMembers: true });
    }, [history, loadMembers]);

    const onReloadMemberProfile = useCallback(async () => {
        setIsReloading(true);
        await Promise.all([
            loadProfile(),
            loadCardData(),
            loadSecondaryAccounts(),
        ]);
        setIsReloading(false);
    }, [loadCardData, loadProfile, loadSecondaryAccounts, setIsReloading]);

    useEffect(() => {
        if (updatedAffinityGroup.length > 0) {
            onShowAffinityGroup();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [updatedAffinityGroup]);

    useEffect(() => {
        if (location.state?.member) {
            setMember(location.state.member);
        }
    }, [location]);

    useEffect(() => {
        if (memberId) {
            loadProfile();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [memberId]);

    useEffect(() => {
        if (memberId) {
            loadCardData();
        }
        // eslint-disable-next-line
    }, [memberId]);

    useEffect(() => {
        if (memberId) {
            loadSecondaryAccounts();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [memberId, selectedRegion?._id]);

    const pointAdjustModal = useCallback(() => {
        switch (pointsActionType) {
            case pointActionsMap.addPoints?.id: {
                return (
                    <AddPoints
                        show={true}
                        onHide={onHidePointModal}
                        memberId={memberId}
                    />
                );
            }
            case pointActionsMap.donate?.id: {
                return (
                    <Donate
                        show={true}
                        onHide={onHidePointModal}
                        memberId={memberId}
                    />
                );
            }
            case pointActionsMap.redeemPoints?.id: {
                return (
                    <RedeemPoints
                        show={
                            pointsActionType === pointActionsMap.redeemPoints.id
                        }
                        onHide={onHidePointModal}
                        memberId={memberId}
                    />
                );
            }
            case pointActionsMap.adjustPoints?.id: {
                return (
                    <AdjustPoints
                        show={
                            pointsActionType === pointActionsMap.adjustPoints.id
                        }
                        onHide={onHidePointModal}
                        memberId={memberId}
                    />
                );
            }
            case pointActionsMap.sharePoint?.id: {
                return (
                    <SharePoints
                        show={
                            pointsActionType === pointActionsMap.sharePoint.id
                        }
                        onHide={onHidePointModal}
                        memberId={memberId}
                    />
                );
            }
            case pointActionsMap.reverseBill?.id: {
                return (
                    <ReverseBill
                        show={
                            pointsActionType === pointActionsMap.reverseBill.id
                        }
                        onHide={onHidePointModal}
                        memberId={memberId}
                    />
                );
            }
            default: {
                if (pointsActionType !== "") {
                    return (
                        <RedeemPartnerReward
                            show={true}
                            onHide={onHidePointModal}
                            memberId={memberId}
                            member={member}
                            reward={rewards.find(
                                (pR) => pR?._id === pointsActionType
                            )}
                        />
                    );
                }
                return null;
            }
        }
    }, [
        memberId,
        member,
        pointsActionType,
        rewards,
        pointActionsMap,
        onHidePointModal,
    ]);
    const {
        firstName,
        lastName,
        email,
        mobileNumber,
        profilePicture,
        loyaltyId,
        lastSeenOn,
        tierPoints,
        points,
        residentialAddress,
        postalAddress,
        tier,
        tierData,
        pointsToExpire,
        ...rest
    } = member;

    const tierName = useMemo(() => {
        if (tierData?.name) {
            return tierData.name;
        }
        if (tier?.name) {
            return tier.name;
        }
        if (tier?.tierId) {
            if (tiers?.length > 0) {
                const selectedTier = tiers.find(
                    ({ _id }) => _id === tier.tierId
                );
                if (selectedTier) {
                    return selectedTier.name;
                }
            }
            return tier.tierId;
        }
        return "-";
    }, [tier, tierData, tiers]);

    return (
        <CreateMemberContextProvider
            loadSecondaryAccounts={loadSecondaryAccounts}
        >
            <div className="member-profile">
                <BaseLayout
                    topLeft={
                        <div className="d-flex justify-content-between align-items-center">
                            <Heading text="Member Profile" />
                            <div className="ml-3">
                                {isReloading ? (
                                    <small className="ml-3 text-primary">
                                        Reloading...
                                    </small>
                                ) : (
                                    <Button
                                        className="shadow-none"
                                        size="sm"
                                        variant="link"
                                        disabled={isLoading || isReloading}
                                        onClick={onReloadMemberProfile}
                                    >
                                        <IcIcon
                                            className="mr-2"
                                            size="md"
                                            icon={faSync}
                                        />
                                        Reload Member Profile
                                    </Button>
                                )}
                            </div>
                        </div>
                    }
                    topRight={
                        <div className="d-flex align-items-center">
                            <Button
                                className="shadow-none"
                                variant="link"
                                size="sm"
                                disabled={isLoading}
                                onClick={onNavigatingBack}
                            >
                                <div className="d-flex align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faAngleLeftB}
                                    />
                                    Back
                                </div>
                            </Button>
                            {(isAuthorizedForAction(
                                AccessPermissionModuleNames.MEMBER,
                                AccessPermissionModules[
                                    AccessPermissionModuleNames.MEMBER
                                ].actions.ExportMember
                            ) ||
                                isAuthorizedForAction(
                                    AccessPermissionModuleNames.MEMBER,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.MEMBER
                                    ].actions.UpdateMemberVerificationStatus
                                ) ||
                                isAuthorizedForAction(
                                    AccessPermissionModuleNames.MEMBER,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.MEMBER
                                    ].actions.EraseMember
                                ) ||
                                isAuthorizedForAction(
                                    AccessPermissionModuleNames.MEMBER,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.MEMBER
                                    ].actions.DeleteMember
                                )) && (
                                <DropdownButton
                                    variant="primary"
                                    size="sm"
                                    title={
                                        <>
                                            <IcIcon
                                                className="mr-2"
                                                size="lg"
                                                icon={faSetting}
                                            />
                                            <span>Member Settings</span>
                                        </>
                                    }
                                    className="ml-2"
                                    disabled={isLoading}
                                >
                                    {isAuthorizedForAction(
                                        AccessPermissionModuleNames.MEMBER,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.MEMBER
                                        ].actions.ExportMember
                                    ) && (
                                        <DropdownItem
                                            className="border-bottom mb-2"
                                            eventKey="Export Member"
                                            key="Export Member"
                                            disabled={isLoading}
                                        >
                                            <Button
                                                className="w-100"
                                                variant="outline-dark"
                                                size="sm"
                                                disabled={isLoading}
                                                onClick={() =>
                                                    setShowExportMemberModal(
                                                        true
                                                    )
                                                }
                                            >
                                                <IcIcon
                                                    className="mr-2"
                                                    size="lg"
                                                    icon={faExport}
                                                />
                                                Export Member
                                            </Button>
                                        </DropdownItem>
                                    )}
                                    {isAuthorizedForAction(
                                        AccessPermissionModuleNames.MEMBER,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.MEMBER
                                        ].actions.UpdateMemberVerificationStatus
                                    ) && (
                                        <DropdownItem
                                            className="border-bottom my-2"
                                            eventKey={`${
                                                rest?.isVerified
                                                    ? MemberVerificationTypes.UNVERIFY
                                                    : MemberVerificationTypes.VERIFY
                                            } Member`}
                                            key={`${
                                                rest?.isVerified
                                                    ? MemberVerificationTypes.UNVERIFY
                                                    : MemberVerificationTypes.VERIFY
                                            } Member`}
                                            disabled={isLoading}
                                        >
                                            <Button
                                                className="w-100"
                                                variant={`outline-${
                                                    rest?.isVerified
                                                        ? "orange"
                                                        : "info"
                                                }`}
                                                size="sm"
                                                disabled={isLoading}
                                                onClick={() =>
                                                    setShowUpdateMemberVerificationModal(
                                                        true
                                                    )
                                                }
                                            >
                                                <IcIcon
                                                    className="mr-2"
                                                    size="lg"
                                                    icon={
                                                        rest?.isVerified
                                                            ? faUserRemove
                                                            : faUserCheck
                                                    }
                                                />
                                                {`${
                                                    rest?.isVerified
                                                        ? toTitleCase(
                                                            MemberVerificationTypes.UNVERIFY
                                                        )
                                                        : toTitleCase(
                                                            MemberVerificationTypes.VERIFY
                                                        )
                                                } Member`}
                                            </Button>
                                        </DropdownItem>
                                    )}
                                    {isAuthorizedForAction(
                                        AccessPermissionModuleNames.MEMBER,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.MEMBER
                                        ].actions.DeleteMember
                                    ) && (
                                        <DropdownItem
                                            className="border-bottom my-2"
                                            eventKey="Archive Member"
                                            key="Archive Member"
                                            disabled={isLoading}
                                        >
                                            <Button
                                                className="w-100"
                                                variant="outline-danger"
                                                size="sm"
                                                disabled={isLoading}
                                                onClick={() =>
                                                    setShowArchiveModal(true)
                                                }
                                            >
                                                <IcIcon
                                                    className="mr-2"
                                                    size="lg"
                                                    icon={faArchive}
                                                />
                                                Archive Member
                                            </Button>
                                        </DropdownItem>
                                    )}
                                    {isAuthorizedForAction(
                                        AccessPermissionModuleNames.MEMBER,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.MEMBER
                                        ].actions.EraseMember
                                    ) && (
                                        <DropdownItem
                                            className="border-botto mt-2"
                                            eventKey="Forget Member"
                                            key="Forget Member"
                                            disabled={isLoading}
                                        >
                                            <Button
                                                className="w-100"
                                                variant="danger"
                                                size="sm"
                                                disabled={isLoading}
                                                onClick={() =>
                                                    setShowForgetModel(true)
                                                }
                                            >
                                                <IcIcon
                                                    className="mr-2"
                                                    size="lg"
                                                    icon={faUserForget}
                                                />
                                                Forget Member
                                            </Button>
                                        </DropdownItem>
                                    )}
                                </DropdownButton>
                            )}
                        </div>
                    }
                    // TODO: UPDATE FORGET MEMBER AND ARCHIVE MEMBER MODAL DESCRIPTIONS
                    bottom={
                        <>
                            {isLoading ? (
                                <LoadingComponent />
                            ) : (
                                <div className="basic-details-top">
                                    <Row className="mb-3 pt-3 basic-detail-row">
                                        <Col
                                            xl="3"
                                            lg="3"
                                            md="3"
                                            className="border-solid-right d-flex align-items-center"
                                        >
                                            <div className="left-panel profile-div">
                                                <div className="d-flex flex-row pr-3 ml-1 align-items-center avatar-panel">
                                                    <div className="pr-3 mr-1 ml-1 mt-4">
                                                        <Avatar
                                                            name={
                                                                !profilePicture &&
                                                                (firstName ||
                                                                    "U")
                                                            }
                                                            size="80"
                                                            src={profilePicture}
                                                        />
                                                    </div>
                                                    <div className="d-flex flex-column mr-1">
                                                        <div>
                                                            {isAuthorizedForAction(
                                                                AccessPermissionModuleNames.MEMBER,
                                                                AccessPermissionModules[
                                                                    AccessPermissionModuleNames
                                                                        .MEMBER
                                                                ].actions
                                                                    .UpdateMember
                                                            ) && (
                                                                <Button
                                                                    className="shadow-none dropdown-btn float-right"
                                                                    variant="link"
                                                                    size="sm"
                                                                    onClick={
                                                                        showEditName
                                                                    }
                                                                >
                                                                    <p className="mb-0">
                                                                        Edit
                                                                    </p>
                                                                </Button>
                                                            )}
                                                        </div>
                                                        <h3 className="my-0 py-0">
                                                            {getTruncatedStringWithTooltip(
                                                                {
                                                                    value: getMemberFullName(
                                                                        {
                                                                            firstName,
                                                                            lastName,
                                                                        }
                                                                    ),
                                                                    valueMaxLength: 15,
                                                                }
                                                            )}
                                                        </h3>
                                                        <p className="text-muted mb-0">
                                                            {loyaltyId}
                                                        </p>
                                                        <small className="mb-0">
                                                            {`Last seen on ${
                                                                lastSeenOn
                                                                    ? formatToCommonReadableFormat(
                                                                        lastSeenOn
                                                                    )
                                                                    : "~ unknown"
                                                            }`}
                                                        </small>
                                                        {rest?.isVerified && (
                                                            <div className="mt-2">
                                                                <Badge
                                                                    className="px-3 py-0"
                                                                    variant="info"
                                                                >
                                                                    <div className="d-flex align-items-center">
                                                                        Verified
                                                                        <IcIcon
                                                                            className="ml-2"
                                                                            size="lg"
                                                                            icon={
                                                                                faCheckCircle
                                                                            }
                                                                        />
                                                                    </div>
                                                                </Badge>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </Col>
                                        <Col
                                            xl="9"
                                            lg="9"
                                            md="9"
                                            className="d-flex align-items-center right-side"
                                        >
                                            <div className="d-flex flex-row">
                                                <div className="border-solid-right pr-1 contact-div">
                                                    <div className="top-panel-border-bottom panel pr-2">
                                                        <div className="d-flex flex-row justify-content-between">
                                                            <div className="d-flex flex-row mr-auto">
                                                                <p className="py-1 mb-0 mx-2">
                                                                    Email
                                                                </p>
                                                            </div>
                                                            <div className="d-flex flex-row">
                                                                {isAuthorizedForAction(
                                                                    AccessPermissionModuleNames.MEMBER,
                                                                    AccessPermissionModules[
                                                                        AccessPermissionModuleNames
                                                                            .MEMBER
                                                                    ].actions
                                                                        .UpdateMember
                                                                ) && (
                                                                    <Button
                                                                        className="shadow-none dropdown-btn"
                                                                        variant="link"
                                                                        size="sm"
                                                                        onClick={
                                                                            showEditEmail
                                                                        }
                                                                    >
                                                                        Edit
                                                                    </Button>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                            <IcIcon
                                                                className="mr-2 text-primary"
                                                                size="w-10"
                                                                icon={
                                                                    faEnvelope
                                                                }
                                                            />
                                                            {email ||
                                                                "~ unknown"}
                                                        </p>
                                                    </div>
                                                    <div className="pr-2 pt-2">
                                                        <div className="d-flex flex-row justify-content-between">
                                                            <div className="d-flex flex-row mr-auto">
                                                                <p className="py-1 mb-0 mx-2">
                                                                    Contact No
                                                                </p>
                                                            </div>
                                                            <div className="d-flex flex-row">
                                                                {isAuthorizedForAction(
                                                                    AccessPermissionModuleNames.MEMBER,
                                                                    AccessPermissionModules[
                                                                        AccessPermissionModuleNames
                                                                            .MEMBER
                                                                    ].actions
                                                                        .UpdateMember
                                                                ) && (
                                                                    <Button
                                                                        className="shadow-none dropdown-btn"
                                                                        variant="link"
                                                                        size="sm"
                                                                        onClick={
                                                                            showEditMobile
                                                                        }
                                                                    >
                                                                        Edit
                                                                    </Button>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                                <IcIcon
                                                                    className="mr-2 text-primary"
                                                                    size="w-10"
                                                                    icon={
                                                                        faPhone
                                                                    }
                                                                />
                                                                {mobileNumber ||
                                                                    "~ unknown"}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="border-solid-right px-3 account-div">
                                                    <div className="top-panel-border-bottom  panel">
                                                        <div className="d-flex flex-row justify-content-between">
                                                            <div className="d-flex flex-row mr-auto">
                                                                <p className="py-1 mb-0 mr-2">
                                                                    Account Type
                                                                </p>
                                                            </div>
                                                            {isAuthorizedForAction(
                                                                AccessPermissionModuleNames.MEMBER,
                                                                AccessPermissionModules[
                                                                    AccessPermissionModuleNames
                                                                        .MEMBER
                                                                ].actions
                                                                    .ChangeType
                                                            ) &&
                                                                accountType ===
                                                                    MemberTypes.SECONDARY && (
                                                                    <div className="d-flex flex-row ml-3">
                                                                        <DropdownButton
                                                                            data-testid="convert-primary-dropdown"
                                                                            bsPrefix="dropdown-btn single-dropdown-toggle"
                                                                            title={
                                                                                <IcIcon
                                                                                    className="text-primary"
                                                                                    size="w-10"
                                                                                    icon={
                                                                                        faEllipsisV
                                                                                    }
                                                                                />
                                                                            }
                                                                            onSelect={
                                                                                showConvertToPrimary
                                                                            }
                                                                        >
                                                                            <Dropdown.Item
                                                                                eventKey="convertToPrimary"
                                                                                key="convertToPrimary"
                                                                            >
                                                                                Convert
                                                                                to
                                                                                Primary
                                                                            </Dropdown.Item>
                                                                        </DropdownButton>
                                                                    </div>
                                                                )}
                                                        </div>
                                                        {applyBadgeStyling({
                                                            text: accountType,
                                                            variant:
                                                                accountType,
                                                        })}
                                                    </div>
                                                    <div className="pr-2 pt-2">
                                                        <p className="py-1 mb-0 mr-2">
                                                            Account Status
                                                        </p>
                                                        {accountStatus ===
                                                        MemberStatus.ARCHIVED ? (
                                                            applyBadgeStyling({
                                                                text: accountStatus,
                                                                variant:
                                                                    "danger",
                                                            })
                                                        ) : (
                                                            <DropdownButton
                                                                variant={
                                                                    accountStatus ===
                                                                    MemberStatus.ACTIVE
                                                                        ? "success"
                                                                        : "orange"
                                                                }
                                                                size="sm"
                                                                title={
                                                                    <span className="mr-2 font-weight-bold">
                                                                        {toTitleCase(
                                                                            accountStatus
                                                                        ) ||
                                                                            "-"}
                                                                    </span>
                                                                }
                                                            >
                                                                {accountStatus !==
                                                                    MemberStatus.ACTIVE && (
                                                                    <DropdownItem
                                                                        eventKey={
                                                                            MemberStatus.ACTIVE
                                                                        }
                                                                        key={
                                                                            MemberStatus.ACTIVE
                                                                        }
                                                                    >
                                                                        <Button
                                                                            className="mb-2"
                                                                            variant="outline-success"
                                                                            size="sm"
                                                                            disabled={
                                                                                isLoading
                                                                            }
                                                                            onClick={
                                                                                onShowMemberReactivateWizard
                                                                            }
                                                                        >
                                                                            Reactivate
                                                                            Account
                                                                        </Button>
                                                                    </DropdownItem>
                                                                )}
                                                                {accountStatus !==
                                                                    MemberStatus.SUSPENDED && (
                                                                    <DropdownItem
                                                                        eventKey={
                                                                            MemberStatus.SUSPENDED
                                                                        }
                                                                        key={
                                                                            MemberStatus.SUSPENDED
                                                                        }
                                                                    >
                                                                        <Button
                                                                            className="mb-2"
                                                                            variant="outline-orange"
                                                                            size="sm"
                                                                            disabled={
                                                                                isLoading
                                                                            }
                                                                            onClick={
                                                                                onShowSuspendAccount
                                                                            }
                                                                        >
                                                                            Suspend
                                                                            Account
                                                                        </Button>
                                                                    </DropdownItem>
                                                                )}
                                                            </DropdownButton>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="border-solid-right px-3 point-div">
                                                    <div className="top-panel-border-bottom panel">
                                                        <div className="d-flex flex-row justify-content-between">
                                                            <div className="d-flex flex-row mr-auto">
                                                                <p className="py-1 mb-0 mx-2">
                                                                    Redeemable
                                                                    Points
                                                                </p>
                                                            </div>
                                                            <div className="d-flex flex-row">
                                                                <DropdownButton
                                                                    data-testid="points-dropdown"
                                                                    bsPrefix="dropdown-btn single-dropdown-toggle"
                                                                    title={
                                                                        <IcIcon
                                                                            className={`
                                                                            ${
                                                                                isReloadingAfterRedeem
                                                                                    ? "text-muted"
                                                                                    : "text-primary"
                                                                            }`}
                                                                            size="w-10"
                                                                            icon={
                                                                                faEllipsisV
                                                                            }
                                                                        />
                                                                    }
                                                                    disabled={
                                                                        isReloadingAfterRedeem
                                                                    }
                                                                    onSelect={
                                                                        setPointsActionType
                                                                    }
                                                                >
                                                                    {userHaveAnAssignedCard ? (
                                                                        Object.entries(
                                                                            showPartnerReward
                                                                        ).map(
                                                                            ([
                                                                                key,
                                                                                item,
                                                                            ]) => (
                                                                                <DropdownItem
                                                                                    eventKey={
                                                                                        key
                                                                                    }
                                                                                    key={
                                                                                        key
                                                                                    }
                                                                                >
                                                                                    {
                                                                                        item.name
                                                                                    }
                                                                                </DropdownItem>
                                                                            )
                                                                        )
                                                                    ) : (
                                                                        <DropdownItem
                                                                            className="text-danger"
                                                                            disabled
                                                                        >
                                                                            Member
                                                                            Needs
                                                                            an
                                                                            Active
                                                                            Card
                                                                            to
                                                                            Do
                                                                            the
                                                                            Transactions
                                                                        </DropdownItem>
                                                                    )}
                                                                </DropdownButton>
                                                            </div>
                                                        </div>
                                                        <p
                                                            className={`
                                                            py-1 mb-0 mx-2 
                                                            ${
                                                                isReloadingAfterRedeem
                                                                    ? "text-muted"
                                                                    : "font-weight-bold"
                                                            }`}
                                                        >
                                                            {isReloadingAfterRedeem
                                                                ? "Loading points..."
                                                                : points || 0}
                                                        </p>
                                                        {pointsToExpire &&
                                                            Array.isArray(
                                                                pointsToExpire
                                                            ) && (
                                                                <small className="py-1 mb-0 mx-2 text-danger">
                                                                    <span className="font-weight-bold">
                                                                        {pointsToExpire[0]?.pointsToExpire?.toFixed(
                                                                            2
                                                                        ) ||
                                                                            "0"}
                                                                    </span>
                                                                    {` points will expire on ${
                                                                        pointsToExpire[0]
                                                                            ?.pointsExpireOn
                                                                            ? formatToCommonFormat(
                                                                                pointsToExpire[0]
                                                                                    .pointsExpireOn
                                                                            )
                                                                            : "~ unknown"
                                                                    }`}
                                                                </small>
                                                            )}
                                                    </div>
                                                    <div className="pr-2">
                                                        <div>
                                                            <p className="mb-0 d-flex align-items-center mx-2">
                                                                {isReloadingAffinityGroups ? (
                                                                    <p className="mb-0 py-2">
                                                                        Affinity
                                                                        Group...
                                                                    </p>
                                                                ) : (
                                                                    "Affinity Group"
                                                                )}
                                                                <Button
                                                                    className="shadow-none"
                                                                    variant="link"
                                                                    size="sm"
                                                                    disabled={
                                                                        isLoading ||
                                                                        isReloadingAffinityGroups
                                                                    }
                                                                    onClick={
                                                                        onClickReloadAffinityGroups
                                                                    }
                                                                >
                                                                    {isReloadingAffinityGroups ? null : (
                                                                        <IcIcon
                                                                            size="md"
                                                                            icon={
                                                                                faSync
                                                                            }
                                                                        />
                                                                    )}
                                                                </Button>
                                                            </p>
                                                            <Form.Select
                                                                id="basic-typeahead-single"
                                                                labelKey="name"
                                                                onChange={
                                                                    onChangeAffinityGroup
                                                                }
                                                                options={
                                                                    affinityOptions
                                                                }
                                                                placeholder="Affinity group not set"
                                                                selected={
                                                                    selectedAffinityGroup
                                                                }
                                                                noBorder={true}
                                                                disabled={
                                                                    !isAuthorizedForAction(
                                                                        AccessPermissionModuleNames.MEMBER,
                                                                        AccessPermissionModules[
                                                                            AccessPermissionModuleNames
                                                                                .MEMBER
                                                                        ]
                                                                            .actions
                                                                            .UpdateMemberAffinityGroup
                                                                    ) ||
                                                                    isLoading ||
                                                                    isReloadingAffinityGroups
                                                                }
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="pl-3 tier-div">
                                                    <div className="top-panel-border-bottom panel">
                                                        <p className="py-1 mb-0 mx-2">
                                                            Tier
                                                        </p>
                                                        <div className="mx-2 d-flex justify-content-start align-items-center">
                                                            <IcIcon
                                                                className="mr-2 text-primary"
                                                                icon={faMedal}
                                                            />
                                                            <div className="font-weight-bold">
                                                                {tierName}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="pr-2 pt-2">
                                                        <p className="py-1 mb-0 mx-2">
                                                            Tier Points
                                                        </p>
                                                        <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                            {tierPoints || 0}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </Col>
                                    </Row>
                                </div>
                            )}
                            {isLoading ? (
                                <LoadingComponent />
                            ) : (
                                <Row className="mt-3">
                                    <Col xl="3" lg="3" md="3">
                                        <div className="border-solid-right bottom-panel">
                                            <SubHeading text="About Member" />
                                            <LoyaltyCards
                                                isLoading={isLoadingCards}
                                                address={
                                                    residentialAddress ||
                                                    postalAddress
                                                }
                                                name={`${member.firstName} ${member.lastName}`}
                                                memberId={memberId}
                                                loyaltyCards={loyaltyCards}
                                                totalCards={totalCards}
                                                setLoyaltyCards={
                                                    setLoyaltyCards
                                                }
                                                loadProfile={loadProfile}
                                                loadCardData={loadCardData}
                                            />
                                            {accountType ===
                                                MemberTypes.PRIMARY &&
                                                memberId && (
                                                    <SecondaryAccounts
                                                        memberId={memberId}
                                                        memberStatus={
                                                            member?.status
                                                        }
                                                        secondaryAccounts={
                                                            secondaryAccounts
                                                        }
                                                        isLoading={
                                                            isLoadingSecondaryAccounts
                                                        }
                                                        loadSecondaryAccounts={
                                                            loadSecondaryAccounts
                                                        }
                                                    />
                                                )}
                                            {accountType ===
                                                MemberTypes.SECONDARY &&
                                                member.parentMemberId && (
                                                    <PrimaryAccount
                                                        parentMemberId={
                                                            member.parentMemberId
                                                        }
                                                    />
                                                )}
                                            <Tags
                                                profileType="MEMBER"
                                                memberId={memberId}
                                                memberTags={member?.tags || []}
                                                hasEditPermission={isAuthorizedForAction(
                                                    AccessPermissionModuleNames.MEMBER,
                                                    AccessPermissionModules[
                                                        AccessPermissionModuleNames
                                                            .MEMBER
                                                    ].actions.UpdateMember
                                                )}
                                                isLoading={isLoading}
                                                loadProfile={loadProfile}
                                            />
                                            {member && (
                                                <CustomerPortal
                                                    portalData={
                                                        rest?.portalMetadata ||
                                                        {}
                                                    }
                                                />
                                            )}
                                            <MemberDetails
                                                member={member}
                                                loadProfile={loadProfile}
                                                setMember={setMember}
                                                profileType="MEMBER"
                                                hasEditPermission={isAuthorizedForAction(
                                                    AccessPermissionModuleNames.MEMBER,
                                                    AccessPermissionModules[
                                                        AccessPermissionModuleNames
                                                            .MEMBER
                                                    ].actions.UpdateMember
                                                )}
                                            />
                                        </div>
                                    </Col>
                                    <Col xl="9" lg="9" md="9">
                                        <div>
                                            <Tabs
                                                activeKey={tab}
                                                transition={false}
                                                id="noanim-tab-example"
                                                className="mb-3 border-solid-bottom"
                                                onSelect={setTab}
                                            >
                                                <Tab
                                                    eventKey={tabs.overview}
                                                    title={
                                                        <>
                                                            <IcIcon
                                                                className="mr-2"
                                                                size="w-10"
                                                                icon={
                                                                    faTachometerAlt
                                                                }
                                                            />
                                                            <span className="mr-2">
                                                                Overview
                                                            </span>
                                                        </>
                                                    }
                                                    disabled={isDisabledTab}
                                                />
                                                <Tab
                                                    eventKey={tabs.activityLog}
                                                    title={
                                                        <>
                                                            <IcIcon
                                                                className="mr-2"
                                                                size="w-10"
                                                                icon={
                                                                    faFileBookmarkAlt
                                                                }
                                                            />
                                                            <span className="mr-2">
                                                                Activity Log
                                                            </span>
                                                        </>
                                                    }
                                                    disabled={isDisabledTab}
                                                />
                                                <Tab
                                                    eventKey={tabs.history}
                                                    title={
                                                        <>
                                                            <IcIcon
                                                                className="mr-2"
                                                                size="w-10"
                                                                icon={faHistory}
                                                            />
                                                            <span className="mr-2">
                                                                History Events
                                                            </span>
                                                        </>
                                                    }
                                                    disabled={isDisabledTab}
                                                />
                                                <Tab
                                                    eventKey={tabs.points}
                                                    title={
                                                        <>
                                                            <IcIcon
                                                                className="mr-2"
                                                                size="w-10"
                                                                icon={faCoins}
                                                            />
                                                            <span className="mr-2">
                                                                Points
                                                            </span>
                                                        </>
                                                    }
                                                    disabled={isDisabledTab}
                                                />
                                                <Tab
                                                    eventKey={tabs.rewards}
                                                    title={
                                                        <>
                                                            <IcIcon
                                                                className="mr-2"
                                                                size="w-10"
                                                                icon={faGift}
                                                            />
                                                            <span className="mr-2">
                                                                Rewards
                                                            </span>
                                                        </>
                                                    }
                                                    disabled={isDisabledTab}
                                                />
                                                <Tab
                                                    eventKey={tabs.notes}
                                                    title={
                                                        <>
                                                            <IcIcon
                                                                className="mr-2"
                                                                size="w-10"
                                                                icon={faFile}
                                                            />
                                                            <span className="mr-2">
                                                                Notes
                                                            </span>
                                                        </>
                                                    }
                                                    disabled={isDisabledTab}
                                                />
                                            </Tabs>
                                            <DetailsTab
                                                tab={tab}
                                                member={member}
                                                selectedRegion={selectedRegion}
                                                rewards={rewards}
                                                memberRefreshMetadata={{
                                                    isReloadingAfterRedeem,
                                                    setIsReloadingAfterRedeem,
                                                    setMember,
                                                }}
                                                setIsDisabledTab={
                                                    setIsDisabledTab
                                                }
                                            />
                                        </div>
                                    </Col>
                                </Row>
                            )}
                        </>
                    }
                />
                {showSuspendAccount && (
                    <SuspendAccount
                        show={showSuspendAccount}
                        onHide={onHideSuspendAccount}
                        memberId={memberId}
                        loadProfile={loadProfile}
                        loadCardData={loadCardData}
                        loadSecondaryMembersData={loadSecondaryAccounts}
                        profileType={member?.type}
                    />
                )}
                {showConvertPrimary && (
                    <ConvertPrimary
                        show={showConvertPrimary}
                        onHide={onHideConvertPrimary}
                        memberId={memberId}
                        loadProfile={loadProfile}
                    />
                )}
                {showEdit && (
                    <EditBasicInfo
                        member={member}
                        type="Email Address"
                        value={email || ""}
                        show={showEdit}
                        profileType="MEMBER"
                        onHide={onHideEditEmail}
                        showEdit={setShowEdit}
                        loadProfile={loadProfile}
                    />
                )}
                {showNameEdit && (
                    <EditBasicInfo
                        member={member}
                        type="Name"
                        value={{
                            memberName: {
                                firstName: firstName,
                                lastName: lastName,
                            },
                        }}
                        show={showNameEdit}
                        onHide={onHideEditName}
                        showEdit={setShowNameEdit}
                        profileType="MEMBER"
                        loadProfile={loadProfile}
                    />
                )}
                {showMobileEdit && (
                    <EditBasicInfo
                        member={member}
                        type="Contact Number"
                        value={mobileNumber || ""}
                        show={showMobileEdit}
                        onHide={onHideEditMobile}
                        showEdit={setShowMobileEdit}
                        profileType="MEMBER"
                        loadProfile={loadProfile}
                    />
                )}
                {showAffinityGroup && (
                    <ChangeAffinityGroup
                        memberId={memberId}
                        show={showAffinityGroup}
                        onHide={onHideAffinityGroup}
                        affinityGroup={selectedAffinityGroup}
                        updatedAffinityGroup={updatedAffinityGroup}
                    />
                )}
                {showArchiveModal && (
                    <ArchiveAccount
                        show={showArchiveModal}
                        memberId={memberId}
                        member={member}
                        profileType="MEMBER"
                        onHide={onHideArchiveModal}
                        onNavigatingBack={onNavigatingBack}
                    />
                )}
                {showForgetModel && (
                    <ForgetAccount
                        show={showForgetModel}
                        memberId={memberId}
                        member={member}
                        profileType="MEMBER"
                        onHide={onHideForgetModal}
                        onNavigatingBack={onNavigatingBack}
                    />
                )}
                {showExportMemberModal && (
                    <AccountExportModal
                        show={showExportMemberModal}
                        onHide={onHideExportMemberModal}
                        memberId={memberId}
                        profileType="MEMBER"
                    />
                )}
                {showUpdateMemberVerificationModal && (
                    <UpdateMemberVerification
                        show={showUpdateMemberVerificationModal}
                        onHide={onHideMemberVerificationModal}
                        memberId={memberId}
                        toBeUpdatedVerificationStatus={
                            rest?.isVerified
                                ? MemberVerificationTypes.UNVERIFY
                                : MemberVerificationTypes.VERIFY
                        }
                    />
                )}
                {memberId &&
                    accountStatus === MemberStatus.SUSPENDED &&
                    showMemberReactivateWizard && (
                        <MemberReactivateWizard
                            memberId={memberId}
                            profileType={member?.type || ""}
                            suspendedCards={memberSuspendedCards}
                            previousCard={memberPrevCard}
                            secondaryMembers={secondaryAccounts}
                            loadProfile={loadProfile}
                            loadCardData={loadCardData}
                            loadSecondaryMembersData={loadSecondaryAccounts}
                        />
                    )}
                {pointAdjustModal()}
            </div>
        </CreateMemberContextProvider>
    );
};

export default MemberProfile;
