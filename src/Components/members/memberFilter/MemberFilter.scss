.member-filter {
    .query-builder-container {
        .query-builder {
            margin: 0;

            .group {
                background-color: transparent;
                border: none;
            }

            .input {
                height: 2rem !important;
            }
        }

        .select-typeahead .dropdown-menu .dropdown-item {
            word-wrap: break-word;
            /* IE 5.5-7 */
            white-space: -moz-pre-wrap;
            /* Firefox 1.0-2.0 */
            white-space: pre-wrap;
            /* current browsers */
        }

        .rule--field {
            width: 20rem !important;
        }

        .rule--operator {
            width: 15rem !important;
        }

        .rule--value {
            .merchant-location {
                width: 36rem !important;
            }
        }
    }
}
