import React, {
    use<PERSON>allback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import PropTypes from "prop-types";
import {
    Query,
    Builder,
    Utils as QbUtils,
} from "@shoutout-labs/react-awesome-query-builder-shoutout";
import { <PERSON><PERSON>, <PERSON> } from "@shoutout-labs/shoutout-themes-enterprise";
import { MembersContext, UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { useToggle } from "Hooks";
import { isEmptyObject, isEqualObjects } from "Utils";
import MemberCreateSegment from "../memberSegments/MemberSegmentCreate";

import "@shoutout-labs/react-awesome-query-builder-shoutout/lib/css/styles.css";
import "./MemberFilter.scss";

const queryValue = { id: QbUtils.uuid(), type: "group" };

const renderBuilder = (props) => (
    <div className="query-builder-container">
        <div className="query-builder qb-lite">
            <Builder {...props} />
        </div>
    </div>
);

const MemberFilter = ({ setSegmentKey }) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const {
        isLoading,
        filterConfig: config,
        currentFilters,
        setFilterConfig,
        setCurrentFilters,
        selectSegment,
        setIsFiltersApplied,
    } = useContext(MembersContext)||{};
    const [showCreateFilter, toggleShowCreateFilter] = useToggle(false);
    const [data, setData] = useState(
        QbUtils.checkTree(QbUtils.loadTree(queryValue), config)
    );

    const onChange = useCallback(
        (immutableTree, config) => {
            // * Tip: for better performance you can apply `throttle` - see `examples/demo`
            // * `jsonTree` can be saved to backend, and later loaded to `queryValue`
            // ? setState({ tree: immutableTree, config: config });
            // ? const jsonTree = QbUtils.getTree(immutableTree);
            setData(immutableTree);
            setFilterConfig(config);
        },
        [setFilterConfig, setData]
    );

    const onApplyFilter = useCallback(() => {
        setIsFiltersApplied(true);
        setCurrentFilters(QbUtils.getTree(data));
    }, [data, setCurrentFilters, setIsFiltersApplied]);

    const onClickSaveFilter = useCallback(() => {
        onApplyFilter();
        toggleShowCreateFilter();
    }, [onApplyFilter, toggleShowCreateFilter]);

    const onClickClearFilter = useCallback(() => {
        setData(QbUtils.checkTree(QbUtils.loadTree(queryValue), config));
        setCurrentFilters();
        setSegmentKey("");
        selectSegment({});
        setIsFiltersApplied(false);
    }, [
        config,
        selectSegment,
        setCurrentFilters,
        setSegmentKey,
        setIsFiltersApplied,
        setData,
    ]);

    const hasFilters = useMemo(() => {
        return !isEqualObjects(QbUtils.getTree(data), queryValue);
    }, [data]);

    useEffect(() => {
        if (
            currentFilters &&
            !isEmptyObject(currentFilters) &&
            !isEqualObjects(QbUtils.getTree(data), currentFilters)
        ) {
            setData(
                QbUtils.checkTree(QbUtils.loadTree(currentFilters), config)
            );
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentFilters]);

    return (
        <Card className="member-filter">
            <Card.Body>
                <Query
                    {...config}
                    value={data}
                    onChange={onChange}
                    renderBuilder={renderBuilder}
                />

                {hasFilters && (
                    <div className="text-center mt-4">
                        <Button
                            className="mr-2"
                            variant="outline-primary"
                            size="sm"
                            disabled={
                                isLoading ||
                                isEqualObjects(
                                    QbUtils.getTree(data),
                                    currentFilters
                                )
                            }
                            onClick={onApplyFilter}
                        >
                            Apply Filter
                        </Button>
                        {isAuthorizedForAction(
                            AccessPermissionModuleNames.SEGMENT,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.SEGMENT
                            ].actions.CreateSegment
                        ) && (
                            <Button
                                className="mr-2"
                                variant="outline-primary"
                                size="sm"
                                disabled={isLoading}
                                onClick={onClickSaveFilter}
                            >
                                Save Filter
                            </Button>
                        )}
                        <Button
                            variant="outline-danger"
                            size="sm"
                            disabled={isLoading}
                            onClick={onClickClearFilter}
                        >
                            Clear Filter
                        </Button>
                    </div>
                )}
            </Card.Body>
            {showCreateFilter && (
                <MemberCreateSegment
                    show={showCreateFilter}
                    onHide={toggleShowCreateFilter}
                />
            )}
        </Card>
    );
};

MemberFilter.defaultProps = {
    setSegmentKey: () => {},
};

MemberFilter.propTypes = {
    setSegmentKey: PropTypes.func,
};

export default MemberFilter;
