import React, { useCallback, useContext } from "react";
import { toast } from "react-toastify";
import { Button, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext, UserContext } from "Contexts";
import { deleteSegment } from "Services";

const DeleteMemberSegment = ({
    show,
    onHide,
    segmentToDelete,
    isDeletingSegment,
    setIsDeletingSegment,
}) => {
    const { regionId } = useContext(UserContext);
    const { loadSegments } = useContext(DataContext);

    const confirmDeleteSegment = useCallback(async () => {
        try {
            setIsDeletingSegment(true);
            await deleteSegment(segmentToDelete?._id);
            setIsDeletingSegment(false);
            loadSegments(regionId);
            toast.success(
                `Successfully deleted the segment "${
                    segmentToDelete?.name || ""
                }".`
            );
            onHide();
        } catch (e) {
            setIsDeletingSegment(false);
            console.error(e);
            toast.error(
                <div>
                    Failed to delete the segment {segmentToDelete?.name || ""}!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        regionId,
        segmentToDelete?._id,
        segmentToDelete?.name,
        onHide,
        loadSegments,
        setIsDeletingSegment,
    ]);

    return (
        <Modal show={show} onHide={onHide} size="md" backdrop="static" centered>
            <Modal.Header closeButton={!isDeletingSegment}>
                <Modal.Title>Delete Segment</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                Are you sure you want to delete the segment "
                {segmentToDelete?.name || ""}"?
            </Modal.Body>
            <Modal.Footer>
                <Button
                    variant="outline-primary"
                    size="sm"
                    disabled={isDeletingSegment}
                    onClick={onHide}
                >
                    Cancel
                </Button>
                <Button
                    variant="danger"
                    size="sm"
                    disabled={isDeletingSegment}
                    onClick={confirmDeleteSegment}
                >
                    {isDeletingSegment ? "Deleting..." : "Delete Segment"}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default DeleteMemberSegment;
