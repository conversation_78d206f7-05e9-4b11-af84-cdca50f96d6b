import React, { useContext, useCallback, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON> } from "@shoutout-labs/shoutout-themes-enterprise";
import { CardTypes, MemberTypes } from "Data";
import { toTitleCase } from "Utils";
import { CreateMemberContext } from "./context/EnrollMembersContext";
import EnrollMemberFirstPage from "./EnrollMemberFirstPage";
import EnrollMemberSecondPage from "./EnrollMemberSecondPage";
import EnrollMemberThirdPage from "./EnrollMemberThirdPage";

import "./EnrollMemberWizard.scss";

const EnrollMemberWizard = () => {
    const {
        setShowMemberEnrollWizard,
        showMemberWizard: show,
        isGenerating,
        isCreating,
        isAssigning,
        memberAction,
        reset,
        isLoadingNearestStore,
    } = useContext(CreateMemberContext);

    const modalTitle = useMemo(() => {
        switch (memberAction) {
            case "charity":
                return "Create Charity";
            case "enroll":
            case "addSecondary": {
                return `Enrol${
                    memberAction === "addSecondary" ? " Secondary " : " "
                }Member`;
            }
            default:
                return "";
        }
    }, [memberAction]);

    const finishButtonText = useMemo(() => {
        if (isGenerating)
            return `Generating a ${toTitleCase(CardTypes.DIGITAL_CARD)}...`;
        else if (isCreating) {
            switch (memberAction) {
                case "charity":
                    return "Creating Charity...";
                case "enroll":
                case "addSecondary": {
                    return `Enrolling Member${
                        memberAction === "addSecondary"
                            ? " as a " +
                                toTitleCase(MemberTypes.SECONDARY) +
                                " Member"
                            : ""
                    }...`;
                }
                default:
                    return "Creating...";
            }
        } else if (isAssigning)
            return `Assigning Card to ${
                memberAction === "charity" ? "Charity" : "Member"
            }...`;
        else return memberAction === "charity" ? "Create" : "Enrol";
    }, [memberAction, isGenerating, isCreating, isAssigning]);

    const handleClose = useCallback(() => {
        reset(memberAction);
        setShowMemberEnrollWizard(false);
    }, [reset, memberAction, setShowMemberEnrollWizard]);

    return useMemo(
        () => (
            <EnrollMemberWizardView
                handleClose={handleClose}
                show={show}
                modalTitle={modalTitle}
                finishButtonText={finishButtonText}
                disabled={
                    isGenerating ||
                    isCreating ||
                    isAssigning ||
                    isLoadingNearestStore
                }
            />
        ),
        [
            show,
            isGenerating,
            isCreating,
            isAssigning,
            isLoadingNearestStore,
            modalTitle,
            finishButtonText,
            handleClose,
        ]
    );
};

const EnrollMemberWizardView = ({
    show,
    modalTitle,
    finishButtonText,
    disabled,
    handleClose,
}) => (
    <Modal
        show={show}
        className="member-wizard border-0"
        size="lg"
        centered
        backdrop="static"
        onHide={handleClose}
    >
        <Modal.Header closeButton={!disabled}>
            <Modal.Title>{modalTitle}</Modal.Title>
        </Modal.Header>
        <Modal.Body className="pt-3 mt-2 px-2">
            <Wizard
                validate
                finishButtonClick={() => {}}
                color="primary"
                heightClass="point-wizard-height"
                disabled={disabled}
                finishButtonText={finishButtonText}
                steps={[
                    {
                        stepName: "Profile Info",
                        component: EnrollMemberFirstPage,
                        showNextBtn: true,
                    },
                    {
                        stepName: "Contact Info",
                        component: EnrollMemberSecondPage,
                        showNextBtn: true,
                    },
                    {
                        stepName: "Preferences",
                        component: EnrollMemberThirdPage,
                        showNextBtn: true,
                    },
                ]}
            />
        </Modal.Body>
    </Modal>
);

export default EnrollMemberWizardView;

export { EnrollMemberWizard };
