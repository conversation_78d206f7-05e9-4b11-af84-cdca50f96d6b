import React, { useReducer, useCallback, useContext, useEffect } from "react";
import { toast } from "react-toastify";
import moment from "moment";
import { DataContext, MembersContext, UserContext } from "Contexts";
import { CardTypes, MemberTypes, MerchantLocationStatusObj } from "Data";
import {
    assignCardToMember,
    createNewCharity,
    createNewUser,
    getAllMerchantLocations,
    manualGenerateDigitalCards,
} from "Services";
import { toTitleCase } from "Utils";

const CreateMemberContext = React.createContext();

const memberStatus = {
    loyaltyCardNumber: "",
    systemGenerateDigitalCard: false,
    manualGenerateDigitalCard: false,
    firstName: "",
    lastName: "",
    dateOfBirth: "",
    iDType: "",
    iDNumber: "",
    gender: "",
    company: "",
    occupation: "",
    mobileNumber: "",
    landLineNumber: "",
    email: "",
    line1: "",
    line2: "",
    line3: "",
    zipOrPostcode: "",
    country: [],
    stateOrProvince: [],
    city: [],
    nearStore: [],
    preferredContact: "",
    showMemberWizard: false,
    showCardSearch: false,
    isGenerating: false,
    isCreating: false,
    isAssigning: false,
    memberAction: "enroll",
    memberId: "",
    charityName: "",
    description: "",
    logoUrl: "",
    showImageEditor: false,
    isAgreedPromotions: false,
    validMobileNumber: false,
    validLandLineNumber: false,
};

const initialState = {
    ...memberStatus,
    nearestStore: [],
    isLoadingNearestStore: false,
};

const CreateMemberContextActions = {
    SET_PAGEVALUE: "setPageValue",
    RESET: "reset",
    SET_CARD_NUMBER: "setCardNumber",
    SET_SYSTEM_GENERATE_DIGITAL_CARD: "setSystemGenerateDigitalCard",
    SET_MANUAL_GENERATE_DIGITAL_CARD: "setManualGenerateDigitalCard",
    SET_MOBILE_NUMBER: "setMobileNumber",
    SET_LAND_NUMBER: "setLandNumber",
    SET_COUNTRY: "setCountry",
    SET_DEFAULT_COUNTRY: "setDefaultCountry",
    SET_STATE: "setState",
    SET_CITY: "setCity",
    SET_BIRTHDATE: "setBirthdate",
    SET_SHOW_WIZARD: "setShowWizard",
    SET_CARD_SEARCH: "setCardSearch",
    SET_IS_CREATING: "setIsCreating",
    SET_LOADING_STATES: "setLoadingStates",
    SET_MEMBER_ACTION: "setMemberAction",
    SET_MEMBER_ID: "setMemberId",
    SET_LOGO_URL: "setLogoUrl",
    SET_SHOW_IMAGE_EDITOR: "setShowImageEditor",
    SET_NEAREST_STORE: "setNearestStore",
    SET_IS_LOADING_NEAREST_STORE: "setIsLoadingNearestStore",
    SET_SELECTED_NEAREST_STORE: "setSelectedNearestStore",
};

const reducer = (state, action) => {
    switch (action.type) {
        case CreateMemberContextActions.SET_PAGEVALUE: {
            return {
                ...state,
                [action.key]: action.value,
            };
        }
        case CreateMemberContextActions.SET_CARD_NUMBER: {
            return {
                ...state,
                loyaltyCardNumber: action.loyaltyCardNumber,
            };
        }
        case CreateMemberContextActions.SET_SYSTEM_GENERATE_DIGITAL_CARD: {
            return {
                ...state,
                systemGenerateDigitalCard: action.systemGenerateDigitalCard,
            };
        }
        case CreateMemberContextActions.SET_MANUAL_GENERATE_DIGITAL_CARD: {
            return {
                ...state,
                manualGenerateDigitalCard: action.manualGenerateDigitalCard,
            };
        }
        case CreateMemberContextActions.SET_MOBILE_NUMBER: {
            return {
                ...state,
                mobileNumber: action.mobileNumber,
                validMobileNumber: action.status,
            };
        }
        case CreateMemberContextActions.SET_LAND_NUMBER: {
            return {
                ...state,
                landLineNumber: action.landLineNumber,
                validLandLineNumber: action.status,
            };
        }
        case CreateMemberContextActions.SET_COUNTRY: {
            return {
                ...state,
                country: action.country,
            };
        }
        case CreateMemberContextActions.SET_DEFAULT_COUNTRY: {
            return {
                ...state,
                country: action.country,
            };
        }
        case CreateMemberContextActions.SET_STATE: {
            return {
                ...state,
                stateOrProvince: action.stateOrProvince,
            };
        }
        case CreateMemberContextActions.SET_CITY: {
            return {
                ...state,
                city: action.city,
            };
        }
        case CreateMemberContextActions.SET_BIRTHDATE: {
            return {
                ...state,
                dateOfBirth: action.dateOfBirth,
            };
        }
        case CreateMemberContextActions.SET_SHOW_WIZARD: {
            return {
                ...state,
                showMemberWizard: action.showMemberWizard,
            };
        }
        case CreateMemberContextActions.SET_CARD_SEARCH: {
            return {
                ...state,
                showCardSearch: action.showCardSearch,
            };
        }
        case CreateMemberContextActions.SET_MEMBER_ACTION: {
            return {
                ...state,
                memberAction: action.memberAction,
            };
        }
        case CreateMemberContextActions.SET_MEMBER_ID: {
            return {
                ...state,
                memberId: action.memberId,
            };
        }
        case CreateMemberContextActions.RESET: {
            return {
                ...state,
                ...memberStatus,
                memberAction: action.memberAction,
            };
        }
        case CreateMemberContextActions.SET_IS_CREATING: {
            return {
                ...state,
                isCreating: action.status,
            };
        }
        case CreateMemberContextActions.SET_LOADING_STATES: {
            return {
                ...state,
                [action.key]: action.status,
            };
        }
        case CreateMemberContextActions.SET_LOGO_URL: {
            return {
                ...state,
                logoUrl: action.logoUrl,
            };
        }
        case CreateMemberContextActions.SET_SHOW_IMAGE_EDITOR: {
            return {
                ...state,
                showImageEditor: action.status,
            };
        }
        case CreateMemberContextActions.SET_NEAREST_STORE: {
            return {
                ...state,
                nearestStore: action.nearestStore,
            };
        }
        case CreateMemberContextActions.SET_IS_LOADING_NEAREST_STORE: {
            return {
                ...state,
                isLoadingNearestStore: action.isLoadingNearestStore,
            };
        }
        case CreateMemberContextActions.SET_SELECTED_NEAREST_STORE: {
            return {
                ...state,
                nearStore: action.selectedNearestStore,
            };
        }
        default:
            return state;
    }
};

const getResidentialAddress = ({
    line1,
    line2,
    line3,
    city,
    stateOrProvince,
    zipOrPostcode,
}) => {
    if (
        !line1 &&
        !line2 &&
        !line3 &&
        !city &&
        !stateOrProvince &&
        !zipOrPostcode
    )
        return {};

    return {
        residentialAddress: {
            ...(line1 ? { line1 } : {}),
            ...(line2 ? { line2 } : {}),
            ...(line3 ? { line3 } : {}),
            ...(city ? { city } : {}),
            ...(stateOrProvince ? { stateOrProvince } : {}),
            ...(zipOrPostcode ? { zipOrPostcode } : {}),
        },
    };
};

const CreateMemberContextProvider = ({
    loadSecondaryAccounts,
    children,
    refreshTable,
}) => {
    const { selectedRegion } = useContext(UserContext);
    const { merchants, loadCharities } = useContext(DataContext);
    const { loadMembers } = useContext(MembersContext);
    const [state, dispatch] = useReducer(reducer, initialState);

    const setMemberAction = useCallback(
        (memberAction) => {
            dispatch({
                type: CreateMemberContextActions.SET_MEMBER_ACTION,
                memberAction,
            });
        },
        [dispatch]
    );

    const onSetLogoImage = useCallback(
        (image) => {
            dispatch({
                type: CreateMemberContextActions.SET_LOGO_URL,
                logoUrl: image,
            });
        },
        [dispatch]
    );

    const setMemberId = useCallback(
        (memberId) => {
            dispatch({
                type: CreateMemberContextActions.SET_MEMBER_ID,
                memberId,
            });
        },
        [dispatch]
    );

    const setLoadingStates = useCallback(
        (key, status) => {
            dispatch({
                type: CreateMemberContextActions.SET_LOADING_STATES,
                key,
                status,
            });
        },
        [dispatch]
    );

    const setPageValue = useCallback(
        (event) => {
            dispatch({
                type: CreateMemberContextActions.SET_PAGEVALUE,
                key: event.target.name,
                value: event.target.value,
            });
        },
        [dispatch]
    );

    const onSelectNearestStore = useCallback(
        (selectedNearestStore) => {
            dispatch({
                type: CreateMemberContextActions.SET_SELECTED_NEAREST_STORE,
                selectedNearestStore,
            });
        },
        [dispatch]
    );

    const getNearestStore = useCallback(async () => {
        try {
            dispatch({
                type: CreateMemberContextActions.SET_IS_LOADING_NEAREST_STORE,
                isLoadingNearestStore: true,
            });
            const merchantsIdNameMap = merchants.reduce((result, merchant) => {
                result[merchant?._id] = merchant?.merchantName;
                return result;
            }, {});

            const locationsResponse = await getAllMerchantLocations({
                regionId: selectedRegion?._id,
                nearestLocations: true,
                status: MerchantLocationStatusObj.ACTIVE,
            });
            const nearestStore = locationsResponse
                .filter((item) => item.options?.enroll)
                .map((location) => ({
                    ...location,
                    merchantName: merchantsIdNameMap[location?.merchantId],
                    locationName: `${
                        location.code ? location.code + " / " : ""
                    }${location.locationName}`,
                }));
            dispatch({
                type: CreateMemberContextActions.SET_NEAREST_STORE,
                nearestStore,
            });
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load nearest stores!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            dispatch({
                type: CreateMemberContextActions.SET_IS_LOADING_NEAREST_STORE,
                isLoadingNearestStore: false,
            });
        }
    }, [merchants, selectedRegion?._id]);

    const setLoyaltyCardNo = useCallback(
        (loyaltyCardNumber) => {
            dispatch({
                type: CreateMemberContextActions.SET_CARD_NUMBER,
                loyaltyCardNumber,
            });
        },
        [dispatch]
    );

    const setSystemGenerateDigitalCard = useCallback(
        (value = false) => {
            dispatch({
                type: CreateMemberContextActions.SET_SYSTEM_GENERATE_DIGITAL_CARD,
                systemGenerateDigitalCard: value,
            });
        },
        [dispatch]
    );

    const setManualGenerateDigitalCard = useCallback(
        (value = false) => {
            dispatch({
                type: CreateMemberContextActions.SET_MANUAL_GENERATE_DIGITAL_CARD,
                manualGenerateDigitalCard: value,
            });
        },
        [dispatch]
    );

    const setMobileNo = useCallback(
        (mobileNumber, status) => {
            dispatch({
                type: CreateMemberContextActions.SET_MOBILE_NUMBER,
                mobileNumber,
                status,
            });
        },
        [dispatch]
    );

    const setLandNo = useCallback(
        (landLineNumber, status) => {
            dispatch({
                type: CreateMemberContextActions.SET_LAND_NUMBER,
                landLineNumber,
                status,
            });
        },
        [dispatch]
    );

    const setCountry = useCallback(
        (country) => {
            dispatch({
                type: CreateMemberContextActions.SET_COUNTRY,
                country,
            });
        },
        [dispatch]
    );

    const setDefaultCountry = useCallback(() => {
        dispatch({
            type: CreateMemberContextActions.SET_DEFAULT_COUNTRY,
            country: [
                {
                    currency: selectedRegion.defaultCurrencyCode,
                    flag: selectedRegion.defaultCountryISO2Code,
                    isoCode: selectedRegion.defaultCountryISO2Code,
                    name: selectedRegion.regionName,
                },
            ],
        });
    }, [dispatch, selectedRegion]);

    const setState = useCallback(
        (stateOrProvince) => {
            dispatch({
                type: CreateMemberContextActions.SET_STATE,
                stateOrProvince,
            });
        },
        [dispatch]
    );

    const setCity = useCallback(
        (city) => {
            dispatch({
                type: CreateMemberContextActions.SET_CITY,
                city,
            });
        },
        [dispatch]
    );

    const setBirthDate = useCallback(
        (dateOfBirth) => {
            dispatch({
                type: CreateMemberContextActions.SET_BIRTHDATE,
                dateOfBirth,
            });
        },
        [dispatch]
    );

    const setShowMemberEnrollWizard = useCallback(
        (showMemberWizard) => {
            dispatch({
                type: CreateMemberContextActions.SET_SHOW_WIZARD,
                showMemberWizard,
            });
        },
        [dispatch]
    );

    const setShowCardSearch = useCallback(
        (showCardSearch) => {
            dispatch({
                type: CreateMemberContextActions.SET_CARD_SEARCH,
                showCardSearch,
            });
        },
        [dispatch]
    );

    const onClickOnCheckbox = useCallback(
        (e) => {
            dispatch({
                type: CreateMemberContextActions.SET_PAGEVALUE,
                key: e.target.name,
                value: e.target.checked,
            });
        },
        [dispatch]
    );

    const toggleShowEnrollMemeberImageEditor = useCallback(() => {
        if (state.showMemberWizard) {
            dispatch({
                type: CreateMemberContextActions.SET_SHOW_WIZARD,
                showMemberWizard: false,
            });
            dispatch({
                type: CreateMemberContextActions.SET_SHOW_IMAGE_EDITOR,
                status: true,
            });
        } else if (state.showImageEditor) {
            dispatch({
                type: CreateMemberContextActions.SET_SHOW_IMAGE_EDITOR,
                status: false,
            });
            dispatch({
                type: CreateMemberContextActions.SET_SHOW_WIZARD,
                showMemberWizard: true,
            });
        }
    }, [state.showMemberWizard, state.showImageEditor, dispatch]);

    const reset = useCallback(
        (memberAction) => {
            dispatch({ type: CreateMemberContextActions.RESET, memberAction });
        },
        [dispatch]
    );

    const generateDigitalCardNumber = useCallback(
        async (numberToGenerateAsCard) => {
            try {
                let loyaltyCardToBeAssigned;

                setLoadingStates("isGenerating", true);

                const generatedCards = await manualGenerateDigitalCards({
                    regionId: selectedRegion._id,
                    ...(state.systemGenerateDigitalCard
                        ? { autoGenerate: true }
                        : { cardNumbers: [numberToGenerateAsCard] }),
                });
                loyaltyCardToBeAssigned = state.systemGenerateDigitalCard
                    ? generatedCards[0]?.cardNoStr || null
                    : numberToGenerateAsCard;

                if (state.systemGenerateDigitalCard)
                    setLoyaltyCardNo(generatedCards[0]?.cardNoStr || null);

                setLoadingStates("isGenerating", false);
                setSystemGenerateDigitalCard(false);
                setManualGenerateDigitalCard(false);

                return loyaltyCardToBeAssigned;
            } catch (e) {
                console.error(e);
                setLoadingStates("isGenerating", false);
                toast.error(
                    <div>
                        {`Failed to generate a ${toTitleCase(
                            CardTypes.DIGITAL_CARD
                        )}!`}
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
                return Promise.reject(e);
            }
        },
        [
            selectedRegion._id,
            state.systemGenerateDigitalCard,
            setLoadingStates,
            setLoyaltyCardNo,
            setManualGenerateDigitalCard,
            setSystemGenerateDigitalCard,
        ]
    );

    const assignCardNumber = useCallback(
        async ({ cardData }) => {
            try {
                setLoadingStates("isAssigning", true);
                const payload = {
                    memberId: cardData.memberId,
                    cardNumberStr: cardData.cardNumber?.toString(),
                };
                await assignCardToMember(payload);
                setLoadingStates("isAssigning", false);
                return null;
            } catch (e) {
                console.error("Failed to assign the card");
                console.error(e);
                setLoadingStates("isAssigning", false);
                return "CARD_ASSIGN_FAILED";
            }
        },
        [setLoadingStates]
    );

    const createMember = useCallback(async () => {
        try {
            if (state.nearStore.length === 0)
                throw new Error("Nearest store location is required.");
            const residentialAddress = getResidentialAddress({
                line1: state?.line1,
                line2: state?.line2,
                line3: state?.line3,
                city: state?.city[0]?.value,
                stateOrProvince: state?.stateOrProvince[0]?.value,
                zipOrPostcode: state?.zipOrPostcode,
            });

            const newUser = {
                regionId: selectedRegion._id,
                merchantLocationId: state.nearStore[0]?._id,
                type: MemberTypes.PRIMARY,
                firstName: state.firstName,
                mobileNumber: state.mobileNumber,
                gender: state?.gender?.toUpperCase(),
                country:
                    state.country && state.country?.length > 0
                        ? state.country[0]?.name
                        : selectedRegion?.regionName,
                notificationPreference: {
                    preferredChannel: state?.preferredContact,
                    allowPromotionalNotifications: !!state?.isAgreedPromotions,
                },
                ...(state?.lastName ? { lastName: state.lastName } : {}),
                ...(state?.email ? { email: state.email } : {}),
                ...(state?.dateOfBirth
                    ? {
                        birthDate: moment(state.dateOfBirth).format(
                            "YYYY-MM-DD"
                        ),
                    }
                    : {}),
                ...(state?.companyName
                    ? { companyName: state.companyName }
                    : {}),
                ...(state?.occupation ? { occupation: state.occupation } : {}),
                ...(state.iDType && state.iDNumber
                    ? {
                        identifications: [
                            {
                                identificationType: state.iDType,
                                identificationNumber: state.iDNumber,
                            },
                        ],
                    }
                    : {}),
                ...residentialAddress,
            };

            if (state.memberAction === "addSecondary") {
                newUser.type = MemberTypes.SECONDARY;
                newUser.parentMemberId = state.memberId;
            }

            let loyaltyCardToBeAssigned = state.loyaltyCardNumber || null;
            if (
                state.systemGenerateDigitalCard ||
                state.manualGenerateDigitalCard
            ) {
                try {
                    loyaltyCardToBeAssigned = await generateDigitalCardNumber(
                        loyaltyCardToBeAssigned
                    );
                } catch (e) {
                    return Promise.reject(e);
                }
            }

            dispatch({
                type: CreateMemberContextActions.SET_IS_CREATING,
                status: true,
            });
            const createdMember = await createNewUser(newUser);
            dispatch({
                type: CreateMemberContextActions.SET_IS_CREATING,
                status: false,
            });

            const newMemberId = createdMember._id;
            const loyaltyCardDetails = {
                memberId: newMemberId,
                cardNumber: loyaltyCardToBeAssigned,
            };

            const cardAssignStatus = await assignCardNumber({
                cardData: loyaltyCardDetails,
            });

            if (state.memberAction === "enroll") {
                loadMembers({
                    shouldReset: true,
                    skip: 1,
                    countMembers: true,
                });
            } else {
                loadSecondaryAccounts();
            }
            reset(state.memberAction);

            return {
                response: createdMember,
                error: cardAssignStatus || null,
                ...(cardAssignStatus
                    ? { failedToAssignCard: loyaltyCardToBeAssigned }
                    : {}),
            };
        } catch (e) {
            console.error(e);
            setLoadingStates("isGenerating", false);
            dispatch({
                type: CreateMemberContextActions.SET_IS_CREATING,
                status: false,
            });
            setLoadingStates("isAssigning", false);

            if (state.memberAction === "enroll") {
                toast.error(
                    <div>
                        Failed to enrol member!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } else {
                toast.error(
                    <div>
                        Failed to enrol a {MemberTypes.SECONDARY} member!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }

            return Promise.reject(e);
        }
    }, [
        selectedRegion,
        state.nearStore,
        state.firstName,
        state.mobileNumber,
        state?.gender,
        state.country,
        state?.lastName,
        state.email,
        state.dateOfBirth,
        state.companyName,
        state.occupation,
        state.iDType,
        state.iDNumber,
        state.line1,
        state.line2,
        state.line3,
        state.stateOrProvince,
        state.city,
        state.zipOrPostcode,
        state?.preferredContact,
        state.isAgreedPromotions,
        state.memberAction,
        state.loyaltyCardNumber,
        state.systemGenerateDigitalCard,
        state.manualGenerateDigitalCard,
        state.memberId,
        loadSecondaryAccounts,
        loadMembers,
        generateDigitalCardNumber,
        assignCardNumber,
        reset,
        setLoadingStates,
        dispatch,
    ]);

    const createCharity = useCallback(async () => {
        try {
            if (state.nearStore.length === 0)
                throw new Error("Nearest store location is required.");

            const residentialAddress = getResidentialAddress({
                line1: state?.line1,
                line2: state?.line2,
                line3: state?.line3,
                city: state?.city[0]?.value,
                stateOrProvince: state?.stateOrProvince[0]?.value,
                zipOrPostcode: state?.zipOrPostcode,
            });

            const payload = {
                regionId: selectedRegion._id,
                profilePicture: state.logoUrl,
                preferredName: state.charityName,
                description: state.description,
                type: MemberTypes.CHARITY,
                merchantLocationId: state.nearStore[0]?._id,
                mobileNumber: state.mobileNumber,
                country:
                    state.country && state.country?.length > 0
                        ? state.country[0]?.name
                        : selectedRegion?.regionName,
                ...(state?.email ? { email: state.email } : {}),
                ...residentialAddress,
            };

            dispatch({
                type: CreateMemberContextActions.SET_IS_CREATING,
                status: true,
            });

            let loyaltyCardToBeAssigned = state.loyaltyCardNumber || null;
            if (
                state.systemGenerateDigitalCard ||
                state.manualGenerateDigitalCard
            ) {
                try {
                    loyaltyCardToBeAssigned = await generateDigitalCardNumber(
                        loyaltyCardToBeAssigned
                    );
                } catch (e) {
                    return Promise.reject(e);
                }
            }

            dispatch({
                type: CreateMemberContextActions.SET_IS_CREATING,
                status: true,
            });

            const createdCharity = await createNewCharity(payload);
            dispatch({
                type: CreateMemberContextActions.SET_IS_CREATING,
                status: false,
            });

            const newCharityId = createdCharity._id;
            const loyaltyCardDetails = {
                memberId: newCharityId,
                cardNumber: loyaltyCardToBeAssigned,
            };

            const cardAssignStatus = await assignCardNumber({
                cardData: loyaltyCardDetails,
            });

            loadCharities(selectedRegion._id);
            refreshTable();
            reset(state.memberAction);

            return {
                response: createdCharity,
                error: cardAssignStatus || null,
                ...(cardAssignStatus
                    ? { failedToAssignCard: loyaltyCardToBeAssigned }
                    : {}),
            };
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to create a charity account!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
            dispatch({
                type: CreateMemberContextActions.SET_IS_CREATING,
                status: false,
            });

            return Promise.reject(e);
        }
    }, [
        selectedRegion,
        state.nearStore,
        state.logoUrl,
        state.charityName,
        state.description,
        state.mobileNumber,
        state.email,
        state.country,
        state.line1,
        state.line2,
        state.line3,
        state.stateOrProvince,
        state.city,
        state.zipOrPostcode,
        state.loyaltyCardNumber,
        state.systemGenerateDigitalCard,
        state.manualGenerateDigitalCard,
        state.memberAction,
        refreshTable,
        loadCharities,
        generateDigitalCardNumber,
        assignCardNumber,
        reset,
        dispatch,
    ]);

    const value = {
        ...state,
        setPageValue,
        setLoyaltyCardNo,
        setMobileNo,
        setLandNo,
        setCountry,
        setState,
        setCity,
        setBirthDate,
        setShowMemberEnrollWizard,
        setShowCardSearch,
        reset,
        createMember,
        setMemberAction,
        setMemberId,
        onSetLogoImage,
        createCharity,
        toggleShowEnrollMemeberImageEditor,
        onClickOnCheckbox,
        onSelectNearestStore,
        getNearestStore,
        setSystemGenerateDigitalCard,
        setManualGenerateDigitalCard,
    };

    useEffect(() => {
        if (state.showMemberWizard) {
            setDefaultCountry();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedRegion, state.showMemberWizard]);

    useEffect(() => {
        if (selectedRegion._id) {
            getNearestStore();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <CreateMemberContext.Provider value={value}>
            {children}
        </CreateMemberContext.Provider>
    );
};

const CreateMemberContextConsumer = CreateMemberContext.Consumer;

export {
    CreateMemberContextProvider,
    CreateMemberContext,
    CreateMemberContextConsumer,
};
