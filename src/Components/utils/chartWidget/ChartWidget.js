import React from "react";
import {
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "recharts";

const ChartWidget = ({
  dataset,
  barOptions = { xAxis: {}, bar: [] },
}) => {

  return (
    <div className="bar-chart-widget text-small">
      <ResponsiveContainer
        width="100%"
        height={300}
        margin={{ top: 0, right: 10, left: 10, bottom: 0 }}
      >
        <BarChart
          width="100%"
          height={250}
          data={dataset}
          margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
        >
          <XAxis allowDecimals={false}
            type={barOptions.xAxis.type || "category"}
            dataKey={barOptions.xAxis.key}
            orientation="bottom"
            tick={{ fontSize: 10 }} />
          <YAxis />
          <Tooltip />

          {barOptions.bar.map((item) => {
            return (
              <Bar
                dataKey={item.key}
                stackId="a"
                key={item.key}
                fill="#00AEFF"
              />
            );
          })
          }
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ChartWidget;
