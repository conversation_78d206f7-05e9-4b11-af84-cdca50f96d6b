import React from 'react';
import './ToggleButton.css';

const ToggleButton = ({checked, onChange,...rest}) => {

    return (
        <>
            <label className="switch mb-0" data-testid="switch">
                <input type="checkbox" checked={checked} onChange={onChange} {...rest} data-testid="switch-input"/>
                <span className="slider round" data-testid="switch-span"/>
            </label>
        </>
    )
}

export default ToggleButton
