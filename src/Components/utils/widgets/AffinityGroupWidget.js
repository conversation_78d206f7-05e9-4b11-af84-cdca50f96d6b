import React, { useCallback, useContext, useMemo } from "react";
import { Form, FormSelect } from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext } from "../../../Contexts";

const AffinityGroupWidget=(props)=>{
    const {affinityGroups } = useContext(DataContext);
    const { value, onChange } = props

    const handleChange = useCallback( val =>
        onChange(val.length!==0?val[0].value:"")
    , [onChange]);

    const selectedAffinityGroup = useMemo( () =>
        (affinityGroups&&value)?
            affinityGroups.filter(affinityGroup=>affinityGroup._id===value)
                .map(affinityGroup => (
                        {
                            label: affinityGroup.name,
                            value: affinityGroup._id,
                            name:"affinityGroup",
                        }
                 )):[]
        , [affinityGroups, value]);

    return (
        <Form.Group controlId="affinity-group">
            <FormSelect
                clearButton
                onChange={handleChange}
                multiple={false}
                options={affinityGroups ? affinityGroups.map(affinityGroup => (
                    {
                        label: affinityGroup.name,
                        value: affinityGroup._id,
                        name:"affinityGroup",
                    }
                )):[]}
                placeholder="Select Affinity Groups"
                required
                selected={selectedAffinityGroup}
            />
        </Form.Group>
    )
}

export default AffinityGroupWidget
