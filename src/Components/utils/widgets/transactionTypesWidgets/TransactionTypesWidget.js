import React, { useCallback, useMemo } from "react";
import { Form, FormSelect } from "@shoutout-labs/shoutout-themes-enterprise";
import { TransactionTypes } from "Data";
import { objectToLabelValueArray } from "Utils";

import "./TransactionTypesWidget.scss";

const TransactionTypesOptions = objectToLabelValueArray(TransactionTypes);

const TransactionTypesWidget = (props) => {
    const { value, onChange, options } = props;
    const { isValidated } = options;
    const invertedValue= value?JSON.parse(value):value;

    const transactionType = useMemo(
        () =>
            TransactionTypesOptions&&invertedValue
                ? TransactionTypesOptions.filter((transactionType) =>
                    invertedValue.includes(transactionType.value)
                )
                : invertedValue === null
                    ? [{
                        label:"ALL Transaction types",
                        value:null,
                        name: "All",
                    }]
                    : [],
        [invertedValue]
    );

    const onSelectTransactionType = useCallback(
        (val) => {
            let transactionTypeIds = [];
            val.forEach((item) => {
                if (item.name === "All") {
                    transactionTypeIds = null;
                } else if (transactionTypeIds !== null) {
                    transactionTypeIds.push(item.value);
                }
            });
            onChange(JSON.stringify(transactionTypeIds));
        },
        [onChange]
    );

    return (
        <Form.Group
            className="transaction-types-widget-view"
            controlId="transactionType"
        >
            <Form.Label className="d-flex align-items-center">
                Transaction Type
                <div className="ml-1 text-danger">*</div>
            </Form.Label>
            <div
                className={`${
                    ((isValidated &&
                    value!=="null"&&
                    !invertedValue)||
                    value === "[]")&&
                    "border rounded border-danger form-select-styles"
                }`}
            >
                <FormSelect
                    labelKey="label"
                    clearButton
                    multiple
                    id="typeahead-transaction"
                    options={TransactionTypesOptions&&TransactionTypesOptions.length!==0?[{
                        label:"ALL Transaction types",
                        value:null,
                        name: "All",
                    },...TransactionTypesOptions]:[]}
                    placeholder="Select a transaction type..."
                    selected={transactionType}
                    onChange={onSelectTransactionType}
                />
            </div>
            {/*{isValidated && required && !invertedValue && (
                <small className="text-danger">
                    * Please select a transaction type.
                </small>
            )}*/}
        </Form.Group>
    );
};

export default TransactionTypesWidget;
