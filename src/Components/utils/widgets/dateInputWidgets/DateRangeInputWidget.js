import React, { useCallback, useMemo } from "react";
import { Form, FormDate } from "@shoutout-labs/shoutout-themes-enterprise";
import moment from "moment/moment";

const DateRangeInputWidget = (props) => {
    const { value, onChange, options, required } = props;
    const {
        property,
        isValidated,
        limitedDateValue = "",
        isFromDate = false,
        isToDate = false,
        fromDate = "",
        toDate = "",
    } = options;
    const invertedFromDate= fromDate?moment(fromDate).toDate():"";
    const invertedToDate= toDate?moment(toDate).toDate():"";

    const onChangeDate = useCallback(
        (date) => onChange(date.toLocaleDateString("sv-SE")),
        [onChange]
    );

    const dateComponent = useMemo(() => {
        if (limitedDateValue) {
            if (isFromDate) {
                return (
                    <>
                        {invertedToDate ? (
                            <FormDate
                                id="from-date-id"
                                onChange={onChangeDate}
                                date={invertedFromDate}
                                maxDate={invertedToDate ||limitedDateValue}
                                selectText="Select from date"
                            />
                        ) : (
                            <FormDate
                                id="from-date-id"
                                onChange={onChangeDate}
                                date={invertedFromDate}
                                maxDate={limitedDateValue}
                                selectText="Select from date"
                            />
                        )}
                    </>
                );
            } else if (isToDate) {
                return (
                    <>
                        {invertedFromDate ? (
                            <FormDate
                                id="to-date-id"
                                onChange={onChangeDate}
                                date={invertedToDate}
                                minDate={invertedFromDate}
                                maxDate={limitedDateValue}
                                selectText="Select to date"
                            />
                        ) : (
                            <FormDate
                                id="to-date-id"
                                onChange={onChangeDate}
                                date={invertedToDate}
                                maxDate={limitedDateValue}
                                selectText="Select to date"
                            />
                        )}
                    </>
                );
            } else {
                return null;
            }
        } else {
            if (isFromDate) {
                return (
                    <>
                        {invertedToDate ? (
                            <FormDate
                                id="from-date-id"
                                onChange={onChangeDate}
                                date={invertedFromDate}
                                maxDate={invertedToDate}
                                selectText="Select from date"
                            />
                        ) : (
                            <FormDate
                                id="from-date-id"
                                onChange={onChangeDate}
                                date={invertedFromDate}
                                selectText="Select from date"
                            />
                        )}
                    </>
                );
            } else if (isToDate) {
                return (
                    <>
                        {invertedFromDate ? (
                            <FormDate
                                id="to-date-id"
                                onChange={onChangeDate}
                                date={invertedToDate}
                                minDate={invertedFromDate}
                                selectText="Select to date"
                            />
                        ) : (
                            <FormDate
                                id="to-date-id"
                                onChange={onChangeDate}
                                date={invertedToDate}
                                selectText="Select to date"
                            />
                        )}
                    </>
                );
            } else {
                return null;
            }
        }
    }, [
        limitedDateValue,
        isFromDate,
        isToDate,
        invertedFromDate,
        invertedToDate,
        onChangeDate,
    ]);

    return (
        <>
            <Form.Group>
                <Form.Label className="d-flex align-items-center">
                    {property === "fromDate" ? "From" : "To"} Date
                    <div className="ml-1 text-danger">*</div>
                </Form.Label>
                <div
                    className={`${
                        isValidated &&
                        required &&
                        !value &&
                        "border border-danger rounded"
                    }`}
                >
                    {dateComponent}
                </div>
                {/*{isValidated && (
                    <>
                        {required && !value && (
                            <small className="text-danger">
                                {`* ${
                                    property === "fromDate" ? "From" : "To"
                                } date cannot be empty.`}
                            </small>
                        )}
                    </>
                )}*/}
            </Form.Group>
        </>
    );
};

export default DateRangeInputWidget;
