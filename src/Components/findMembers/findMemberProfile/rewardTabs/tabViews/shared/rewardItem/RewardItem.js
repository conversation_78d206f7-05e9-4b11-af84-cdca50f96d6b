import React, { useCallback, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Image, Row } from "@shoutout-labs/shoutout-themes-enterprise";
import { 
    RewardGenerationJobStatus, 
    RewardGenerationJobStatusColorCode, 
    RedemptionLogStatusColorCode,
    RewardTabValues, 
    RewardValidity,
    RedemptionStatus
} from "Data";
import { toTitleCase } from "Utils";
import RewardValidityDetails from "Components/common/rewardValidityDetails/RewardValidityDetails";

import "./RewardItem.scss";

const badgeInfo = (processingStatus, status) => {
    if (processingStatus === RewardGenerationJobStatus.COMPLETED && status === RedemptionStatus.READY) {
        return {
            badgeText: "Available at store",
            badgeStyle: RewardGenerationJobStatusColorCode[processingStatus],
        }
    }
    else if (processingStatus === RewardGenerationJobStatus.COMPLETED && status === RedemptionStatus.CLAIMED) {      
        return {
            badgeText: toTitleCase(status),
            badgeStyle: RedemptionLogStatusColorCode[status],
        }
    }
    else {
        return {
            badgeText: toTitleCase(processingStatus),
            badgeStyle: RewardGenerationJobStatusColorCode[processingStatus],
        }
    }
};

const RewardItem = ({ 
    tab, 
    data, 
    rewardValidityState,
    onClick,
}) => {

    const mappedRewardItemData = useMemo(() => {
        const rewardData = tab !== RewardTabValues.REDEEMED ? data : data?.reward;
        const isOutOfStock = data?.remainingCount === 0;
        let isNotValidDate = null;
        let dateTextColor = "";

        if (tab === RewardTabValues.UNLOCKED) {
            const isExpired = RewardValidity.EXPIRED === rewardValidityState;
            const isScheduled = RewardValidity.SCHEDULED === rewardValidityState;
            isNotValidDate = ~ [RewardValidity.EXPIRED, RewardValidity.SCHEDULED].indexOf(rewardValidityState);

            if (isExpired) {
                dateTextColor = "text-danger";
            } 
            else if (isScheduled) {
                dateTextColor = "text-orange";
            }
        }

        return {
            ...data,
            name: rewardData?.name || "~ unknown",
            imageUrl: Array.isArray(rewardData?.imageUrls) && rewardData?.imageUrls.length > 0 && rewardData?.imageUrls[0] !== "" && rewardData?.imageUrls[0],
            buttonText: tab === RewardTabValues.REDEEMED ? "Claim" : "Redeem",
            ...(tab === RewardTabValues.REDEEMED ? 
                {
                    badgeText: badgeInfo(data?.processingStatus, data?.status).badgeText,
                    badgeStyle: badgeInfo(data?.processingStatus, data?.status).badgeStyle,
                    disableBtn: data?.processingStatus !== RewardGenerationJobStatus.DISPATCHED && data?.status !== RedemptionStatus.READY
                        // data?.processingStatus !== RewardGenerationJobStatus.COMPLETED && 
                        // data?.status === RedemptionStatus.READY,
                } 
                : 
                { 
                    isStockAvailable: data?.remainingCount > 0,
                    invalidDateTextColor: dateTextColor,
                    invalidStocksLabelColor: isOutOfStock && "text-danger",                    
                    stockTextColor: data?.remainingCount === 0 ? "text-danger" : "text-muted",
                    disableBtn: isOutOfStock || isNotValidDate,
                } 
            ),
            rewardValidityState
        };
    }, [tab, data, rewardValidityState,]);

    const onButtonClick = useCallback(e => {
        e.stopPropagation();
        e.preventDefault();
        onClick(mappedRewardItemData);
    }, [mappedRewardItemData, onClick]);

    return (
        <Card className="reward-item-view rounded-corners">
            <Card.Body>
                <Row className="d-flex align-items-center">
                    <Col className="d-flex flex-column align-items-center">
                        <div className="border rounded-corners p-3 reward-image">
                            {mappedRewardItemData.imageUrl ? 
                                <Image 
                                    className="reward-item-image"
                                    src={mappedRewardItemData?.imageUrl} 
                                    alt={mappedRewardItemData?.name}
                                /> : 
                                <div className="no-image">
                                    No image found
                                </div>
                            }
                        </div>
                        <div className="my-2 font-weight-bold medium-font">{mappedRewardItemData?.name}</div>
                    </Col>
                    <Col className="d-flex flex-column align-items-center">
                        {tab !== RewardTabValues.REDEEMED ?
                            <>
                                {tab !== RewardTabValues.LOCKED && 
                                    <>
                                        <span className={`font-weight-bold mt-2 ${mappedRewardItemData?.invalidDateTextColor}`}>Validity</span>
                                        <RewardValidityDetails
                                            rewardValidityState={rewardValidityState}
                                            reward={mappedRewardItemData}
                                            removeLabel={true}
                                            additionalClasses="reward-item-details-font"
                                            additionalValidityTextColor={`${mappedRewardItemData?.invalidDateTextColor}`}                                    
                                        />
                                        <div className="my-2 text-center">
                                            <span className={`font-weight-bold ${mappedRewardItemData?.invalidStocksLabelColor}`}>Stocks</span>
                                            <div className={`reward-item-details-font ${mappedRewardItemData?.stockTextColor}`}>{mappedRewardItemData?.remainingCount || 0}</div>
                                        </div>
                                    </>
                                }
                                <div className="font-weight-bold text-center mt-2">
                                    <span className="font-weight-bold">Points</span>
                                    <div className="larger-font">{mappedRewardItemData?.pointsStatic}</div>
                                </div>
                            </>
                            : 
                            <div>
                                <Badge
                                    className="smaller-font py-2 px-3 rounded-corners"
                                    // style={RewardGenerationJobStatusColorCode[mappedRewardItemData?.processingStatus]}
                                    style={mappedRewardItemData?.badgeStyle}
                                >
                                    {mappedRewardItemData?.badgeText}
                                </Badge>
                            </div>
                        }
                    </Col>
                    {tab !== RewardTabValues.LOCKED &&
                        <Col className="text-center">
                            <Button
                                className="rounded-corners"
                                size="lg"
                                variant="secondary"
                                data-id={mappedRewardItemData?._id}
                                disabled={mappedRewardItemData?.disableBtn}
                                onClick={onButtonClick}
                            >
                                {mappedRewardItemData.buttonText} Reward
                            </Button>
                        </Col>
                    }
                </Row>
            </Card.Body>
        </Card>
    );
};

export default RewardItem;