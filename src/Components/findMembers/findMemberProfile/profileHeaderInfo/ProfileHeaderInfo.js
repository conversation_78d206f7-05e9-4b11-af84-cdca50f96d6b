import React, { use<PERSON><PERSON>back, useContext, useState } from "react";
import {
    Avatar,
    Badge,
    Button,
    Card,
    Col,
    IcIcon,
    Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faAngleRightB } from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    MemberStatus,
    MemberTypes,
} from "Data";
import { formatToCommonFormat, toTitleCase } from "Utils";
import { getContactByIdKlip } from "Services";
import { LoadingComponent } from "Components/utils";
import AddPoints from "../addPoints/AddPoints";
import AdjustPoints from "../adjustPoints/AdjustPoints";
import RedeemPoints from "../redeemPoints/RedeemPoints";
import RedeemPointsWithoutOTP from "../redeemPointsWithoutOTP/RedeemPointsWithoutOTP";

const PointsActions = {
    ADD_POINTS: "ADD_POINTS",
    REDEEM_POINTS: "REDEEM_POINTS",
    REDEEM_POINTS_WITHOUT_OTP: "REDEEM_POINTS_WITHOUT_OTP",
    ADJUST_POINTS: "ADJUST_POINTS",
};

const getKlipPermission = (actionName = "", isAuthorizedForAction = () => {}) =>
    !isAuthorizedForAction(
        AccessPermissionModuleNames.KLIP,
        AccessPermissionModules[AccessPermissionModuleNames.KLIP].actions[
            actionName
        ]
    );

const getPointActionBtnData = ({
    variant = "secondary",
    name = "",
    isAuthorized = false,
}) => {
    return {
        variant,
        btnName: `${!isAuthorized ? "Not Authorized to " : ""}${name}`,
        isAuthorized,
    };
};

const PointsActionsButtons = ({
    isLoadingMember = false,
    isReloadingMember = false,
    isLoadingRewards = false,
    memberDetails = null,
    isAuthorizedForAction = () => {},
    onShowPointsActionModal = () => {},
}) =>
    Object.keys(PointsActions).map((action) => {
        let pointsActionData = {
            btnName: "~ unknown",
            variant: "secondary",
            isAuthorized: false,
        };
        switch (action) {
            case PointsActions.ADD_POINTS: {
                const addPointsManuallyNotAllowed = getKlipPermission(
                    "CollectPointsAmount",
                    isAuthorizedForAction
                );
                const addBillAmountPointsNotAllowed = getKlipPermission(
                    "CollectPointsBill",
                    isAuthorizedForAction
                );

                pointsActionData = getPointActionBtnData({
                    name: "Add Points",
                    isAuthorized: !(
                        addPointsManuallyNotAllowed &&
                        addBillAmountPointsNotAllowed
                    ),
                    ...(addPointsManuallyNotAllowed &&
                    addBillAmountPointsNotAllowed
                        ? { variant: "outline-dark" }
                        : {}),
                });
                break;
            }
            case PointsActions.REDEEM_POINTS: {
                const redeemPointsWithOtpNotAllowed = getKlipPermission(
                    "RedeemPoints",
                    isAuthorizedForAction
                );

                pointsActionData = getPointActionBtnData({
                    name: "Redeem Points with OTP",
                    isAuthorized: !redeemPointsWithOtpNotAllowed,
                    ...(redeemPointsWithOtpNotAllowed
                        ? { variant: "outline-dark" }
                        : {}),
                });
                break;
            }
            case PointsActions.REDEEM_POINTS_WITHOUT_OTP: {
                const redeemPointsWithoutOtpNotAllowed = getKlipPermission(
                    "RedeemPointsWithoutOtp",
                    isAuthorizedForAction
                );

                pointsActionData = getPointActionBtnData({
                    name: "Redeem Points without OTP",
                    isAuthorized: !redeemPointsWithoutOtpNotAllowed,
                    ...(redeemPointsWithoutOtpNotAllowed
                        ? { variant: "outline-dark" }
                        : {}),
                });
                break;
            }
            case PointsActions.ADJUST_POINTS: {
                const adjustPointsNotAllowed = getKlipPermission(
                    "AdjustPoints",
                    isAuthorizedForAction
                );

                pointsActionData = getPointActionBtnData({
                    name: "Adjust Points",
                    isAuthorized: !adjustPointsNotAllowed,
                    ...(adjustPointsNotAllowed
                        ? { variant: "outline-dark" }
                        : {}),
                });
                break;
            }
            default:
                break;
        }

        return (
            <Button
                key={action}
                id={action}
                className="my-2 find-members-default-font rounded-corners w-100"
                size="lg"
                variant={pointsActionData.variant}
                disabled={
                    isLoadingMember ||
                    isReloadingMember ||
                    isLoadingRewards ||
                    !memberDetails ||
                    !pointsActionData.isAuthorized
                }
                onClick={
                    !pointsActionData.isAuthorized
                        ? () => {}
                        : onShowPointsActionModal
                }
            >
                {pointsActionData.btnName}
            </Button>
        );
    });

const badgeVariant = ({ value1, value2, variant1, variant2 }) =>
    value1 === value2 ? variant1 : variant2;

const ProfileHeaderInfo = ({
    isLoadingMember,
    isReloadingMember,
    isLoadingRewards,
    memberDetails,
    onViewFullMemberProfile,
    setIsReloadingMember,
    setSelectedMember,
}) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [pointsAction, setPointsAction] = useState("");
    const [showAddPointsModal, setShowAddPointsModal] = useState(false);
    const [showRedeemPointsModal, setShowRedeemPointsModal] = useState(false);
    const [
        showRedeemPointsWithoutOTPModal,
        setShowRedeemPointsWithoutOTPModal,
    ] = useState(false);
    const [showAdjustPointsModal, setShowAdjustPointsModal] = useState(false);

    const onShowPointsActionModal = useCallback(
        (e) => {
            setPointsAction(e.currentTarget.id);
            switch (e.currentTarget.id) {
                case PointsActions.ADD_POINTS: {
                    setShowAddPointsModal(true);
                    break;
                }
                case PointsActions.REDEEM_POINTS: {
                    setShowRedeemPointsModal(true);
                    break;
                }
                case PointsActions.ADJUST_POINTS: {
                    setShowAdjustPointsModal(true);
                    break;
                }
                case PointsActions.REDEEM_POINTS_WITHOUT_OTP: {
                    setShowRedeemPointsWithoutOTPModal(true);
                    break;
                }
                default: {
                    break;
                }
            }
        },
        [setPointsAction, setShowAddPointsModal, setShowRedeemPointsModal]
    );

    const onClosePointsActionModal = useCallback(
        async (e, resData) => {
            switch (pointsAction) {
                case PointsActions.ADD_POINTS: {
                    setShowAddPointsModal(false);
                    break;
                }
                case PointsActions.REDEEM_POINTS: {
                    setShowRedeemPointsModal(false);
                    break;
                }
                case PointsActions.ADJUST_POINTS: {
                    setShowAdjustPointsModal(false);
                    break;
                }
                case PointsActions.REDEEM_POINTS_WITHOUT_OTP: {
                    setShowRedeemPointsWithoutOTPModal(false);
                    break;
                }
                default: {
                    break;
                }
            }
            setPointsAction("");
            if (resData) {
                setIsReloadingMember(true);
                const updatedMemberResponse = await getContactByIdKlip(
                    memberDetails?._id
                );
                setSelectedMember(updatedMemberResponse);
                setIsReloadingMember(false);
            }
        },
        [
            pointsAction,
            memberDetails?._id,
            setIsReloadingMember,
            setSelectedMember,
            setShowAddPointsModal,
            setShowRedeemPointsModal,
            setPointsAction,
        ]
    );

    return (
        <Card className="rounded-corners">
            {isLoadingMember ? (
                <LoadingComponent />
            ) : (
                <Card.Body>
                    <Row>
                        <Col
                            className="my-3 border-right"
                            xs={12}
                            sm={12}
                            md={5}
                            lg={5}
                            xl={5}
                        >
                            <div className="d-flex justify-content-start align-items-center">
                                <Avatar
                                    name={
                                        !memberDetails?.profilePicture &&
                                        (memberDetails?.firstName || "U")
                                    }
                                    size="10rem"
                                    src={memberDetails?.profilePicture}
                                    alt="Profile Picture"
                                />
                                <div className="ml-3">
                                    <div className="font-weight-bold larger-font">
                                        {`${memberDetails?.firstName || ""} ${
                                            memberDetails?.lastName || ""
                                        }`}
                                    </div>
                                    <div className="text-muted smaller-font">
                                        {memberDetails?.cardNumber}
                                    </div>
                                    <div>
                                        <Badge
                                            className="px-3 mr-3"
                                            variant={badgeVariant({
                                                value1: memberDetails?.type,
                                                value2: MemberTypes.PRIMARY,
                                                variant1: "secondary",
                                                variant2: "warning",
                                            })}
                                        >
                                            {toTitleCase(
                                                memberDetails?.type || "unknown"
                                            )}
                                        </Badge>
                                        <Badge
                                            className="px-3"
                                            variant={badgeVariant({
                                                value1: memberDetails?.status,
                                                value2: MemberStatus.ACTIVE,
                                                variant1: "success",
                                                variant2: "danger",
                                            })}
                                        >
                                            {toTitleCase(
                                                memberDetails?.status ||
                                                    "unknown"
                                            )}
                                        </Badge>
                                    </div>
                                    {isAuthorizedForAction(
                                        AccessPermissionModuleNames.MEMBER,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.MEMBER
                                        ].actions.GetMember
                                    ) && (
                                        <Button
                                            className="m-0 p-0 btn shadow-none"
                                            variant="link"
                                            size="lg"
                                            disabled={
                                                isLoadingMember ||
                                                isReloadingMember ||
                                                isLoadingRewards
                                            }
                                            onClick={onViewFullMemberProfile}
                                        >
                                            <div className="d-flex align-items-center">
                                                View Full Profile
                                                <IcIcon
                                                    className="ml-2"
                                                    icon={faAngleRightB}
                                                />
                                            </div>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </Col>
                        <Col
                            className="my-3 d-flex align-items-center border-right"
                            xs={12}
                            sm={12}
                            md={4}
                            lg={4}
                            xl={4}
                        >
                            <div className="mx-3">
                                Redeemable Points
                                {isReloadingMember && (
                                    <div className="smaller-font">
                                        Refreshing points...
                                    </div>
                                )}
                                {!isReloadingMember && (
                                    <div className="font-weight-bold larger-font">
                                        {memberDetails?.points || 0}
                                    </div>
                                )}
                                {memberDetails?.pointsToExpire &&
                                    Array.isArray(
                                        memberDetails?.pointsToExpire
                                    ) && (
                                        <div className="text-danger smaller-font">
                                            <span className="font-weight-bold">
                                                {memberDetails
                                                    ?.pointsToExpire[0]
                                                    ?.pointsToExpire?.toFixed(2) || "0"}
                                            </span>
                                            {` points will expire on ${
                                                memberDetails?.pointsToExpire[0]
                                                    ?.pointsExpireOn
                                                    ? formatToCommonFormat(
                                                        memberDetails
                                                            .pointsToExpire[0]
                                                            .pointsExpireOn
                                                    )
                                                    : "~ unknown"
                                            }`}
                                        </div>
                                    )}
                            </div>
                        </Col>
                        <Col
                            className="my-3 d-flex align-items-center"
                            xs={12}
                            sm={12}
                            md={3}
                            lg={3}
                            xl={3}
                        >
                            {memberDetails?.cardNumber &&
                            memberDetails?.status === MemberStatus.ACTIVE ? (
                                <div className="text-center">
                                    <PointsActionsButtons
                                        isLoadingMember={isLoadingMember}
                                        isReloadingMember={isReloadingMember}
                                        isLoadingRewards={isLoadingRewards}
                                        memberDetails={memberDetails}
                                        isAuthorizedForAction={
                                            isAuthorizedForAction
                                        }
                                        onShowPointsActionModal={
                                            onShowPointsActionModal
                                        }
                                    />
                                </div>
                            ) : (
                                <div className="text-danger">
                                    User Needs to be Active and have an Active
                                    Card to Do the Transactions
                                </div>
                            )}
                        </Col>
                    </Row>
                    {showAddPointsModal && (
                        <AddPoints
                            show={showAddPointsModal}
                            onHide={onClosePointsActionModal}
                        />
                    )}
                    {showRedeemPointsModal && (
                        <RedeemPoints
                            show={showRedeemPointsModal}
                            onHide={onClosePointsActionModal}
                        />
                    )}
                    {showRedeemPointsWithoutOTPModal && (
                        <RedeemPointsWithoutOTP
                            show={showRedeemPointsWithoutOTPModal}
                            onHide={onClosePointsActionModal}
                        />
                    )}
                    {showAdjustPointsModal && (
                        <AdjustPoints
                            show={showAdjustPointsModal}
                            onHide={onClosePointsActionModal}
                        />
                    )}
                </Card.Body>
            )}
        </Card>
    );
};

export default ProfileHeaderInfo;
