import React, { useCallback, useContext, useEffect } from "react";
import { useHistory, useParams } from "react-router-dom";
import {
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { RewardTabValues } from "Data";
import { FindMembersContext } from "../context/FindMembersContext";
import BaseLayout from "Layout/BaseLayout";
import ProfileHeaderInfo from "./profileHeaderInfo/ProfileHeaderInfo";
import OtherInfoCards from "./otherInfoCards/OtherInfoCards";
import RewardTabs from "./rewardTabs/RewardTabs";

import "./FindMemberProfile.scss";

const FindMemberProfile = () => {
    const {
        limit,
        skip,
        searchedValue,
        isLoadingMember,
        isReloadingMember,
        loadSelectedMemberProfile,
        selectedMember,
        setSelectedMember,
        setStatus,
        showAddPoint,
        showRedeemPoints,
        isLoadingRewards,
        setTab,
        onFindMembers,
        setIsReloadingMember,
        searchField,
    } = useContext(FindMembersContext);
    const history = useHistory();
    const { id: memberId } = useParams();

    const onNavigatingBack = useCallback(async () => {
        history.push("/find-members");
        await onFindMembers({ limit, skip }, searchedValue, searchField);
    }, [history, limit, onFindMembers, searchedValue, skip, searchField]);

    const onViewFullMemberProfile = useCallback(
        () => history.push(`/members/${memberId}`),
        [memberId, history]
    );

    useEffect(() => {
        if (memberId) {
            loadSelectedMemberProfile(memberId);
        }
        // * To reset the member when unmounting the component.
        return () => {
            setSelectedMember();
            setTab(RewardTabValues.UNLOCKED);
        };
    }, [loadSelectedMemberProfile, memberId, setSelectedMember, setTab]);

    return (
        <BaseLayout
            containerClassName="find-member-profile-view"
            topLeft={<></>}
            topRight={
                <Button
                    className="px-5 find-members-default-font rounded-corners"
                    size="lg"
                    variant="dark"
                    disabled={
                        isLoadingMember || isReloadingMember || isLoadingRewards
                    }
                    onClick={onNavigatingBack}
                >
                    Back
                </Button>
            }
            bottom={
                <div className="mt-3">
                    <ProfileHeaderInfo
                        isLoadingMember={isLoadingMember}
                        isReloadingMember={isReloadingMember}
                        isLoadingRewards={isLoadingRewards}
                        memberDetails={selectedMember}
                        setStatus={setStatus}
                        showAddPoint={showAddPoint}
                        showRedeemPoints={showRedeemPoints}
                        onViewFullMemberProfile={onViewFullMemberProfile}
                        setIsReloadingMember={setIsReloadingMember}
                        setSelectedMember={setSelectedMember}
                    />

                    <div className="mt-4">
                        <Row>
                            <Col
                                className="pr-0"
                                xs={12}
                                sm={12}
                                md={4}
                                lg={4}
                                xl={4}
                            >
                                <OtherInfoCards
                                    isLoadingMember={isLoadingMember}
                                    title="Tier"
                                    badgeValue={selectedMember?.tierData?.name}
                                    badgeStatus="warning"
                                    benefits={
                                        selectedMember?.tierData?.benefits
                                    }
                                />
                                <OtherInfoCards
                                    isLoadingMember={isLoadingMember}
                                    title="Affinity Group"
                                    badgeValue={
                                        selectedMember?.affinityGroup?.details
                                            ?.name
                                    }
                                    badgeStatus={
                                        selectedMember?.affinityGroup?.details
                                            ? "info"
                                            : "default"
                                    }
                                    benefits={
                                        selectedMember?.affinityGroup?.details
                                            ?.benefits
                                    }
                                />
                            </Col>
                            <Col xs={12} sm={12} md={8} lg={8} xl={8}>
                                <Card className="rounded-corners">
                                    <Card.Body>
                                        <RewardTabs />
                                    </Card.Body>
                                </Card>
                            </Col>
                        </Row>
                    </div>
                </div>
            }
        />
    );
};

export default FindMemberProfile;
