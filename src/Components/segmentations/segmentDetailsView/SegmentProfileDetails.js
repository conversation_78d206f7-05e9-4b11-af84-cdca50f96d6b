import React, { useContext } from "react";
import { Button, Col, Row } from "@shoutout-labs/shoutout-themes-enterprise";
import { formatToCommonReadableFormat } from "../../../Utils";
import { LoadingComponent } from "../../utils";
import { MembersContext } from "../../../Contexts";

const SegmentProfileDetails=({isLoadingSegment, segmentData, onClickSegmentName})=>{
    const {
        totalCount,
        isLoading
    } = useContext(MembersContext);
    return(
        <div className="segment-profile-details mt-4">
            {isLoadingSegment?<LoadingComponent/>:<Row>
                <Col md={4}>
                    <Row>
                        <Col md={9}>
                            <h4 className="mb-0">{segmentData?.name|| "-"}</h4>
                            <p className="text-muted mt-1">{formatToCommonReadableFormat(segmentData?.updatedOn) || "-"}</p>
                        </Col>
                        <Col>
                            <Button 
                                variant="link" 
                                className="text-muted"
                                disabled={isLoading}
                                onClick={onClickSegmentName}
                            >
                                Edit
                            </Button>
                        </Col>
                    </Row>
                </Col>
                <Col md={2}>
                    <div >
                        <p className="text-muted mb-0">Member Count</p>
                        <p className="font-weight-bold">{segmentData?.memberCount||totalCount||"-"}</p>
                    </div>
                </Col>
                <Col md={2}>
                    <p className="text-muted mb-0">Average Spend</p>
                    <p className="font-weight-bold">{segmentData?.averageSpend || "-"}</p>
                </Col>
                <Col md={2}>
                    <p className="text-muted mb-0">SMS Reachable</p>
                    <p className="font-weight-bold">{segmentData?.smsReachable || "-"}</p>
                </Col>
                <Col md={2}>
                    <p className="text-muted mb-0">Email Reachable</p>
                    <p className="font-weight-bold">{segmentData?.emailReachable || "-"}</p>
                </Col>
            </Row>}
        </div>
    )
}
export default SegmentProfileDetails;
