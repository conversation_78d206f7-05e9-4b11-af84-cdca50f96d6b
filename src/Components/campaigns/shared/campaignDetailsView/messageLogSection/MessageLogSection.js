import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import {
    Button,
    IcIcon,
    SubHeading,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faCalendar,
    faCreditCard,
    faEnvelope,
    faFileExport,
    faFilter,
    faFilterSlash,
    faPhone,
    faPower,
    faSync,
} from "FaICIconMap";
import { CampaignChannels, NotificationFilterOptions } from "Data";
import { UserContext } from "Contexts";
import { useToggle } from "Hooks";
import { getMessageLogs, getMessageLogsCount } from "Services";
import {
    formatToCommonFormat,
    formatToCommonReadableFormat,
    isEmptyObject,
    toTitleCase,
} from "Utils";
import BaseLayout from "Layout/BaseLayout";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import NameIconTemplate from "Components/utils/table/NameIconTemplate";
import NotificationFilters from "Components/notification/shared/notificationsFilters/NotificationFilters";
import TableView from "../../TableView";
import ExportLogs from "./exportLogs/ExportLogs";

const defaultSkip = 1,
    defaultLimit = 25;

const columns = (campaignChannel = "") => [
    {
        dataField: "_id",
        text: NameIconTemplate({ name: "Log Id", icon: faCreditCard }),
    },
    {
        dataField: "to",
        text: NameIconTemplate({
            name:
                campaignChannel === CampaignChannels.EMAIL
                    ? "Email"
                    : "Mobile Number",
            icon: campaignChannel === CampaignChannels.EMAIL ? faEnvelope : faPhone,
        }),
        headerStyle: { width: "30%" },
    },
    {
        dataField: "status",
        text: NameIconTemplate({ name: "Status", icon: faPower }),
    },
    {
        dataField: "sentDate",
        text: NameIconTemplate({ name: "Sent Date", icon: faCalendar }),
    },
];

const MessageLogSection = ({
    campaignChannel,
    categoryId,
    isLoadingMainDetailsPage,
    setIsLoadingMainDetailsPage,
}) => {
    const { regionId } = useContext(UserContext);
    const [skip, setSkip] = useState(defaultSkip);
    const [limit, setLimit] = useState(defaultLimit);
    const [isLoadingMessageLog, setIsLoadingMessageLog] = useState(false);
    const [isReloading, setIsReloading] = useState(false);
    const [data, setData] = useState([]);
    const [dataCount, setDataCount] = useState(0);
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [selectedOption, setSelectedOption] = useState([]);
    const [fromDate, setFromDate] = useState("");
    const [toDate, setToDate] = useState("");
    const [mobileNumber, setMobileNumber] = useState("");
    const [email, setEmail] = useState("");
    const [messageLogStatus, setMessageLogStatus] = useState([]);
    const [otherFilter, setOtherFilter] = useState({});
    const [appliedFilters, setAppliedFilters] = useState();
    const [isApplied, setIsApplied] = useState(false);
    const [otherFilterKeyLabelPair, setOtherFilterKeyLabelPair] = useState({});
    const [isSelectDisabled, setIsSelectDisabled] = useState(false);
    const [showExportLogsModal, toggleShowExportLogsModal] = useToggle(false);

    const campaignTypeToReadable = useMemo(
        () =>
            campaignChannel === CampaignChannels.EMAIL
                ? toTitleCase(campaignChannel)
                : campaignChannel,
        [campaignChannel]
    );

    const tableData = useMemo(
        () =>
            data.map((log) => ({
                _id: log?._id || "~ unknown",
                to: log?.to || "~ unknown",
                status: applyBadgeStyling({
                    text: log?.status,
                    variant: log?.status,
                }),
                sentDate: log?.createdOn
                    ? formatToCommonReadableFormat(log.createdOn)
                    : "~ unknown",
            })),
        [data]
    );

    const selectOptions = useMemo(() => {
        const dynamicFilterOption =
            campaignChannel === CampaignChannels.EMAIL
                ? [
                    {
                        label: "To Email",
                        value: NotificationFilterOptions.TO_EMAIL,
                    },
                ]
                : [
                    {
                        label: "To Mobile Number",
                        value: NotificationFilterOptions.TO_SMS,
                    },
                ];
        return [
            { label: "Status", value: NotificationFilterOptions.STATUS },
            ...dynamicFilterOption,
        ];
    }, [campaignChannel]);

    const loadMessageLogs = useCallback(
        async ({ limit, skip }, filters, reloadCount = true) => {
            try {
                setIsLoadingMainDetailsPage(true);
                setIsLoadingMessageLog(true);

                const commonFilters = {
                    regionId,
                    transport: campaignChannel,
                    categoryId,
                };
                let messageLogsData = {};
                let payload = {
                    limit,
                    skip: (skip - 1) * limit,
                    ...commonFilters,
                };
                let countPayload = commonFilters;

                if (filters) {
                    payload = { ...payload, ...filters };
                    countPayload = { ...countPayload, ...filters };
                }

                if (reloadCount) {
                    const [messageLogResponse, messageLogCountResponse] =
                        await Promise.all([
                            getMessageLogs(payload),
                            getMessageLogsCount(countPayload),
                        ]);
                    setDataCount(messageLogCountResponse?.total || 0);
                    messageLogsData = messageLogResponse;
                } else {
                    messageLogsData = await getMessageLogs(payload);
                }

                setData(messageLogsData?.items || []);
            } catch (error) {
                console.error(error);
                setData([]);
                setDataCount(0);
                toast.error(
                    <div>
                        Failed to load campaign {campaignTypeToReadable} logs!
                        <br />
                        {error.message
                            ? `Error: ${error.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setIsLoadingMainDetailsPage(false);
                setIsLoadingMessageLog(false);
            }
        },
        [
            categoryId,
            campaignChannel,
            setIsLoadingMainDetailsPage,
            regionId,
            campaignTypeToReadable,
            setIsLoadingMessageLog,
            setData,
            setDataCount,
        ]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadMessageLogs({ limit, skip: newSkip }, appliedFilters, false);
        },
        [limit, appliedFilters, loadMessageLogs, setSkip]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadMessageLogs(
                { limit: newLimit, skip: defaultSkip },
                appliedFilters,
                false
            );
        },
        [appliedFilters, loadMessageLogs, setLimit, setSkip]
    );

    const onSelectOption = useCallback(
        (e) => setSelectedOption(e),
        [setSelectedOption]
    );

    const onSetFilter = useCallback(
        (e, filterType) => {
            if (appliedFilters) {
                switch (filterType) {
                    case "transport":
                        setIsApplied(e[0]?.value === appliedFilters?.transport);
                        break;
                    case "status":
                        setIsApplied(e[0]?.value === appliedFilters?.status);
                        break;
                    case "toPhoneNumber":
                        setIsApplied(e === appliedFilters?.toPhoneNumber);
                        break;
                    case "toEmail":
                        setIsApplied(e === appliedFilters?.toEmail);
                        break;
                    case "fromDate":
                        setIsApplied(
                            formatToCommonFormat(e) === appliedFilters.fromDate
                        );
                        break;
                    case "toDate":
                        setIsApplied(
                            formatToCommonFormat(e) === appliedFilters.toDate
                        );
                        break;
                    default:
                        break;
                }
            }
        },
        [appliedFilters]
    );

    const onSelectStatus = useCallback(
        (e) => {
            setMessageLogStatus(e);
            setOtherFilterKeyLabelPair({ key: "status", label: "Status" });
            onSetFilter(e, "status");
        },
        [setMessageLogStatus, setOtherFilterKeyLabelPair, onSetFilter]
    );

    const onMobileNumberChange = useCallback(
        (status, value, countryData, number, formattedNumber) => {
            setMobileNumber(formattedNumber);
            setOtherFilterKeyLabelPair({
                key: "toPhoneNumber",
                label: "To Mobile Number",
            });
            onSetFilter(formattedNumber, "toPhoneNumber");
        },
        [setMobileNumber, setOtherFilterKeyLabelPair, onSetFilter]
    );

    const onChangeEmail = useCallback(
        (e) => {
            e.stopPropagation();
            setEmail(e.target.value);
            setOtherFilterKeyLabelPair({ key: "toEmail", label: "To Email" });
            onSetFilter(e.target.value, "toEmail");
        },
        [setEmail, setOtherFilterKeyLabelPair, onSetFilter]
    );

    const onFilterBy = useCallback(async () => {
        toggleShowFilters();
        if (!isApplied && !appliedFilters) {
            setOtherFilter({});
            setOtherFilterKeyLabelPair({});
            setSelectedOption([]);
            setEmail("");
            setMobileNumber("");

            setMessageLogStatus([]);
            setFromDate("");
            setToDate("");
        }
    }, [
        toggleShowFilters,
        isApplied,
        appliedFilters,
        setOtherFilter,
        setOtherFilterKeyLabelPair,
        setSelectedOption,
        setEmail,
        setMobileNumber,

        setMessageLogStatus,
        setFromDate,
        setToDate,
    ]);

    const onChangeFromDate = useCallback(
        (date) => {
            setFromDate(date);
            onSetFilter(date, "fromDate");
        },
        [setFromDate, onSetFilter]
    );

    const onChangeToDate = useCallback(
        (date) => {
            setToDate(date);
            onSetFilter(date, "toDate");
        },
        [setToDate, onSetFilter]
    );

    const getOtherFilterValue = useCallback(() => {
        let filterValue = {};

        if (selectedOption[0]?.value) {
            switch (selectedOption[0].value) {
                case NotificationFilterOptions.STATUS:
                    filterValue = {
                        status: messageLogStatus[0]?.value || "",
                    };
                    break;
                case NotificationFilterOptions.TO_SMS:
                    filterValue = { toPhoneNumber: mobileNumber };
                    break;
                case NotificationFilterOptions.TO_EMAIL:
                    if (!/\S+@\S+\.\S+/.test(email)) {
                        throw new Error("Entered email is invalid!");
                    }
                    filterValue = { toEmail: email };
                    break;
                default:
                    setOtherFilterKeyLabelPair({});
                    break;
            }
        }

        return filterValue;
    }, [
        email,
        messageLogStatus,
        mobileNumber,
        selectedOption,
        setOtherFilterKeyLabelPair,
    ]);

    const applyFilter = useCallback(() => {
        try {
            const otherFilter = getOtherFilterValue();

            setOtherFilter(otherFilter);
            setAppliedFilters(otherFilter);

            if (fromDate !== "" || toDate !== "") {
                if (toDate === "") {
                    setAppliedFilters({
                        ...otherFilter,
                        fromDate: formatToCommonFormat(fromDate),
                    });
                } else if (fromDate === "") {
                    setAppliedFilters({
                        ...otherFilter,
                        toDate: formatToCommonFormat(toDate),
                    });
                } else {
                    setAppliedFilters({
                        ...otherFilter,
                        fromDate: formatToCommonFormat(fromDate),
                        toDate: formatToCommonFormat(toDate),
                    });
                }
            }
            setIsApplied(true);
            setIsSelectDisabled(true);
            setSkip(defaultSkip);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to apply filter!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        fromDate,
        toDate,
        getOtherFilterValue,
        setOtherFilter,
        setAppliedFilters,
        setIsApplied,
        setIsSelectDisabled,
        setSkip,
    ]);

    const removeOtherFilter = useCallback(() => {
        setOtherFilter({});
        setOtherFilterKeyLabelPair({});
        setSelectedOption([]);
        setEmail("");
        setMobileNumber("");

        setMessageLogStatus([]);
        setAppliedFilters();

        if (appliedFilters?.fromDate || appliedFilters?.toDate) {
            setAppliedFilters({
                ...(appliedFilters?.fromDate
                    ? {
                        fromDate: formatToCommonFormat(
                            appliedFilters.fromDate
                        ),
                    }
                    : {}),
                ...(appliedFilters?.toDate
                    ? { toDate: formatToCommonFormat(appliedFilters.toDate) }
                    : {}),
            });
        } else {
            setIsApplied(false);
        }
        setIsSelectDisabled(false);
        setSkip(defaultSkip);
    }, [
        appliedFilters,
        setOtherFilter,
        setOtherFilterKeyLabelPair,
        setSelectedOption,
        setEmail,
        setMobileNumber,

        setMessageLogStatus,
        setAppliedFilters,
        setIsApplied,
        setIsSelectDisabled,
        setSkip,
    ]);

    const removeFromDate = useCallback(() => {
        setFromDate("");
        setAppliedFilters();
        if (!isEmptyObject(otherFilter || {}) || appliedFilters?.toDate) {
            setAppliedFilters({
                ...(!isEmptyObject(otherFilter || {}) ? otherFilter : {}),
                ...(appliedFilters?.toDate
                    ? { toDate: formatToCommonFormat(toDate) }
                    : {}),
            });
        } else {
            setIsApplied(false);
        }
        setSkip(defaultSkip);
    }, [
        appliedFilters,
        otherFilter,
        toDate,
        setFromDate,
        setAppliedFilters,
        setIsApplied,
        setSkip,
    ]);

    const removeToDate = useCallback(() => {
        setToDate("");
        setAppliedFilters();
        if (!isEmptyObject(otherFilter || {}) || appliedFilters?.fromDate) {
            setAppliedFilters({
                ...(!isEmptyObject(otherFilter || {}) ? otherFilter : {}),
                ...(appliedFilters?.fromDate
                    ? { fromDate: formatToCommonFormat(fromDate) }
                    : {}),
            });
        } else {
            setIsApplied(false);
        }
        setSkip(defaultSkip);
    }, [
        appliedFilters,
        otherFilter,
        fromDate,
        setToDate,
        setAppliedFilters,
        setIsApplied,
        setSkip,
    ]);

    const resetFilter = useCallback(() => {
        setOtherFilter({});
        setOtherFilterKeyLabelPair({});
        setSelectedOption([]);
        setEmail("");
        setMobileNumber("");
        setMessageLogStatus([]);
        setFromDate("");
        setToDate("");
        setAppliedFilters();
        setIsApplied(false);
        setIsSelectDisabled(false);
        setSkip(defaultSkip);
    }, [
        setOtherFilter,
        setOtherFilterKeyLabelPair,
        setSelectedOption,
        setEmail,
        setMobileNumber,
        setMessageLogStatus,
        setFromDate,
        setToDate,
        setAppliedFilters,
        setIsApplied,
        setIsSelectDisabled,
        setSkip,
    ]);

    const onReloadCampaigns = useCallback(async () => {
        setIsReloading(true);
        await loadMessageLogs({ limit, skip: defaultSkip }, appliedFilters);
        setSkip(defaultSkip);
        setIsReloading(false);
    }, [limit, appliedFilters, setSkip, setIsReloading, loadMessageLogs]);

    useEffect(() => {
        loadMessageLogs({ limit, skip: defaultSkip }, appliedFilters);
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [appliedFilters]);
    return (
        <BaseLayout
            topLeft={<SubHeading text={`${campaignTypeToReadable} Logs`} />}
            bottom={
                <div className="w-100">
                    <div className="d-flex justify-content-between">
                        <div className="d-flex align-items-center">
                            <Button
                                className="btn shadow-none"
                                variant="link"
                                size="sm"
                                disabled={
                                    isLoadingMainDetailsPage ||
                                    isLoadingMessageLog ||
                                    isReloading
                                }
                                onClick={onReloadCampaigns}
                            >
                                {!isReloading && (
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            size="md"
                                            className="mr-2"
                                            icon={faSync}
                                        />
                                        {`Reload ${campaignTypeToReadable} Logs`}
                                    </div>
                                )}
                            </Button>
                            <div style={{ fontSize: "0.9rem" }}>
                                {isReloading && (
                                    <div className="text-primary">
                                        Reloading...
                                    </div>
                                )}
                            </div>
                        </div>
                        <Button
                            variant="outline-primary"
                            size="sm"
                            disabled={
                                isLoadingMainDetailsPage || isLoadingMessageLog
                            }
                            onClick={onFilterBy}
                        >
                            <IcIcon
                                className="mr-2"
                                size="lg"
                                icon={showFilters ? faFilterSlash : faFilter}
                            />
                            {showFilters ? "Hide Filters" : "Filter By"}
                        </Button>
                    </div>
                    {showFilters && <hr />}
                    <NotificationFilters
                        showFilters={showFilters}
                        selectedOption={selectedOption}
                        onSelectOption={onSelectOption}
                        isLoading={
                            isLoadingMainDetailsPage || isLoadingMessageLog
                        }
                        selectOptions={selectOptions}
                        mobileNumber={mobileNumber}
                        email={email}
                        fromDate={fromDate}
                        toDate={toDate}
                        status={messageLogStatus}
                        appliedFilters={appliedFilters}
                        appliedOtherFilter={otherFilterKeyLabelPair}
                        appliedFromDateAttr="fromDate"
                        appliedToDateAttr="toDate"
                        isApplied={isApplied}
                        isSelectDisabled={isSelectDisabled}
                        onChangeEmail={onChangeEmail}
                        setFromDate={onChangeFromDate}
                        setToDate={onChangeToDate}
                        onSelectStatus={onSelectStatus}
                        onMobileNumberChange={onMobileNumberChange}
                        removeOtherFilter={removeOtherFilter}
                        removeFromDate={removeFromDate}
                        removeToDate={removeToDate}
                        applyFilter={applyFilter}
                        clearFilter={resetFilter}
                    />
                    <hr />
                    <div className="text-right">
                        <Button
                            variant="primary"
                            size="sm"
                            onClick={toggleShowExportLogsModal}
                            disabled={
                                isLoadingMainDetailsPage ||
                                isLoadingMessageLog ||
                                dataCount === 0
                            }
                        >
                            <IcIcon
                                className="mr-2"
                                size="lg"
                                icon={faFileExport}
                            />
                            Export {campaignTypeToReadable} Logs
                        </Button>
                    </div>
                    <div>
                        <TableView
                            columns={columns(campaignChannel)}
                            sizePerPage={limit}
                            page={skip}
                            data={tableData}
                            totalCount={dataCount}
                            isLoading={isLoadingMessageLog}
                            onChangePagination={onChangePagination}
                            onChangePageSize={onChangePageSize}
                        />
                    </div>
                    {showExportLogsModal && (
                        <ExportLogs
                            show={showExportLogsModal}
                            onHide={toggleShowExportLogsModal}
                            regionId={regionId}
                            campaignChannel={campaignChannel}
                            categoryId={categoryId}
                            campaignTypeToReadable={campaignTypeToReadable}
                            appliedFilters={appliedFilters}
                        />
                    )}
                </div>
            }
        />
    );
};

export default MessageLogSection;
