.pop-up-window-view {
    .modal-dialog {
        max-width: 80% !important;
    }
    .group--actions--tr, .group--actions--br {
        justify-content: flex-end;
        opacity: 0;
        pointer-events: none;
        height: 0;
    }
    .rule--header {
        margin-left: auto;
        display: flex;
        opacity: 0;
        pointer-events: none;
        height: 0;
    }
    .popup-default-bg {
        background-color: #efefef;
    }

    .segments-and-filters-view {
        max-height: 20rem !important;
        overflow: auto;
    }

    .history-view {
        background-color: #efefef;
        max-height: 30rem !important;
        overflow: scroll;
    }

    .campaign-description-view {
        background-color: #efefef;
        max-height: 15rem !important;
        overflow: auto;
    }

    .query-builder-container {
        .query-builder {
            margin: 0;

            .group {
                background-color: transparent;
                border: none;
            }

            .input {
                height: 2rem !important;
            }
        }

        .rule--field {
            width: 20rem !important;
        }

        .rule--operator {
            width: 15rem !important;
        }

        .rule--value {
            .merchant-location {
                width: 36rem !important;
            }
        }
    }

    // * To remove unwanted table header.
    .table > thead {
        display: none;
    }

 /*   // * To remove row hover background color.
    .table-hover > tbody > tr:hover {
        background-color: #fff;
    }*/

    // * To remove border between.
    .table-hover > tbody > tr {
        border: none;
        background-color: #fff;
    }

    // * To remove default row padding and margins.
    .table > tbody > tr > td {
        padding: 0;
        margin: 0;
    }
}
