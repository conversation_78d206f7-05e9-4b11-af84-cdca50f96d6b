import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    Form,
    IcIcon,
    Modal,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faSync } from "FaICIconMap";
import { CreateCampaignContext, DataContext } from "Contexts";
import { CampaignTypes, PromotionalCampaignFilter, PromotionalCampaignMsg } from "Data";
import SegmentsOrFiltersSummaryView from "../segmentsOrFiltersSummaryView/SegmentsOrFiltersSummaryView";

import "./CampaignSegmentSelector.scss";

const CampaignSegmentSelector = ({ show, onHide }) => {
    const { segments } = useContext(DataContext);
    const {
        campaignType,
        to,
        selectSegments,
        appliedFilters,
        isLoadingMemberCount,
        membersCountForSegmentsOrFilters,
        setToSegments,
        setMembersCountForSegmentsOrFilters,
        loadMembersCountDataForFiltersOrSegments,
        setAppliedFilters,
    } = useContext(CreateCampaignContext);
    const [selectedSegments, setSelectedSegments] = useState([]);
    const [isConfirming, setIsConfirming] = useState(false);
    const [validated, setValidated] = useState(false);
    const [membersCountPreview, setMembersCountPreview] = useState(0);
    const [isLoadingMembersCountPreview, setIsLoadingMembersCountPreview] = useState(false);
    const [selectedFilterFiltersToApplyForMemberCount, setSelectedFilterFiltersToApplyForMemberCount] = useState([]);

    const segmentOptions = useMemo(
        () =>
            segments.map((segment) => ({
                label: segment?.name || "~ unknown",
                value: segment._id || "~ unknown",
                memberFilter: segment?.memberFilter || segment?.filter || {},
                transactionFilter: segment?.transactionFilter || {},
            })),
        [segments],
    );

    const isSegmentsNotSelected = useMemo(() => {
        return (
            selectSegments === "SELECT_MEMBER_SEGMENTS" &&
            selectedSegments.length === 0
        );
    }, [selectSegments, selectedSegments.length]);

    const isMaxNoOfSegmentsSelected = useMemo(() => {
        return (
            selectSegments === "SELECT_MEMBER_SEGMENTS" &&
            selectedSegments.length > 5
        );
    }, [selectSegments, selectedSegments.length]);

    const segmentValidityClasses = useMemo(() => {
        if (validated) {
            return !isSegmentsNotSelected && !isMaxNoOfSegmentsSelected
                ? "select-valid"
                : "select-invalid";
        }
        return "";
    }, [isSegmentsNotSelected, isMaxNoOfSegmentsSelected, validated]);

    const onSelectSegments = useCallback((event) => {
            setMembersCountPreview(0);
            let filtersToApply = [];
            if (event.length === 0) {
                filtersToApply = [];
                setSelectedFilterFiltersToApplyForMemberCount([]);
            }
            event.forEach((item) => {
                filtersToApply.push({
                    ...(Object.keys(item?.memberFilter)?.length > 0 ? { memberFilter: item?.memberFilter } : {}),
                    ...(Object.keys(item?.transactionFilter)?.length > 0 ? { transactionFilter: item?.transactionFilter } : {}),
                    segment: {
                        id: item?.value || "",
                        name: item?.label || "~ unknown",
                        isCustomFilter: false,
                    },
                });
            });

            setSelectedSegments(event);
            setSelectedFilterFiltersToApplyForMemberCount(filtersToApply);
        },
        [
            setSelectedSegments,
            setMembersCountPreview,
            setSelectedFilterFiltersToApplyForMemberCount,
        ],
    );

    const onLoadMembersCountForFiltersOrSegments = useCallback(async () => {
        setIsLoadingMembersCountPreview(true);
        const updatedFilters=appliedFilters.concat(selectedFilterFiltersToApplyForMemberCount);
        const membersCountDataResponses = await loadMembersCountDataForFiltersOrSegments(
            updatedFilters,
        );
        setMembersCountPreview(membersCountDataResponses);
        setIsLoadingMembersCountPreview(false);
    }, [
        selectedFilterFiltersToApplyForMemberCount,
        loadMembersCountDataForFiltersOrSegments,
        setMembersCountPreview,
        appliedFilters
    ]);

    const onConfirmSegmentSelection = useCallback(
        async (event) => {
            try {
                event.preventDefault();
                event.stopPropagation();

                if (!isSegmentsNotSelected && !isMaxNoOfSegmentsSelected) {
                    const updatedFilters=appliedFilters.filter((appliedFilter)=>appliedFilter?.id===PromotionalCampaignFilter?.id).concat(selectedFilterFiltersToApplyForMemberCount);
                    if (membersCountPreview === 0) {
                        try {
                            setIsConfirming(true);
                            const membersCountDataResponse =
                                await loadMembersCountDataForFiltersOrSegments(
                                    updatedFilters,
                                    true,
                                );
                            setIsConfirming(false);
                            setToSegments(selectedSegments);
                            setAppliedFilters(updatedFilters);
                            setMembersCountForSegmentsOrFilters(
                                membersCountDataResponse,
                            );
                        } catch (e) {
                            throw e;
                        }
                    } else {
                        setToSegments(selectedSegments);
                        setAppliedFilters(updatedFilters);
                        setMembersCountForSegmentsOrFilters(
                            membersCountPreview,
                        );
                    }
                    onHide();
                } else {
                    setValidated(true);
                }
            } catch (e) {
                setIsConfirming(false);
                console.error(e);
                toast.error(
                    <div>
                        Failed to confirm segment selection/s!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>,
                );
            }
        },
        [
            isSegmentsNotSelected,
            isMaxNoOfSegmentsSelected,
            membersCountPreview,
            selectedFilterFiltersToApplyForMemberCount,
            onHide,
            setToSegments,
            selectedSegments,
            setMembersCountForSegmentsOrFilters,
            loadMembersCountDataForFiltersOrSegments,
            setValidated,
            setIsConfirming,
            setAppliedFilters,
            appliedFilters
        ],
    );

    useEffect(() => {
        if (to.length !== 0) setSelectedSegments(to);
    }, [to]);

    useEffect(() => {
        if (membersCountForSegmentsOrFilters > 0)
            setMembersCountPreview(membersCountForSegmentsOrFilters);
    }, [membersCountForSegmentsOrFilters]);

    useEffect(() => {
        if (appliedFilters?.length !== 0)
            setSelectedFilterFiltersToApplyForMemberCount(
                appliedFilters,
            );
    }, [appliedFilters]);

    return (
        <Modal
            className="campaign-segment-selector-modal-view"
            show={show}
            onHide={onHide}
            size="lg"
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!(isLoadingMemberCount || isConfirming)}>
                <Modal.Title>Campaign Segment Selector</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form
                    validated={validated}
                    onSubmit={onConfirmSegmentSelection}
                    noValidate
                >
                    <div className="mb-4 text-info">
                        <div className="mb-1">
                            <small>
                                {"* Select a "}
                                <span className="font-weight-bold">
                                    segment
                                </span>
                                {" or "}
                                <span className="font-weight-bold">
                                    segments
                                </span>
                                {" the campaign should target."}
                            </small>
                        </div>
                        <div>
                            <small>
                                {"* You can select up to a "}
                                <span className="font-weight-bold">
                                    maximum of 5
                                </span>
                                {" segments per campaign."}
                            </small>
                        </div>
                    </div>
                    <Form.Group>
                        <div className={segmentValidityClasses}>
                            <Form.Label>
                                Segments
                                <span className="text-danger">*</span>
                            </Form.Label>
                            <Form.Select
                                id="to-segments"
                                labelKey="label"
                                options={segmentOptions}
                                placeholder={
                                    segments.length === 0
                                        ? "No segments found."
                                        : "Select a segment or multiple segments..."
                                }
                                selected={selectedSegments}
                                disabled={
                                    isLoadingMemberCount ||
                                    isConfirming ||
                                    segments.length === 0
                                }
                                onChange={onSelectSegments}
                                multiple
                                clearButton
                            />
                        </div>
                        {validated && isSegmentsNotSelected && (
                            <Form.Text className="text-danger">
                                * Segments cannot be empty!
                            </Form.Text>
                        )}
                        {validated && isMaxNoOfSegmentsSelected && (
                            <Form.Text className="text-danger">
                                * Only 5 segments can be selected per campaign!
                                {` You have selected ${selectedSegments.length} segments.`}
                            </Form.Text>
                        )}
                    </Form.Group>
                    {isLoadingMembersCountPreview && !isConfirming ? (
                        <div className="text-center font-weight-bold border rounded p-2 grey-bg">
                            <div className="my-2">
                                Loading members count preview...
                            </div>
                        </div>
                    ) : (
                        <>
                            {membersCountPreview > 0 ? (
                                <SegmentsOrFiltersSummaryView
                                    totalMembersCount={membersCountPreview}
                                    selectedSegments={selectedSegments}
                                    noShowReload
                                    onLoadMembersCountForFiltersOrSegments={
                                        onLoadMembersCountForFiltersOrSegments
                                    }
                                />
                            ) : null}
                        </>
                    )}
                    {campaignType[0]?.value === CampaignTypes.PROMOTIONAL ? (
                        <div className="mt-3 px-3 py-2 info-div-type-secondary rounded">
                            <small>{PromotionalCampaignMsg}</small>
                        </div>
                    ) : null}
                    <Modal.Footer
                        className={`mt-5 d-flex justify-content-${
                            selectedSegments.length !== 0 &&
                            !isMaxNoOfSegmentsSelected
                                ? "between"
                                : "end"
                        } align-items-center`}
                    >
                        {selectedSegments.length !== 0 &&
                            !isMaxNoOfSegmentsSelected && (
                                <div className="text-center">
                                    <Button
                                        className="p-0 shadow-none"
                                        variant="link"
                                        size="sm"
                                        disabled={
                                            isLoadingMemberCount || isConfirming
                                        }
                                        onClick={
                                            onLoadMembersCountForFiltersOrSegments
                                        }
                                    >
                                        <IcIcon
                                            size="md"
                                            className="mr-2"
                                            icon={faSync}
                                        />
                                        Preview Members Count
                                    </Button>
                                </div>
                            )}
                        <div>
                            <Button
                                className="mr-2"
                                variant="outline-primary"
                                size="sm"
                                disabled={isLoadingMemberCount || isConfirming}
                                onClick={onHide}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                variant="primary"
                                size="sm"
                                disabled={isLoadingMemberCount || isConfirming}
                            >
                                {isLoadingMemberCount && isConfirming
                                    ? "Loading members count..."
                                    : "Confirm"}
                            </Button>
                        </div>
                    </Modal.Footer>
                </Form>
            </Modal.Body>
        </Modal>
    );
};

CampaignSegmentSelector.defaultProps = {
    show: false,
    onConfirm: () => {
    },
    onHide: () => {
    },
};

CampaignSegmentSelector.propTypes = {
    show: PropTypes.bool,
    onConfirm: PropTypes.func,
    onHide: PropTypes.func,
};

export default React.memo(CampaignSegmentSelector);
