import React, { use<PERSON><PERSON>back, useContext, useEffect, useState } from "react";
import {
    <PERSON><PERSON>,
    Card,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faFilter, faFilterSlash } from "FaICIconMap";
import { DataContext, UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    PointRuleSubTypes,
} from "Data";
import { useToggle } from "Hooks";
import { TransactionRuleContext } from "../context/TransactionalRuleContext";
import CreatePointRule from "../pointRuleCreate/CreatePointRule";
import FilterDropdown from "./FilterDropdown";

const FilterOptions = [
    { label: "Merchant", value: "MERCHANT" },
    { label: "Status", value: "STATUS" },
    { label: "Rule Sub Type", value: "POINT_RULE_SUB_TYPE" },
];

const PointRuleSubTypeOptions = Object.values(PointRuleSubTypes)
    .filter(
        (item) =>
            ![
                PointRuleSubTypes.ENROLL.value,
                PointRuleSubTypes.SIGNUP.value,
                PointRuleSubTypes.PROFILE_COMPLETION.value,
                PointRuleSubTypes.BIRTHDAY.value,
            ].includes(item?.value)
    ) // * Comment if non-transactional rule sub type filters are needed.
    .map((subType) => ({
        label: subType?.name,
        value: subType?.value,
    }));

const TopPanel = () => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const { merchants } = useContext(DataContext);
    const { locationState, isLoading, onSetLocationState, onApplyFilter } =
        useContext(TransactionRuleContext);
    const [selectedFilter, setSelectedFilter] = useState([]);
    const [merchantFilter, setMerchantFilter] = useState();
    const [statusFilter, setStatusFilter] = useState();
    const [ruleSubTypeFilter, setRuleSubTypeFilter] = useState();
    const [appliedFilter, setAppliedFilter] = useState("");
    const [filterApplied, setFilterApplied] = useState(false);
    const [isFilterSet, setIsFilterSet] = useState(false);
    const [showFilters, toggleShowFilters] = useToggle(false);

    const onSelectFilter = useCallback(
        (e) => {
            setMerchantFilter();
            setStatusFilter();
            setRuleSubTypeFilter();
            setSelectedFilter(e);
        },
        [setSelectedFilter]
    );

    const onSelectMerchant = useCallback(
        (e) => {
            setMerchantFilter(e);
            setIsFilterSet(true);
            if (appliedFilter === e[0]?._id) {
                setFilterApplied(true);
            } else {
                setFilterApplied(false);
            }
        },
        [appliedFilter, setMerchantFilter, setIsFilterSet, setFilterApplied]
    );

    const onSetStatus = useCallback(
        (e) => {
            setStatusFilter(e);
            setIsFilterSet(true);
            if (appliedFilter === e[0]?.value) {
                setFilterApplied(true);
            } else {
                setFilterApplied(false);
            }
        },
        [appliedFilter, setStatusFilter, setIsFilterSet, setFilterApplied]
    );

    const onSelectRuleType = useCallback(
        (e) => {
            setRuleSubTypeFilter(e);
            setIsFilterSet(true);
            if (appliedFilter === e[0]?.value) {
                setFilterApplied(true);
            } else {
                setFilterApplied(false);
            }
        },
        [appliedFilter, setRuleSubTypeFilter, setIsFilterSet, setFilterApplied]
    );

    const applyFilter = useCallback(() => {
        if (merchantFilter) {
            onApplyFilter("MERCHANT", merchantFilter?.[0]._id);
            setAppliedFilter(merchantFilter?.[0]._id);
        } else if (statusFilter) {
            onApplyFilter("STATUS", statusFilter?.[0].value);
            setAppliedFilter(statusFilter?.[0].value);
        } else if (ruleSubTypeFilter) {
            onApplyFilter("POINT_RULE_SUB_TYPE", ruleSubTypeFilter?.[0]?.value);
            setAppliedFilter(ruleSubTypeFilter?.[0].value);
        }
        setFilterApplied(true);
    }, [
        merchantFilter,
        statusFilter,
        ruleSubTypeFilter,
        onApplyFilter,
        setAppliedFilter,
        setFilterApplied,
    ]);

    const clearFilter = useCallback(() => {
        onApplyFilter();
        setSelectedFilter([]);
        setMerchantFilter();
        setStatusFilter();
        setRuleSubTypeFilter();
        setIsFilterSet(false);
        setAppliedFilter("");
        setFilterApplied(false);
    }, [
        onApplyFilter,
        setSelectedFilter,
        setMerchantFilter,
        setStatusFilter,
        setRuleSubTypeFilter,
        setIsFilterSet,
        setAppliedFilter,
        setFilterApplied,
    ]);

    const onShowFilters = useCallback(() => {
        if (isFilterSet && !filterApplied) {
            clearFilter();
        }
        toggleShowFilters();
    }, [isFilterSet, filterApplied, clearFilter, toggleShowFilters]);

    useEffect(() => {
        if (locationState?.filter) {
            switch (locationState.filter.key) {
                case "merchant": {
                    toggleShowFilters();
                    setSelectedFilter([FilterOptions[0]]);
                    setMerchantFilter([
                        {
                            _id: locationState.filter.value?._id,
                            merchantName:
                                locationState.filter.value?.merchantName,
                        },
                    ]);
                    setIsFilterSet(true);
                    setFilterApplied(true);
                    setAppliedFilter(locationState.filter.value?._id);
                    onApplyFilter("MERCHANT", locationState.filter.value?._id);
                    break;
                }
                case "ruleType": {
                    toggleShowFilters();
                    setSelectedFilter([FilterOptions[2]]);
                    setRuleSubTypeFilter(
                        PointRuleSubTypeOptions.filter(
                            (subType) =>
                                subType?.value ===
                                PointRuleSubTypes.AFFINITY.value
                        )
                    );
                    setIsFilterSet(true);
                    setFilterApplied(true);
                    setAppliedFilter(PointRuleSubTypes.AFFINITY.value);
                    onApplyFilter(
                        "POINT_RULE_SUB_TYPE",
                        PointRuleSubTypes.AFFINITY.value
                    );
                    break;
                }
                default: {
                    break;
                }
            }
            onSetLocationState("");
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [locationState]);

    return (
        <div className="mt-5">
            {/* 
              // TODO: Optional UX improving feature: Show a badge if filter applied and filter buttons are hidden. 
            */}
            <div className="d-flex flex-row justify-content-end">
                <div className="mb-3">
                    {isAuthorizedForAction(
                        AccessPermissionModuleNames.POINT_RULES,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.POINT_RULES
                        ].actions.CreatePointRule
                    ) && (
                        <CreatePointRule
                            location={locationState?.addPointRule}
                        />
                    )}
                    <Button
                        variant="outline-primary"
                        size="sm"
                        disabled={isLoading}
                        onClick={onShowFilters}
                    >
                        <IcIcon
                            className="mr-2"
                            size="lg"
                            icon={showFilters ? faFilterSlash : faFilter}
                        />
                        {showFilters ? "Hide Filters" : "Filter By"}
                    </Button>
                </div>
            </div>
            <div className="mt-3">
                {showFilters && (
                    <Card>
                        <Card.Body>
                            <div className="pr-2 pt-2 d-flex flex-row text-center">
                                <div className="mb-3 mr-2">
                                    <FilterDropdown
                                        isLoading={isLoading}
                                        merchantList={merchants}
                                        filterOptions={FilterOptions}
                                        pointRuleSubTypes={
                                            PointRuleSubTypeOptions
                                        }
                                        selectedFilter={selectedFilter}
                                        merchantFilter={merchantFilter}
                                        statusFilter={statusFilter}
                                        ruleSubTypeFilter={ruleSubTypeFilter}
                                        filterApplied={filterApplied}
                                        isFilterSet={isFilterSet}
                                        appliedFilter={appliedFilter}
                                        onSelectFilter={onSelectFilter}
                                        onSelectMerchant={onSelectMerchant}
                                        onSetStatus={onSetStatus}
                                        onSelectRuleType={onSelectRuleType}
                                        applyFilter={applyFilter}
                                        clearFilter={clearFilter}
                                    />
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                )}
            </div>
        </div>
    );
};

export default TopPanel;
