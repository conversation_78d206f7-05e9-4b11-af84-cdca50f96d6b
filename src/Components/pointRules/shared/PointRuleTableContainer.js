import React, { useState, useC<PERSON>back, useContext, useMemo } from "react";
import { toast } from "react-toastify";
import update from "immutability-helper";
import moment from "moment";
import { v4 as uuidv4 } from "uuid";
import {
    IcIcon,
    Badge,
    Button,
    Row,
    Col,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faTransaction,
    faPen,
    faCoins,
    faObjectGroup,
    faArchive,
    faCalendar,
    faPower,
    faMoneyBill,
} from "FaICIconMap";
import { DataContext, UserContext } from "Contexts";
import {
    PointRuleTypes,
    PointRuleSubTypes,
    PointRuleStates,
    PointRuleStatus,
    Occasions,
    AccessPermissionModuleNames,
    AccessPermissionModules,
    AffinityGroupOptions,
    PointRuleStatusColorCode,
    LocationSeasonalSubTypes,
    OccasionWithDates,
} from "Data";
import { editPoints } from "Services";
import { toTitleCase, toTitleCaseFromCamelCase } from "Utils";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import { TransactionRuleContext } from "../context/TransactionalRuleContext";
import TransactionalDetails from "../transactionalRules/TransactionalDetails";
import NonTransactionalDetails from "../nonTransactionalRules/NonTransactionalDetails";
import NonTransactionalRuleEdit from "../pointRuleEdit/NonTransactionalRuleEdit";
import { PointRuleCreateWizardV2 } from "../pointRuleCreate/PointRuleCreateWizardV2";
import { CreatePointRuleContext } from "../pointRuleCreate/context/CreatePointRuleContext";
import PointRuleTable from "./PointRuleTable";
import ArchiveModal from "./ArchiveModal";

import "./PointRuleTableContainer.scss";

const today = moment().toDate();

const transactionalRules = [
    { name: "pointRule", headerStyle: { width: "24%" }, icon: faCoins },
    { name: "ruleSubType", headerStyle: { width: "20%" }, icon: faObjectGroup },
    { name: "status", headerStyle: { width: "10%" }, icon: faPower },
    { name: "merchant", headerStyle: { width: "12%" }, icon: faCoins },
    {
        name: "transactions",
        headerStyle: { width: "10%" },
        icon: faTransaction,
    },
    { name: "createdDate", headerStyle: { width: "14%" }, icon: faCalendar },
    { name: "", headerStyle: { width: "18%" }, icon: null },
];

const nonTransactionalRules = [
    { name: "pointRule", headerStyle: { width: "30%" }, icon: faCoins },
    { name: "status", icon: faPower },
    { name: "points", icon: faMoneyBill },
    { name: "transactions", icon: faTransaction },
    { name: "createdDate", headerStyle: { width: "14%" }, icon: faCalendar },
    { name: "", headerStyle: { width: "18%" }, icon: null },
];

const defaultColumnTemplate = ({ name, icon, ...rest }) => ({
    dataField: name,
    text: (
        <div className="d-flex align-items-center">
            {icon && <IcIcon size="lg" className="mr-2" icon={icon} />}
            {toTitleCaseFromCamelCase(name)}
        </div>
    ),
    sort: false,
    ...rest,
});

const PointRuleTableContainer = () => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const { affinityGroups, tiers: tierList } = useContext(DataContext);
    const {
        tab,
        pointRuleList,
        totalCount,
        skip,
        limit,
        merchant,
        status,
        subType,
        loadPointRuleData,
        isLoading,
        onChangePagination,
        onChangePageSize,
    } = useContext(TransactionRuleContext);
    const {
        setShowPointRuleCreateWizard: setShowTransactionalRuleEdit,
        showPointRuleCreateWizard: showTransactionalRuleEdit,
    } = useContext(CreatePointRuleContext);
    const [showTransactionalDetails, setShowTransactionalDetails] =
        useState(false);
    const [showNonTransactionalDetails, setShowNonTransactionalDetails] =
        useState(false);
    const [showNonTransactionalEdit, setShowNonTransactionalEdit] =
        useState(false);
    const [selectedPointRule, setSelectedPointRule] = useState({});
    const [isUpdating, setIsUpdating] = useState(false);
    const [validated, setValidated] = useState(false);
    const [updatedPoint, setUpdatedPoint] = useState({
        _id: "",
        merchantId: "",
        ruleName: "",
        description: "",
        pointAmount: 0,
        status: "",
        merchantLocation: [],
        occasion: "",
        subType: "",
        matchedCount: 0,
        affinityGroup: [],
        tierBasedAmountsPerPoint: [],
    });
    const [initialPointState, setInitialPointState] = useState({
        _id: "",
        merchantId: "",
        ruleName: "",
        description: "",
        pointAmount: 0,
        status: "",
        merchantLocation: [],
        occasion: "",
        subType: "",
        matchedCount: 0,
        affinityGroup: [],
        tierBasedAmountsPerPoint: [],
    });
    const [currentValues, setCurrentValues] = useState({});
    const [daysOfWeek, setDaysOfWeek] = useState([]);
    const [startDate, setStartDate] = useState("");
    const [endDate, setEndDate] = useState("");
    const [selectedId, setSelectedId] = useState("");
    const [showArchivePointRule, setShowArchivePointRule] = useState(false);
    const [capValue, setCapValue] = useState(false);

    const affinityGroupList = useMemo(
        () =>
            affinityGroups.length !== 0
                ? affinityGroups.map((group) => ({
                    _id: group?._id,
                    name: group?.name,
                }))
                : [],
        [affinityGroups]
    );

    const onCloseDetailsModal = useCallback(() => {
        if (tab === PointRuleTypes.TRANSACTIONAL) {
            setShowTransactionalDetails(false);
        } else {
            setShowNonTransactionalDetails(false);
        }

        setSelectedPointRule({});
    }, [
        setShowTransactionalDetails,
        setShowNonTransactionalDetails,
        setSelectedPointRule,
        tab,
    ]);

    const onChangedHandler = useCallback(
        (e) => {
            const name = e.currentTarget.name;
            const value = e.currentTarget.value;

            setUpdatedPoint((prevalue) => ({
                ...prevalue,
                [name]: value,
            }));
        },
        [setUpdatedPoint]
    );

    const onChangeStatus = useCallback(
        (selected) => {
            setUpdatedPoint((prevalue) => ({
                ...prevalue,
                status: selected[0]?.value,
            }));
        },
        [setUpdatedPoint]
    );

    const onChangeConsiderFields = useCallback(() => {
        setUpdatedPoint((prevValue) => ({
            ...prevValue,
            ruleData: {
                ...prevValue?.ruleData,
                completionBonusConfigurations: {
                    ...prevValue?.ruleData?.completionBonusConfigurations,
                    considerAllFields:
                        !prevValue?.ruleData?.completionBonusConfigurations
                            ?.considerAllFields,
                },
            },
        }));
    }, [setUpdatedPoint]);

    const onAddRuleDataFieldConfigurations = useCallback(() => {
        setUpdatedPoint((prevValue) => {
            return update(prevValue, {
                ruleData: {
                    completionBonusConfigurations: {
                        fieldConfigurations: {
                            $push: [
                                {
                                    fieldId: uuidv4(),
                                    fieldName: "",
                                    points: null,
                                },
                            ],
                        },
                    },
                },
            });
        });
    }, [setUpdatedPoint]);

    const onRemoveRuleDataFieldConfigurations = useCallback(
        (index) => {
            setUpdatedPoint((prevValue) => {
                return update(prevValue, {
                    ruleData: {
                        completionBonusConfigurations: {
                            fieldConfigurations: {
                                $splice: [[index, 1]],
                            },
                        },
                    },
                });
            });
        },
        [setUpdatedPoint]
    );

    const onChangeRuleDataFieldConfigurations = useCallback(
        (index, data) =>
            setUpdatedPoint((prevValue) => {
                return update(prevValue, {
                    ruleData: {
                        completionBonusConfigurations: {
                            fieldConfigurations: {
                                [index]: {
                                    $set: {
                                        ...prevValue?.ruleData
                                            ?.completionBonusConfigurations
                                            ?.fieldConfigurations[index],
                                        ...data,
                                    },
                                },
                            },
                        },
                    },
                });
            }),
        [setUpdatedPoint]
    );

    const onChangeRuleDataFieldConfigurationsPoints = useCallback(
        (index, pointsValue) =>
            setUpdatedPoint((prevValue) => {
                return update(prevValue, {
                    ruleData: {
                        completionBonusConfigurations: {
                            fieldConfigurations: {
                                [index]: {
                                    $set: {
                                        ...prevValue?.ruleData
                                            ?.completionBonusConfigurations
                                            ?.fieldConfigurations[index],
                                        points: pointsValue,
                                    },
                                },
                            },
                        },
                    },
                });
            }),
        [setUpdatedPoint]
    );

    const onShowEditTransactionalRuleModal = useCallback(
        (e) => {
            e.stopPropagation();

            const selected = pointRuleList.find(
                (rule) => rule._id === e.currentTarget.dataset.id
            );

            // TODO: UPDATE THIS AND REMOVE OLD EDIT FILE.

            setShowTransactionalRuleEdit(true);
            setSelectedPointRule({
                ...selected,
                affinityGroupOption: selected?.ruleData?.isTierBased
                    ? AffinityGroupOptions.TIERS
                    : AffinityGroupOptions.ENTIER,
            });
        },
        [pointRuleList, setShowTransactionalRuleEdit, setSelectedPointRule]
    );

    const onCloseEditTransactionalRuleModal = useCallback(
        () => setSelectedPointRule({}),
        [setSelectedPointRule]
    );

    const onShowEditModal = useCallback(
        (e) => {
            e.stopPropagation();

            const selected = pointRuleList.find(
                (rule) => rule._id === e.currentTarget.dataset.id
            );
            const affinityGroup =
                affinityGroupList.length !== 0
                    ? affinityGroupList.filter(
                        (group) =>
                            group?._id === selected?.ruleData?.affinityGroupId
                    )
                    : [];

            const tierBasedAmountsPerPoint =
                selected?.ruleData?.tierBasedAmountsPerPoint.reduce(
                    (obj, item) =>
                        Object.assign(obj, {
                            [item.tierId]: item.amountPerPoint.toString(),
                        }),
                    {}
                );

            setSelectedPointRule(selected);
            setCurrentValues({
                _id: selected?._id,
                ruleName: selected?.name,
                description: selected?.description,
                maxPoints: selected?.maxPoints,
                pointAmount:
                    tab === PointRuleTypes.TRANSACTIONAL
                        ? selected?.ruleData?.amountPerPoint?.toString()
                        : selected?.ruleData?.points?.toString(),
                status: selected?.status,
                subType: selected?.subType,
                ...(tab === PointRuleTypes.TRANSACTIONAL && {
                    merchantId: selected?.merchantId,
                    merchantLocation: [
                        {
                            _id: selected?.merchantLocation?._id,
                            locationName:
                                selected?.merchantLocation?.locationName,
                        },
                    ],
                    occasion: selected?.ruleData?.recurrence,
                    matchedCount: selected?.matchedCount,
                    tierBasedAmountsPerPoint: tierBasedAmountsPerPoint,
                    affinityGroup: affinityGroup,
                    affinityGroupOption: selected?.ruleData?.isTierBased
                        ? AffinityGroupOptions.TIERS
                        : AffinityGroupOptions.ENTIER,
                }),

                ...(tab === PointRuleTypes.NON_TRANSACTIONAL && {
                    matchedCount: selected?.matchedCount, // * Include matchedCount for non-transactional rules.
                }),
            });

            const point = {
                _id: selected?._id,
                type: tab,
                maxPoints: selected?.maxPoints,
                ruleName: selected?.name,
                description: selected?.description,
                pointAmount:
                    tab === PointRuleTypes.TRANSACTIONAL
                        ? selected?.ruleData?.amountPerPoint?.toString()
                        : selected?.ruleData?.points?.toString(),
                status: selected?.status,
                subType: selected?.subType,
                ...(tab === PointRuleTypes.TRANSACTIONAL && {
                    merchantId: selected?.merchantId,
                    merchantLocation: [
                        {
                            _id: selected?.merchantLocation?._id,
                            locationName:
                                selected?.merchantLocation?.locationName,
                        },
                    ],
                    occasion: selected?.ruleData?.recurrence,
                    matchedCount: selected?.matchedCount,
                    tierBasedAmountsPerPoint: tierBasedAmountsPerPoint,
                    affinityGroup: affinityGroup,
                    affinityGroupOption: selected?.ruleData?.isTierBased
                        ? AffinityGroupOptions.TIERS
                        : AffinityGroupOptions.ENTIER,
                }),
                ...(tab === PointRuleTypes.NON_TRANSACTIONAL && {
                    matchedCount: selected?.matchedCount,
                }),
                ...(tab === PointRuleTypes.NON_TRANSACTIONAL &&
                    selected?.subType ===
                        PointRuleSubTypes.PROFILE_COMPLETION.value && {
                        ruleData: {
                            completionBonusConfigurations: {
                                considerAllFields:
                                    !!selected?.ruleData
                                        ?.completionBonusConfigurations
                                        ?.considerAllFields,
                                fieldConfigurations:
                                    selected?.ruleData?.completionBonusConfigurations?.fieldConfigurations?.map(
                                        (item) => ({
                                            fieldId: uuidv4(),
                                            ...item,
                                        })
                                    ) || [],
                            },
                        },
                    }),
            };
            setCapValue(!!selected?.maxPoints);
            setUpdatedPoint(point);
            setInitialPointState(point);
            setStartDate(
                moment.utc(selected?.ruleData?.fixedFromDate).toDate() || today
            );
            setEndDate(
                moment.utc(selected?.ruleData?.fixedToDate).toDate() || today
            );
            setDaysOfWeek(selected?.ruleData?.daysOfWeek);
            setShowNonTransactionalEdit(true);
        },
        [
            tab,
            pointRuleList,
            affinityGroupList,
            setShowNonTransactionalEdit,
            setSelectedPointRule,
            setCurrentValues,
            setUpdatedPoint,
            setStartDate,
            setEndDate,
            setInitialPointState,
            setCapValue,
        ]
    );

    const onHideEditModal = useCallback(() => {
        setShowNonTransactionalEdit(false);
        setValidated(false);
        setSelectedPointRule({});
        setCapValue(!!currentValues?.maxPoints);
        setUpdatedPoint({
            _id: "",
            merchantId: "",
            ruleName: "",
            description: "",
            pointAmount: 0,
            status: "",
            merchantLocation: [],
            occasion: "",
            subType: "",
            matchedCount: 0,
            affinityGroup: [],
            tierBasedAmountsPerPoint: [],
        });
    }, [
        currentValues?.maxPoints,
        setShowNonTransactionalEdit,
        setValidated,
        setSelectedPointRule,
        setCapValue,
        setUpdatedPoint,
    ]);

    const showArchiveModal = useCallback(
        (e) => {
            e.stopPropagation();

            setSelectedId(e.currentTarget.dataset.id);
            setShowArchivePointRule(true);
        },
        [setSelectedId, setShowArchivePointRule]
    );

    const onHideArchiveModal = useCallback(() => {
        setShowArchivePointRule(false);
        setSelectedId("");
    }, [setSelectedId, setShowArchivePointRule]);

    const columns = useMemo(() => {
        const columns = [];

        if (tab === PointRuleTypes.TRANSACTIONAL) {
            transactionalRules.forEach((item) => {
                columns.push(defaultColumnTemplate(item));
            });
        } else {
            nonTransactionalRules.forEach((item) => {
                columns.push(defaultColumnTemplate(item));
            });
        }

        return columns.sort((a, b) => a.order - b.order);
    }, [tab]);

    const Data = useMemo(
        () =>
            pointRuleList.map((rule) => {
                let pointsValue = null;

                if (rule?.ruleData?.isTierBased) {
                    pointsValue = applyBadgeStyling({
                        text: "Tier Based",
                        variant: "info",
                    });
                } else if (
                    tab === PointRuleTypes.NON_TRANSACTIONAL &&
                    rule?.ruleData?.completionBonusConfigurations
                        ?.totalBonusPoints
                ) {
                    pointsValue =
                        rule.ruleData.completionBonusConfigurations
                            .totalBonusPoints || 0;
                } else {
                    pointsValue =
                        tab === PointRuleTypes.TRANSACTIONAL
                            ? rule?.ruleData?.amountPerPoint || 0
                            : rule?.ruleData?.points || 0;
                }

                return {
                    id: rule?._id,
                    daysOfWeek: rule?.ruleData.daysOfWeek,
                    maxPoints: rule?.maxPoints,
                    ...(rule.hasOwnProperty("ruleData")
                        ? { ruleData: rule.ruleData }
                        : {}),
                    isTierBased: rule?.ruleData?.isTierBased,
                    tierBasedAmountsPerPoint:
                        rule?.ruleData.tierBasedAmountsPerPoint,
                    name: rule?.name,
                    description: rule?.description,
                    pointRuleStatus: rule?.status,
                    startDate: rule?.ruleData?.fixedFromDate,
                    endDate: rule?.ruleData?.fixedToDate,
                    createdDate: moment(rule?.createdOn).format("LL"),
                    merchant: rule?.merchant?.merchantName,
                    merchantLocation: rule?.merchantLocation?.locationName,
                    pointRule: (
                        <Row className="point-rule-list" noGutters={true}>
                            <Col
                                lg={3}
                                md={3}
                                sm={3}
                                xs={3}
                                className="my-auto"
                            >
                                <div className="d-flex flex-column">
                                    <div className="dot-state">
                                        {rule?.ruleState ===
                                            PointRuleStates.ACTIVE && (
                                            <>
                                                <span className="dot-success mr-1" />
                                                <small className="text-success">
                                                    Active
                                                </small>
                                            </>
                                        )}
                                        {rule?.ruleState ===
                                            PointRuleStates.EXPIRED && (
                                            <>
                                                <span className="dot-danger mr-1" />
                                                <small className="text-danger">
                                                    Expired
                                                </small>
                                            </>
                                        )}
                                        {rule?.ruleState ===
                                            PointRuleStates.SCHEDULED && (
                                            <>
                                                <span className="dot-warning mr-1" />
                                                <small className="text-warning">
                                                    Scheduled
                                                </small>
                                            </>
                                        )}
                                    </div>
                                    <IcIcon
                                        className="text-primary m-1 border rounded border-light point-icon"
                                        size="3x"
                                        icon={
                                            PointRuleSubTypes[rule?.subType]
                                                ?.icon
                                        }
                                    />
                                </div>
                            </Col>
                            <Col
                                lg={9}
                                md={9}
                                sm={9}
                                xs={9}
                                className="my-auto"
                            >
                                <div className="font-weight-bold">
                                    {rule?.name}
                                </div>
                                <div className="mt-2 point-description">
                                    {rule?.description}
                                </div>
                            </Col>
                        </Row>
                    ),
                    ruleSubType: <>{PointRuleSubTypes[rule?.subType]?.name}</>,
                    pointValue:
                        tab === PointRuleTypes.TRANSACTIONAL
                            ? rule?.ruleData?.amountPerPoint
                            : rule?.ruleData?.points,
                    status: (
                        <Badge
                            className="py-2 px-3"
                            variant={PointRuleStatusColorCode[rule?.status]}
                        >
                            {toTitleCase(rule?.status)}
                        </Badge>
                    ),
                    points: pointsValue,
                    transactions: rule?.matchedCount,
                    subType: rule?.subType,
                    recurrence: rule?.ruleData?.recurrence,
                    ...(rule?.status !== PointRuleStatus.ARCHIVED
                        ? {
                            "": (
                                <div className="text-center d-flex align-items-center justify-content-end">
                                    {isAuthorizedForAction(
                                        AccessPermissionModuleNames.POINT_RULES,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames
                                                .POINT_RULES
                                        ].actions.UpdatePointRule
                                    ) && (
                                        <Button
                                            variant="primary"
                                            size="sm"
                                            data-id={rule?._id}
                                            data-rule={rule}
                                            onClick={
                                                tab ===
                                                PointRuleTypes.TRANSACTIONAL
                                                    ? onShowEditTransactionalRuleModal
                                                    : onShowEditModal
                                            }
                                            className="mr-2"
                                        >
                                            <div className="d-flex align-items-center">
                                                <IcIcon
                                                    className="mr-2"
                                                    size="lg"
                                                    icon={faPen}
                                                />
                                                Edit
                                            </div>
                                        </Button>
                                    )}
                                    {isAuthorizedForAction(
                                        AccessPermissionModuleNames.POINT_RULES,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames
                                                .POINT_RULES
                                        ].actions.DeletePointRule
                                    ) && (
                                        <Button
                                            className="action-btn mr-2"
                                            size="sm"
                                            variant="outline-danger"
                                            data-id={rule?._id}
                                            onClick={showArchiveModal}
                                        >
                                            <div className="d-flex align-items-center">
                                                <IcIcon
                                                    className="mr-2"
                                                    size="lg"
                                                    icon={faArchive}
                                                />
                                                Archive
                                            </div>
                                        </Button>
                                    )}
                                </div>
                            ),
                        }
                        : null),
                };
            }),
        [
            tab,
            pointRuleList,
            isAuthorizedForAction,
            onShowEditTransactionalRuleModal,
            onShowEditModal,
            showArchiveModal,
        ]
    );

    const onUpdatePointRule = useCallback(
        async (e) => {
            e.preventDefault();

            if (e.target.checkValidity()) {
                try {
                    const payload = {
                        name: updatedPoint?.ruleName,
                        description: updatedPoint?.description,
                        ...(capValue
                            ? { maxPoints: updatedPoint?.maxPoints }
                            : { maxPoints: null }),
                        status: updatedPoint?.status,
                    };

                    if (
                        updatedPoint?.affinityGroupOption ===
                            AffinityGroupOptions.TIERS ||
                        (initialPointState?.affinityGroup &&
                            initialPointState.affinityGroup[0]?._id) !==
                            (updatedPoint?.affinityGroup &&
                                updatedPoint.affinityGroup[0]?._id) ||
                        initialPointState?.type !== tab ||
                        initialPointState?.pointAmount !==
                            updatedPoint?.pointAmount ||
                        initialPointState?.subType !== updatedPoint?.subType
                    ) {
                        payload.ruleData = {
                            ...(tab === PointRuleTypes.TRANSACTIONAL
                                ? {
                                    ...(updatedPoint?.affinityGroupOption !==
                                        AffinityGroupOptions.TIERS
                                            ? {
                                                amountPerPoint:
                                                    updatedPoint?.affinityGroupOption ===
                                                    AffinityGroupOptions.ENTIER
                                                    ? Number(
                                                            updatedPoint?.pointAmount
                                                        )
                                                        : 0,
                                            }
                                        : {}
                                    ),
                                }
                                : {
                                    points: Number(updatedPoint?.pointAmount),
                                }),
                        };
                        payload.type = tab;
                        payload.subType = updatedPoint?.subType;
                    }

                    if (
                        tab === PointRuleTypes.TRANSACTIONAL &&
                        payload?.ruleData
                    ) {
                        if (
                            LocationSeasonalSubTypes.includes(
                                updatedPoint?.subType
                            )
                        ) {
                            if (
                                updatedPoint?.subType ===
                                PointRuleSubTypes.LOCATION.value
                            ) {
                                payload.ruleData.merchantLocationId =
                                    updatedPoint?.merchantLocation[0]?._id;
                            }
                            payload.ruleData.recurrence =
                                updatedPoint?.occasion;

                            if (
                                ~OccasionWithDates.indexOf(
                                    updatedPoint?.occasion
                                )
                            ) {
                                payload.ruleData.fixedFromDate = moment(
                                    startDate,
                                    "YYYY-MM-DD"
                                ).toISOString();
                                payload.ruleData.fixedToDate = moment(
                                    endDate,
                                    "YYYY-MM-DD"
                                ).toISOString();
                            }

                            payload.ruleData.daysOfWeek =
                                updatedPoint?.occasion === Occasions.NONE
                                    ? []
                                    : daysOfWeek;
                        } else if (
                            updatedPoint?.subType ===
                            PointRuleSubTypes.AFFINITY.value
                        ) {
                            payload.ruleData.affinityGroupId =
                                updatedPoint?.affinityGroup[0]?._id;
                            payload.ruleData.isTierBased =
                                updatedPoint?.affinityGroupOption ===
                                AffinityGroupOptions.TIERS;
                            if (
                                updatedPoint?.affinityGroupOption ===
                                AffinityGroupOptions.TIERS
                            ) {
                                payload.ruleData.tierBasedAmountsPerPoint =
                                    Object.entries(
                                        updatedPoint?.tierBasedAmountsPerPoint
                                    ).map(([key, value]) => ({
                                        tierId: key,
                                        amountPerPoint:
                                            updatedPoint?.affinityGroupOption ===
                                            AffinityGroupOptions.TIERS
                                                ? Number(value)
                                                : 0,
                                    }));
                            }
                        }
                    }

                    if (
                        tab === PointRuleTypes.NON_TRANSACTIONAL &&
                        selectedPointRule?.subType ===
                            PointRuleSubTypes.PROFILE_COMPLETION.value
                    ) {
                        if (
                            updatedPoint.ruleData.completionBonusConfigurations
                                .fieldConfigurations?.length === 0
                        ) {
                            setValidated(true);
                            return;
                        }

                        payload.type = PointRuleTypes.NON_TRANSACTIONAL;
                        payload.subType = selectedPointRule.subType;
                        delete payload.ruleData;
                        delete payload.maxPoints;

                        const clonedAndCleanedFieldConfigurations = JSON.parse(
                            JSON.stringify(
                                updatedPoint?.ruleData
                                    .completionBonusConfigurations
                                    .fieldConfigurations
                            )
                        )?.map((fC) => ({
                            fieldName: fC?.fieldName,
                            points: Number(fC?.points),
                        }));

                        payload.ruleData = updatedPoint?.ruleData;
                        payload.ruleData.completionBonusConfigurations.fieldConfigurations =
                            clonedAndCleanedFieldConfigurations;
                    }

                    setIsUpdating(true);
                    setValidated(false);
                    await editPoints(updatedPoint?._id, payload);
                    setIsUpdating(false);
                    setUpdatedPoint({});
                    onHideEditModal();
                    toast.success(
                        <div>
                            Successfully updated the point rule
                            {selectedPointRule?.name
                                ? ` "${selectedPointRule.name}"`
                                : ""}
                            .
                        </div>
                    );
                    await loadPointRuleData({
                        skip,
                        limit,
                        tab,
                        merchant,
                        status,
                        subType,
                    });
                } catch (e) {
                    console.error(e);
                    setIsUpdating(false);
                    toast.error(
                        <div>
                            Failed to update the point rule
                            {selectedPointRule?.name
                                ? ` "${selectedPointRule.name}"`
                                : ""}
                            !
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setValidated(true);
            }
        },
        [
            selectedPointRule,
            skip,
            limit,
            tab,
            merchant,
            status,
            subType,
            updatedPoint,
            startDate,
            endDate,
            daysOfWeek,
            loadPointRuleData,
            capValue,
            initialPointState,
            setValidated,
            setIsUpdating,
            onHideEditModal,
        ]
    );

    return (
        <div
            className={`
                ${tab === PointRuleTypes.NON_TRANSACTIONAL ? "mt-5" : "mt-1"} 
                point-rule-list
            `}
        >
            <PointRuleTable
                columns={columns}
                data={Data}
                limit={limit}
                skip={skip}
                onChangePagination={onChangePagination}
                onChangePageSize={onChangePageSize}
                totalCount={totalCount}
                isLoading={isLoading}
                setSelectedPointRule={setSelectedPointRule}
                ruleType={tab}
                setShowTransactionalDetails={setShowTransactionalDetails}
                setShowNonTransactionalDetails={setShowNonTransactionalDetails}
            />
            {showTransactionalDetails && (
                <TransactionalDetails
                    showModel={showTransactionalDetails}
                    hideModal={onCloseDetailsModal}
                    selectedPointRule={selectedPointRule}
                    tierList={tierList}
                    affinityGroupList={affinityGroupList}
                />
            )}
            {showNonTransactionalDetails && (
                <NonTransactionalDetails
                    show={showNonTransactionalDetails}
                    selectedPointRule={selectedPointRule}
                    onHide={onCloseDetailsModal}
                />
            )}
            {showTransactionalRuleEdit && (
                <PointRuleCreateWizardV2
                    existingPointRuleData={selectedPointRule}
                    updatePointRule
                    onHide={onCloseEditTransactionalRuleModal}
                />
            )}
            {showNonTransactionalEdit && (
                <NonTransactionalRuleEdit
                    show={showNonTransactionalEdit}
                    isUpdating={isUpdating}
                    validated={validated}
                    capValue={capValue}
                    initialPointState={initialPointState}
                    currentValues={currentValues}
                    updatedPoint={updatedPoint}
                    setCapValue={setCapValue}
                    setUpdatedPoint={setUpdatedPoint}
                    onHide={onHideEditModal}
                    onChangedHandler={onChangedHandler}
                    onChangeStatus={onChangeStatus}
                    onChangeConsiderFields={onChangeConsiderFields}
                    onAddRuleDataFieldConfigurations={
                        onAddRuleDataFieldConfigurations
                    }
                    onRemoveRuleDataFieldConfigurations={
                        onRemoveRuleDataFieldConfigurations
                    }
                    onChangeRuleDataFieldConfigurations={
                        onChangeRuleDataFieldConfigurations
                    }
                    onChangeRuleDataFieldConfigurationsPoints={
                        onChangeRuleDataFieldConfigurationsPoints
                    }
                    onUpdatePointRule={onUpdatePointRule}
                />
            )}
            {showArchivePointRule && (
                <ArchiveModal
                    show={showArchivePointRule}
                    onHide={onHideArchiveModal}
                    limit={limit}
                    skip={skip}
                    pointRuleMainType={tab}
                    merchant={merchant}
                    status={status}
                    subType={subType}
                    selectedID={selectedId}
                    loadPointRuleData={loadPointRuleData}
                />
            )}
        </div>
    );
};

export default PointRuleTableContainer;
