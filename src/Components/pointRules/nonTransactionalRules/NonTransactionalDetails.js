import React, { useCallback, useMemo, useState } from "react";
import paginationFactory, {
    PaginationProvider,
} from "react-bootstrap-table2-paginator";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import PropTypes from "prop-types";
import {
    BootstrapTable,
    Button,
    Modal,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { PointRuleStatusColorCode, PointRuleSubTypes } from "Data";
import { getTruncatedStringWithTooltip } from "Utils";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";
import { BootstrapTableOverlay } from "Components/utils";
import { applyBadgeStyling } from "Components/utils/styling/Styling";

import "../shared/PointRuleTableContainer.scss";

const defaultSkip = 1,
    defaultLimit = 3;

const ProfileCompletionFieldConfig = ({ fieldConfigurations }) => {
    ProfileCompletionFieldConfig.defaultProps = { fieldConfigurations: [] };

    ProfileCompletionFieldConfig.propTypes = {
        fieldConfigurations: PropTypes.array,
    };

    const [skip, setSkip] = useState(defaultSkip);

    const columns = [
        { dataField: "fieldConfig", text: "" },
        { dataField: "id", hidden: true },
    ];

    const data = useMemo(
        () =>
            fieldConfigurations
                .map((fieldConfig) => ({
                    id: fieldConfig?._id || "~ unknown",
                    fieldConfig: (
                        <div className="d-flex align-items-center">
                            <div className="w-100 mr-2">
                                <DetailsAsLabelValue
                                    label="Field Name"
                                    value={getTruncatedStringWithTooltip({
                                        value:
                                            fieldConfig?.fieldName ||
                                            "~ unknown",
                                    })}
                                />
                            </div>
                            <div className="w-100 ml-2">
                                <DetailsAsLabelValue
                                    label="Points"
                                    value={getTruncatedStringWithTooltip({
                                        value:
                                            !fieldConfig?.points &&
                                            fieldConfig?.points !== 0
                                                ? "~ unknown"
                                                : fieldConfig?.points,
                                    })}
                                />
                            </div>
                        </div>
                    ),
                }))
                .slice(
                    (skip - 1) * defaultLimit,
                    (skip - 1) * defaultLimit + defaultLimit
                ),
        [fieldConfigurations, skip]
    );

    const onChangePagination = useCallback(
        (newSkip) => setSkip(newSkip),
        [setSkip]
    );

    const options = {
        page: skip,
        sizePerPage: defaultLimit,
        totalSize: fieldConfigurations.length,
        pageStartIndex: 1,
        paginationSize: defaultLimit,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageList: [],
        onPageChange: onChangePagination,
    };

    const onTableChange = (_type, _newState) => {
        // * When "remote" is enabled, the "onTableChange" prop is activated automatically, so if we don't provide this method react-bootstrap will throw an error.
    };

    return (
        <PaginationProvider
            pagination={paginationFactory(options)}
            keyField="id"
            columns={columns}
            data={data}
        >
            {({ paginationTableProps }) => (
                <ToolkitProvider
                    keyField="id"
                    data={data}
                    columns={columns}
                    columnToggle
                >
                    {(props) => (
                        <div className="custom-details-view p-2 border rounded">
                            <BootstrapTable
                                {...paginationTableProps}
                                remote={{
                                    search: true,
                                    pagination: true,
                                }}
                                keyField="id"
                                onTableChange={onTableChange}
                                overlay={BootstrapTableOverlay}
                                {...props.baseProps}
                            />
                        </div>
                    )}
                </ToolkitProvider>
            )}
        </PaginationProvider>
    );
};

const NonTransactionalDetails = ({ show, onHide, selectedPointRule }) => {
    const completionBonusConfigurations = useMemo(() => {
        let returnValue = {};
        if (
            selectedPointRule?.subType ===
                PointRuleSubTypes.PROFILE_COMPLETION.value &&
            selectedPointRule?.ruleData?.completionBonusConfigurations &&
            typeof selectedPointRule.ruleData.completionBonusConfigurations ===
                "object" &&
            Object.keys(
                selectedPointRule.ruleData.completionBonusConfigurations
            ).length !== 0
        ) {
            returnValue = {
                ...selectedPointRule.ruleData.completionBonusConfigurations,
            };
        }

        return returnValue;
    }, [
        selectedPointRule?.subType,
        selectedPointRule?.ruleData?.completionBonusConfigurations,
    ]);

    return (
        <Modal
            show={show}
            onHide={onHide}
            size="lg"
            centered
            className="point-rule-list"
        >
            <Modal.Header closeButton>
                <Modal.Title>
                    Non Transactional Earning Rule Details
                </Modal.Title>
            </Modal.Header>
            <Modal.Body className="non-transactional-details-modal">
                <div className="my-3">
                    <DetailsAsLabelValue
                        label="Point Rule Name"
                        value={selectedPointRule?.name || "~ unknown"}
                    />
                </div>
                <div className="my-3">
                    <DetailsAsLabelValue
                        label="Description"
                        value={selectedPointRule?.description || "~ unknown"}
                    />
                </div>
                <div className="my-3 d-flex justify-content-between align-items-center">
                    <div className="w-100 mr-2">
                        <DetailsAsLabelValue
                            label="Sub Type"
                            value={applyBadgeStyling({
                                text: selectedPointRule?.subType || "",
                                variant: "primary",
                            })}
                        />
                    </div>
                    <div className="w-100 ml-2">
                        <DetailsAsLabelValue
                            label="Status"
                            value={applyBadgeStyling({
                                text: selectedPointRule?.pointRuleStatus || "",
                                variant:
                                    PointRuleStatusColorCode[
                                        selectedPointRule?.pointRuleStatus
                                    ],
                            })}
                        />
                    </div>
                </div>
                <div className="my-3 d-flex justify-content-between align-items-center">
                    <div className="w-100 mr-2">
                        <DetailsAsLabelValue
                            label="Points"
                            value={selectedPointRule?.points || "~ unknown"}
                        />
                    </div>
                    <div className="w-100 ml-2">
                        <DetailsAsLabelValue
                            label="Maximum Cap Value"
                            value={selectedPointRule?.maxPoints || 0}
                        />
                    </div>
                </div>
                {selectedPointRule?.subType ===
                    PointRuleSubTypes.PROFILE_COMPLETION.value && (
                    <div className="my-3">
                        <DetailsAsLabelValue
                            label="Completion Bonus Configurations"
                            isCustomValue
                            value={
                                <div className="border rounded p-3 profile-completion-configs">
                                    <div className="my-3">
                                        <DetailsAsLabelValue
                                            label="Consider All Fields"
                                            value={
                                                completionBonusConfigurations?.considerAllFields
                                                    ? "Yes"
                                                    : "No"
                                            }
                                        />
                                        <div className="text-muted">
                                            <ul>
                                                <li>
                                                    <small>
                                                        {`If `}
                                                        <span className="font-weight-bold">
                                                            "Yes"
                                                        </span>
                                                        {` points will be awarded `}
                                                        <span className="font-weight-bold">
                                                            ONLY if all the
                                                            configured fields
                                                            are completed
                                                        </span>
                                                        .
                                                    </small>
                                                </li>
                                                <li>
                                                    <small>
                                                        {`If `}
                                                        <span className="font-weight-bold">
                                                            "No"
                                                        </span>
                                                        {` points will be awarded `}
                                                        <span className="font-weight-bold">
                                                            per completed
                                                            configured field
                                                        </span>
                                                        .
                                                    </small>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div className="my-3">
                                        <DetailsAsLabelValue
                                            label="Total Bonus Points to Award"
                                            value={
                                                completionBonusConfigurations?.totalBonusPoints ||
                                                0
                                            }
                                        />
                                    </div>
                                    <div className="my-3">
                                        <DetailsAsLabelValue
                                            label="Field Configurations"
                                            isCustomValue
                                            value={
                                                Array.isArray(
                                                    completionBonusConfigurations?.fieldConfigurations
                                                ) &&
                                                completionBonusConfigurations
                                                    .fieldConfigurations
                                                    .length !== 0 ? (
                                                    <ProfileCompletionFieldConfig
                                                        fieldConfigurations={
                                                            completionBonusConfigurations.fieldConfigurations
                                                        }
                                                    />
                                                ) : (
                                                    <div className="text-danger text-center font-weight-bold rounded p-2 invalid-profile-completion-configs">
                                                        Invalid configurations
                                                        or configurations not
                                                        found!
                                                    </div>
                                                )
                                            }
                                        />
                                    </div>
                                </div>
                            }
                        />
                    </div>
                )}
            </Modal.Body>
            <Modal.Footer className="mt-4">
                <Button size="sm" variant="primary" onClick={onHide}>
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

NonTransactionalDetails.defaultProps = {
    show: false,
    selectedPointRule: {},
    onHide: () => {},
};

NonTransactionalDetails.propTypes = {
    show: PropTypes.bool,
    selectedPointRule: PropTypes.object,
    onHide: PropTypes.func,
};

export default NonTransactionalDetails;
