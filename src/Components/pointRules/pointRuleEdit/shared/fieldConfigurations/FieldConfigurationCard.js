import React, { useCallback, useMemo, useState } from "react";
import PropTypes from "prop-types";
import {
    Button,
    Card,
    Form,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faTimes } from "FaICIconMap";

const FieldConfigurationCard = ({
    validated,
    disabled,
    defaultLimit,
    fieldConfigIndex,
    fieldConfigId,
    skip,
    memberAttributes,
    selectedFieldConfig,
    fieldConfigurations,
    data,
    fieldConfigValidation,
    removeFieldConfig,
    setSelectedFieldConfig,
    setFieldConfigPoints,
    setSkip,
}) => {
    const [points, setPoints] = useState(selectedFieldConfig[0]?.points || "");
    const [confirmPoints, setConfirmPoints] = useState(false);
    const unselectedMemberAttributes = useMemo(() => {
        const configuredFieldNames = fieldConfigurations.map(
            (fieldConfig) => fieldConfig.fieldName
        );
        return memberAttributes.filter(
            (memberAttribute) =>
                !configuredFieldNames.includes(memberAttribute?.value)
        );
    }, [memberAttributes, fieldConfigurations]);

    const onSetSelectedFieldConfig = useCallback(
        (data) => setSelectedFieldConfig(fieldConfigIndex, data),
        [fieldConfigIndex, setSelectedFieldConfig]
    );

    const onChangeFieldConfigPoints = useCallback(
        (e) => {
            setPoints(e.currentTarget.value);
            setConfirmPoints(
                selectedFieldConfig[0]?.fieldName &&
                    selectedFieldConfig[0]?.points !== e.currentTarget.value
            );
        },
        [selectedFieldConfig, setPoints, setConfirmPoints]
    );

    const onConfirmFieldConfigPoints = useCallback(
        (e) => setFieldConfigPoints(fieldConfigIndex, points),
        [fieldConfigIndex, points, setFieldConfigPoints]
    );

    const onRemoveFieldConfig = useCallback(() => {
        if (
            fieldConfigurations.length % defaultLimit === 1 &&
            data.length === 1
        ) {
            setSkip(skip - 1);
        }
        removeFieldConfig(fieldConfigIndex);
    }, [
        fieldConfigurations.length,
        defaultLimit,
        data.length,
        removeFieldConfig,
        fieldConfigIndex,
        setSkip,
        skip,
    ]);

    return (
        <Card
            key={fieldConfigId || fieldConfigIndex}
            className="field-config-card my-3 p-2"
        >
            <div className="font-weight-bold d-flex justify-content-between align-items-center">
                <div className="mx-3">
                    {fieldConfigIndex + 1}.
                    {selectedFieldConfig?.length !== 0 &&
                    selectedFieldConfig[0]?.fieldName
                        ? ` "${selectedFieldConfig[0]?.fieldName}" `
                        : " <empty> "}
                    Field Configuration
                </div>
                <Button
                    className="px-3 py-0 shadow-none text-danger"
                    variant="link"
                    disabled={disabled}
                    onClick={onRemoveFieldConfig}
                >
                    <IcIcon icon={faTimes} size="2x" />
                </Button>
            </div>
            <Card.Body>
                <div className="d-flex justify-content-between">
                    <Form.Group
                        controlId={fieldConfigId || fieldConfigIndex}
                        className="w-100 mb-0 mr-2"
                    >
                        <Form.Label>
                            Field Name
                            <span className="ml-1 text-danger">*</span>
                        </Form.Label>
                        <Form.Select
                            labelKey="fieldName"
                            options={unselectedMemberAttributes || []}
                            placeholder="Select a member attribute..."
                            selected={memberAttributes.filter(
                                (mA) =>
                                    mA?.value ===
                                    selectedFieldConfig[0]?.fieldName
                            )}
                            disabled={disabled}
                            onChange={onSetSelectedFieldConfig}
                            required
                        />
                        {validated &&
                            fieldConfigValidation?.emptyFieldNameMsg && (
                                <Form.Text className="text-danger">
                                    {`* ${fieldConfigValidation.emptyFieldNameMsg}`}
                                </Form.Text>
                            )}
                    </Form.Group>
                    <Form.Group
                        controlId={fieldConfigId || fieldConfigIndex}
                        className="w-100 mb-0 ml-2"
                    >
                        <Form.Label className="d-flex justify-content-between align-items-center">
                            <div>
                                Points
                                <span className="ml-1 text-danger">*</span>
                            </div>
                            {confirmPoints && (
                                <Button
                                    className="ml-2 p-0 shadow-none"
                                    variant="link"
                                    size="sm"
                                    onClick={onConfirmFieldConfigPoints}
                                >
                                    Confirm Points
                                </Button>
                            )}
                        </Form.Label>
                        <Form.Control
                            type="number"
                            name="pointAmount"
                            value={points}
                            disabled={
                                disabled || !selectedFieldConfig[0]?.fieldName
                            }
                            onChange={onChangeFieldConfigPoints}
                            placeholder={
                                !selectedFieldConfig[0]?.fieldName
                                    ? "Please select a field name first."
                                    : `Enter points amount${
                                        selectedFieldConfig[0]?.fieldName
                                            ? " for '" +
                                            selectedFieldConfig[0]
                                                .fieldName +
                                            "'"
                                            : ""
                                    }...`
                            }
                            min={1}
                            required
                        />
                        {confirmPoints && (
                            <Form.Text className="text-info font-weight-bold">
                                * Please confirm points amount.
                            </Form.Text>
                        )}
                        {validated &&
                            selectedFieldConfig[0]?.fieldName &&
                            !confirmPoints &&
                            fieldConfigValidation?.emptyPointsMsg && (
                                <Form.Text className="text-danger">
                                    {`* ${fieldConfigValidation.emptyPointsMsg}`}
                                </Form.Text>
                            )}
                    </Form.Group>
                </div>
            </Card.Body>
        </Card>
    );
};

FieldConfigurationCard.defaultProps = {
    validated: false,
    disabled: false,
    defaultLimit: 3,
    memberAttributes: [],
    selectedFieldConfig: [],
    fieldConfigurations: [],
    data: [],
    fieldConfigValidation: {},
    removeFieldConfig: () => {},
    setSelectedFieldConfig: () => {},
    setFieldConfigPoints: () => {},
    setSkip: () => {},
};

FieldConfigurationCard.propTypes = {
    validated: PropTypes.bool,
    disabled: PropTypes.bool,
    defaultLimit: PropTypes.number,
    fieldConfigIndex: PropTypes.number.isRequired,
    fieldConfigId: PropTypes.string.isRequired,
    skip: PropTypes.number.isRequired,
    memberAttributes: PropTypes.array,
    selectedFieldConfig: PropTypes.array,
    fieldConfigurations: PropTypes.array,
    data: PropTypes.array,
    fieldConfigValidation: PropTypes.object,
    removeFieldConfig: PropTypes.func,
    setSelectedFieldConfig: PropTypes.func,
    setFieldConfigPoints: PropTypes.func,
    setSkip: PropTypes.func,
};

export default FieldConfigurationCard;
