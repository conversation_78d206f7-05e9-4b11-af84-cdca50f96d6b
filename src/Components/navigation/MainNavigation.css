.top-nav-bar {
    z-index: 1;
    border-radius: 0 !important;
}

.top-nav-bar .nav-components {
    margin-top: 10px;
    padding: 5px;
}

.top-nav-bar .module-link {
    color: #ffffff;
}

.top-nav-bar .module-link:hover {
    color: inherit;
}

.top-nav-bar .module-link.active-link {
    background-color: #6AB3E3;
}

.top-nav-bar a.nav-link.active {
    color: #000000;
    background-color: rgba(0, 0, 0, 0);
    border: none;
    font-weight: 600;
}

.top-nav-bar .nav-link {
    padding: 0rem 1rem !important;
}

/* .main-nav-container {
    position: fixed !important;
    width: 100%;
    z-index: 100;
} */

.top-nav-bar .link-text {
    padding: 7px;
    font-size: 12.8px;
}

.navigation-dropdown{
    font-family: 'Poppins', sans-serif !important;
}

.border-bottom-navigation {
    border-bottom: 1px solid #00aeff38!important;
  }

.sidebar-popover {
    position: absolute !important;
    left: 1rem !important;
    background: rgb(255, 255, 255) !important;
    -webkit-border-radius:3px;
    -moz-border-radius:3px;
    border-radius:3px;
    min-width: 160px;
}

.sidebar-popover .popover-content {
    margin: 0;
    padding: 3px 0 3px 0;
}

.sidebar-popover.popover {
    margin-left: -27px !important;
}

.sidebar-popover.popover.right > .arrow:after {
    border-right-color: #556B76;
}

.flags-img {
    /* border: 1px solid #ddd;
    border-radius: 2px;
    padding: 1px 2px; */
    width: 20px;
    height: auto !important;
  }