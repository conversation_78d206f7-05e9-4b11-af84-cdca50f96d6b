//TODO: Temporarily disabled. Need to fix the test issue and re enable this test (Rename the file)
import React from 'react'
import {render, cleanup} from '@testing-library/react';
import { create } from "react-test-renderer";
import Navigation from './Navigation';
import {MemoryRouter} from 'react-router-dom';
import '@testing-library/jest-dom'
import App from '../../App'

afterEach(() => {
    cleanup();
});

describe('Side navigation component', () => {

    test('Render Navigation component', () => {

        render(
         
            <MemoryRouter>  
              <App>  
                <Navigation/> 
              </App> 
          </MemoryRouter>

          );
    })
});

describe('Navigation component snapshot', () => {
  test('Matches the snapshot', ()=> {


       const navigationComponent = create(<MemoryRouter>  
        <App>  
          <Navigation/> 
        </App> 
    </MemoryRouter>);
      expect(navigationComponent.toJSON()).toMatchSnapshot();

   });
});