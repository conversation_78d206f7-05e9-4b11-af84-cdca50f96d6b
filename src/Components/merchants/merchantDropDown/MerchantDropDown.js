import React from 'react';
import { Form } from '@shoutout-labs/shoutout-themes-enterprise';
import PropTypes from 'prop-types';

const MerchantDropDown = ({ onChangeSelect, selectOptions, placeHolder, selectedValue, isLoading }) => {
    return (
        <Form.Select
            id="basic-typeahead-single"
            labelKey="name"
            onChange={onChangeSelect}
            options={selectOptions}
            placeholder={placeHolder}
            selected={selectedValue}
            disabled={isLoading || !selectOptions}
        />
    );
};

MerchantDropDown.propTypes = {
    onChangeSelect : PropTypes.func, 
    selectOptions : PropTypes.array, 
    placeHolder : PropTypes.string,
    selectedValue : PropTypes.array
}

export default MerchantDropDown;