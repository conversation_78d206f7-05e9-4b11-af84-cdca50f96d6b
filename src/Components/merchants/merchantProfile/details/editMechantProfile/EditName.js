import React, { useCallback, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Button, Form, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { MerchantsContext } from "Contexts/merchantsContext";
import { isEmptyObject } from "Utils";
import { updateMerchant } from "Services";

const EditName = ({ show, onHide, currentDetails, merchantId }) => {
    const { onRefreshMerchants } = useContext(MerchantsContext);
    const [isUpdating, setIsUpdating] = useState(false);
    const [merchantName, setMerchantName] = useState(null);

    const onChangeMerchantName = useCallback(e => setMerchantName(e.target.value), []);

    const onSubmit = useCallback(async e => {
        e.preventDefault();
        try {
            const merchantNamePayload = { merchantName: merchantName };

            setIsUpdating(true);
            const updatedMerchant = await updateMerchant(merchantId, merchantNamePayload);
            onRefreshMerchants();
            setIsUpdating(false);
            toast.success("Successfully updated merchant's name.");
            onHide(null, updatedMerchant);
        } catch (e) {
            setIsUpdating(false);
            toast.error(e.message || "Could not update merchant's name! Please try again.");
        }
    }, [merchantId, merchantName, onHide, setIsUpdating, onRefreshMerchants]);

    useEffect(() => {
        if (!isEmptyObject(currentDetails)) {
            setMerchantName(currentDetails.merchantName);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentDetails]);

    return (
        <Modal show={show} onHide={isUpdating ? () => {} : onHide} centered>
          <Modal.Header closeButton={!isUpdating}>
            <Modal.Title>Edit Merchant Name</Modal.Title>
          </Modal.Header>
          <Form onSubmit={onSubmit} noValidate>
            <Modal.Body>
                <Form.Group>
                    <Form.Label>Merchant Name</Form.Label>
                    <Form.Control
                        type="text"
                        required
                        value={merchantName || ""}
                        disabled={isUpdating}
                        onChange={onChangeMerchantName}
                        placeholder="Enter Merchant Name"
                    />
                </Form.Group>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={onHide}
                    type="button"
                    disabled={isUpdating}
                >
                    Cancel
                </Button>
                <Button
                    size="sm"
                    variant="primary"
                    type="submit"
                    disabled={
                        isUpdating ||
                        merchantName === "" ||
                        merchantName === currentDetails.merchantName
                    }
                >
                    {isUpdating ? "Updating..." : "Update"}
                </Button>
            </Modal.Footer>
          </Form>
        </Modal>
      );
};

EditName.propTypes = {
    /**
     * Show edit view
     */
    show: PropTypes.bool.isRequired,
    /**
     * Callback on close
     */
    onHide: PropTypes.func.isRequired,
    /**
     * Current known data
     */
    currentDetails: PropTypes.object,
    /**
     * Merchant Id
     */
    merchantId: PropTypes.string.isRequired,
};

export default EditName;
