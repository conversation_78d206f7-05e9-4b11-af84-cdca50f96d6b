import React, {
    use<PERSON>allback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { useParams, useHistory } from "react-router-dom";
import { toast } from "react-toastify";
import {
    <PERSON><PERSON>,
    Heading,
    Tabs,
    Tab,
    IcIcon,
    Badge,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faAngleLeftB,
    faChartBar,
    faMapMarkerAlt,
    faPhone,
    faUsdSquare,
    faUsers,
} from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    MerchantProfileTabs,
    AccessPermissionModuleNames,
    AccessPermissionModules,
} from "Data";
import { getMerchantById } from "Services";
import BaseLayout from "Layout/BaseLayout";
import MerchantProfileDetails from "./details/MerchantProfileDetails";
import Transactions from "./tabviews/transactions/Transactions";
import Locations from "./tabviews/locations/Locations";
import Users from "./tabviews/users/Users";
import ContactDetails from "./tabviews/contactDetails/ContactDetails";
import Reports from "./tabviews/reports/Reports";

const MerchantProfile = () => {
    const { id: merchantId } = useParams();
    const { organization, selectedRegion, isAuthorizedForAction } =
        useContext(UserContext);
    const [tabIsLoading, setTabIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState(
        MerchantProfileTabs.TRANSACTIONS
    );
    const [merchantDetails, setMerchantDetails] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingLocations, setIsLoadingLocations] = useState(false);
    const [isLocationCountLoading, setIsLocationCountLoading] = useState(false);
    const history = useHistory();

    const isDefaultMerchant = useMemo(
        () => selectedRegion?.defaultMerchantId === merchantId,
        [selectedRegion?.defaultMerchantId, merchantId]
    );

    const isAllowToUpdateMerchant = useMemo(
        () =>
            isAuthorizedForAction(
                AccessPermissionModuleNames.MERCHANT,
                AccessPermissionModules[AccessPermissionModuleNames.MERCHANT]
                    .actions.UpdateMerchant
            ),
        [isAuthorizedForAction]
    );

    const loadMerchantProfile = useCallback(
        async (merchantId) => {
            try {
                setIsLoading(true);
                const response = await getMerchantById(merchantId);
                setMerchantDetails(response);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load merchant profile!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setIsLoading(false);
            }
        },
        [setIsLoading, setMerchantDetails]
    );

    const onNavigatingBack = useCallback(
        () => history.push("/merchants", "reloadMerchants"),
        [history]
    );

    useEffect(() => {
        if (merchantId) {
            loadMerchantProfile(merchantId);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [merchantId, organization, selectedRegion]);

    return (
        <BaseLayout
            containerClassName="user-profile"
            topLeft={
                <div className="d-flex align-items-center">
                    <Heading text="Merchant Profile" />
                    {isDefaultMerchant && (
                        <Badge
                            className="d-flex align-items-center mx-3 py-1 px-3"
                            variant="default"
                        >
                            <h5 className="p-0 m-0">Default Merchant</h5>
                        </Badge>
                    )}
                </div>
            }
            topRight={
                <div className="d-flex align-items-center">
                    <Button
                        className="btn shadow-none"
                        variant="link"
                        disabled={isLoading}
                        onClick={onNavigatingBack}
                    >
                        <div className="d-flex align-items-center">
                            <IcIcon
                                icon={faAngleLeftB}
                                size="lg"
                                className="mr-1"
                            />
                            Back
                        </div>
                    </Button>
                </div>
            }
            bottom={
                <>
                    <MerchantProfileDetails
                        regionId={selectedRegion._id}
                        merchantId={merchantId}
                        isLoading={isLoading}
                        isDefaultMerchant={isDefaultMerchant}
                        merchantDetails={merchantDetails}
                        setMerchantDetails={setMerchantDetails}
                        regions={organization.regions}
                        isAllowToUpdateMerchant={isAllowToUpdateMerchant}
                        isLoadingLocations={isLoadingLocations}
                        setActiveTab={setActiveTab}
                        isLocationCountLoading={isLocationCountLoading}
                    />

                    <Tabs
                        defaultActiveKey={MerchantProfileTabs.TRANSACTIONS}
                        transition={false}
                        id="noanim-tab-example"
                        activeKey={activeTab}
                        onSelect={setActiveTab}
                        className="mt-3"
                    >
                        <Tab
                            eventKey={MerchantProfileTabs.TRANSACTIONS}
                            title={
                                <div className="d-flex flex-row align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faUsdSquare}
                                    />
                                    <span className="mr-2">Transactions</span>
                                </div>
                            }
                            disabled={isLoading || tabIsLoading}
                        >
                            {activeTab === MerchantProfileTabs.TRANSACTIONS && (
                                <Transactions
                                    regionId={selectedRegion._id}
                                    merchantId={merchantId}
                                    setTabIsLoading={setTabIsLoading}
                                />
                            )}
                        </Tab>
                        <Tab
                            eventKey={MerchantProfileTabs.LOCATIONS}
                            title={
                                <div className="d-flex flex-row align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faMapMarkerAlt}
                                    />
                                    <span className="mr-2">Locations</span>
                                </div>
                            }
                            disabled={isLoading || tabIsLoading}
                        >
                            {(isAuthorizedForAction(
                                AccessPermissionModuleNames.LOCATION,
                                AccessPermissionModules[
                                    AccessPermissionModuleNames.LOCATION
                                ].actions.ListLocations
                            ) ||
                                isAuthorizedForAction(
                                    AccessPermissionModuleNames.LOCATION,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.LOCATION
                                    ].actions.CreateLocation
                                )) &&
                                activeTab === MerchantProfileTabs.LOCATIONS && (
                                    <Locations
                                        regionId={selectedRegion._id}
                                        options={merchantDetails?.options}
                                        merchantId={merchantId}
                                        merchantCountryDetails={{
                                            countryISO2Code:
                                                merchantDetails?.countryISO2Code,
                                            countryName:
                                                merchantDetails?.countryName,
                                        }}
                                        isLoading={isLoadingLocations}
                                        setIsLoading={setIsLoadingLocations}
                                        setIsLocationCountLoading={
                                            setIsLocationCountLoading
                                        }
                                        setMerchantDetails={setMerchantDetails}
                                        setTabIsLoading={setTabIsLoading}
                                    />
                                )}
                        </Tab>
                        <Tab
                            eventKey={MerchantProfileTabs.USERS}
                            title={
                                <div className="d-flex flex-row align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faUsers}
                                    />
                                    <span className="mr-2">Users</span>
                                </div>
                            }
                            disabled={isLoading || tabIsLoading}
                        >
                            {activeTab === MerchantProfileTabs.USERS && (
                                <Users
                                    currentMerchant={merchantDetails}
                                    setTabIsLoading={setTabIsLoading}
                                />
                            )}
                        </Tab>
                        <Tab
                            eventKey={MerchantProfileTabs.CONTACT_DETAILS}
                            title={
                                <div className="d-flex flex-row align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faPhone}
                                    />
                                    <span className="mr-2">
                                        Contact Details
                                    </span>
                                </div>
                            }
                            disabled={isLoading || tabIsLoading}
                        >
                            {activeTab ===
                                MerchantProfileTabs.CONTACT_DETAILS &&
                                merchantDetails && (
                                    <ContactDetails
                                        regionId={selectedRegion._id}
                                        merchantDetails={merchantDetails}
                                        loadMerchantProfile={
                                            loadMerchantProfile
                                        }
                                        isLoading={isLoading}
                                        setTabIsLoading={setTabIsLoading}
                                    />
                                )}
                        </Tab>
                        <Tab
                            eventKey={MerchantProfileTabs.REPORT}
                            title={
                                <div className="d-flex flex-row align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faChartBar}
                                    />
                                    <span className="mr-2">Reports</span>
                                </div>
                            }
                            disabled={isLoading || tabIsLoading}
                        >
                            {activeTab === MerchantProfileTabs.REPORT && (
                                <Reports
                                    regionId={selectedRegion._id}
                                    merchantId={merchantId}
                                    setTabIsLoading={setTabIsLoading}
                                />
                            )}
                        </Tab>
                    </Tabs>
                </>
            }
        />
    );
};

export default MerchantProfile;
