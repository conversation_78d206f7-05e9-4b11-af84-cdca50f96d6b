import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    Card,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faFilter, faFilterSlash, faSync } from "FaICIconMap";
import { DataContext } from "Contexts";
import { MerchantTransactionFilter, MerchantTransactionType } from "Data";
import { useToggle } from "Hooks";
import { getTransactions, getTransactionsCount } from "Services";
import BaseLayout from "Layout/BaseLayout";
import TransactionsTable from "./TransactionsTable";
import TransactionsDropDown from "./transactionsDropDown/TransactionsDropDown";

const defaultLimit = 10,
    defaultSkip = 1;

const Transactions = ({ regionId, merchantId, setTabIsLoading }) => {
    const { merchantLocations } = useContext(DataContext);
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [isLoading, setIsLoading] = useState(false);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [transactionsList, setTransactionsList] = useState([]);
    const [totalTransactions, setTotalTransactions] = useState(0);
    const [selectFilter, setSelectFilter] = useState();
    const [selectedFilter, setSelectedFilter] = useState();
    const [filterValues, setFilterValues] = useState();
    const [isSet, setIsSet] = useState(false);
    const [appliedFilters, setAppliedFilters] = useState();
    const [isApplied, setIsApplied] = useState(false);
    const [isReloading, setIsReloading] = useState(false);

    const currentMerchantLocations = useMemo(
        () =>
            Object.values(merchantLocations[merchantId] || {}).map(
                (location) => ({
                    value: location._id,
                    name: location.locationName,
                })
            ),
        [merchantId, merchantLocations]
    );

    const loadTransactions = useCallback(
        async ({ limit, skip }, filters, reloadCount = true) => {
            let queryObj = {
                merchantId: merchantId,
                regionId: regionId,
            };

            try {
                setTabIsLoading(true);
                setIsLoading(true);
                if (filters) {
                    queryObj = { ...queryObj, ...filters };
                }
                const promises = [
                    (async () => {
                        const transactions = await getTransactions(
                            queryObj,
                            limit,
                            (skip - 1) * limit
                        );
                        setTransactionsList(transactions.items);
                        return Promise.resolve();
                    })(),
                ];
                if (reloadCount) {
                    promises.push(
                        (async () => {
                            const transactionCountResponse =
                                await getTransactionsCount(queryObj);
                            setTotalTransactions(
                                transactionCountResponse.count
                            );
                            return Promise.resolve();
                        })()
                    );
                }
                await Promise.all(promises);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load merchant's transactions!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setIsLoading(false);
                setTabIsLoading(false);
            }
        },
        [
            regionId,
            merchantId,
            setTabIsLoading,
            setIsLoading,
            setTransactionsList,
            setTotalTransactions,
        ]
    );

    const onFilterBy = useCallback(async () => {
        toggleShowFilters();
        if ((selectFilter || selectedFilter) && !isApplied && !appliedFilters) {
            setSelectFilter();
            setSelectedFilter();
            setIsSet(false);
        }
    }, [
        toggleShowFilters,
        selectFilter,
        selectedFilter,
        isApplied,
        appliedFilters,
    ]);

    const onSetFilterType = useCallback(
        (e) => {
            setSelectFilter(e);
            if (e[0].value === "TRANSACTION_TYPE") {
                setFilterValues(MerchantTransactionType);
            }
            if (e[0].value === "LOCATION") {
                setSelectedFilter();
                setFilterValues(currentMerchantLocations);
            }
            setIsApplied(false);
            setSelectedFilter();
            setIsSet(false);
        },
        [currentMerchantLocations, setSelectFilter]
    );

    const onSetFilter = useCallback(
        (e) => {
            setSelectedFilter(e);
            setIsSet(true);
            if (appliedFilters && selectFilter[0].value) {
                switch (selectFilter[0].value) {
                    case "TRANSACTION_TYPE":
                        setIsApplied(
                            e[0].value === appliedFilters.transactionType
                        );
                        break;
                    case "LOCATION":
                        setIsApplied(
                            e[0].value === appliedFilters.merchantLocationId
                        );
                        break;
                    default:
                        break;
                }
            }
        },
        [selectFilter, appliedFilters, setSelectedFilter, setIsSet]
    );

    const applyFilter = useCallback(() => {
        if (selectFilter[0].value === "TRANSACTION_TYPE") {
            setAppliedFilters({ transactionType: selectedFilter[0].value });
        } else if (selectFilter[0].value === "LOCATION") {
            setAppliedFilters({ merchantLocationId: selectedFilter[0].value });
        }
        setIsApplied(true);
        setSkip(defaultSkip);
    }, [
        selectFilter,
        selectedFilter,
        setAppliedFilters,
        setIsApplied,
        setSkip,
    ]);

    const resetFilter = useCallback(() => {
        setSelectFilter();
        setSelectedFilter();
        setAppliedFilters();
        setIsSet(false);
        setIsApplied(false);
        setSkip(defaultSkip);
    }, [
        setSelectFilter,
        setSelectedFilter,
        setAppliedFilters,
        setIsSet,
        setIsApplied,
        setSkip,
    ]);

    const reloadTransactions = useCallback(async () => {
        setIsReloading(true);
        await loadTransactions(
            {
                limit,
                skip: defaultSkip,
            },
            appliedFilters,
            true
        );
        setSkip(defaultSkip);
        setIsReloading(false);
    }, [limit, appliedFilters, setIsReloading, loadTransactions, setSkip]);

    useEffect(() => {
        if (merchantId) {
            loadTransactions(
                {
                    limit,
                    skip: defaultSkip,
                },
                appliedFilters,
                true
            );
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [merchantId, appliedFilters]);

    return (
        <BaseLayout
            bottom={
                <div className="w-100 mt-1">
                    <div className="d-flex justify-content-between my-4">
                        <div className="d-flex align-items-center">
                            <Button
                                variant="link"
                                size="sm"
                                disabled={isLoading || isReloading}
                                onClick={reloadTransactions}
                            >
                                {!isReloading && (
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            size="md"
                                            className="mr-2"
                                            icon={faSync}
                                        />
                                        Reload Transactions
                                    </div>
                                )}
                            </Button>
                            <small>
                                {isReloading && (
                                    <div className="text-primary">
                                        Reloading...
                                    </div>
                                )}
                            </small>
                        </div>
                        <div>
                            <Button
                                variant={`${
                                    !showFilters ? "outline-" : ""
                                }primary`}
                                size="sm"
                                disabled={isLoading || isReloading}
                                onClick={onFilterBy}
                            >
                                <IcIcon
                                    className="mr-2"
                                    size="lg"
                                    icon={
                                        showFilters ? faFilterSlash : faFilter
                                    }
                                />
                                {showFilters ? "Hide Filters" : "Filter By"}
                            </Button>
                        </div>
                    </div>
                    {showFilters && (
                        <Card>
                            <Card.Body>
                                <div className="d-flex align-items-center my-2">
                                    <div className="mx-3">
                                        <TransactionsDropDown
                                            onChangeSelect={onSetFilterType}
                                            selectOptions={
                                                MerchantTransactionFilter
                                            }
                                            placeHolder="Select Filter"
                                            selectedValue={selectFilter}
                                            isLoading={isLoading}
                                            isApplied={isApplied}
                                            appliedFilters={appliedFilters}
                                        />
                                    </div>
                                    {selectFilter && (
                                        <>
                                            is
                                            <div className="mx-3 w-25">
                                                <TransactionsDropDown
                                                    onChangeSelect={onSetFilter}
                                                    selectOptions={filterValues}
                                                    placeHolder={selectFilter}
                                                    selectedValue={
                                                        selectedFilter
                                                    }
                                                    isLoading={isLoading}
                                                />
                                            </div>
                                        </>
                                    )}
                                    <div className="mx-3">
                                        <Button
                                            variant="outline-primary "
                                            size="sm"
                                            className="mx-2"
                                            disabled={
                                                !isSet || isLoading || isApplied
                                            }
                                            onClick={applyFilter}
                                        >
                                            {isLoading &&
                                            isApplied &&
                                            !isReloading
                                                ? "Applying..."
                                                : "Apply Filter"}
                                        </Button>
                                        <Button
                                            variant="outline-danger "
                                            size="sm"
                                            className="mx-2"
                                            disabled={!isApplied || isLoading}
                                            onClick={resetFilter}
                                        >
                                            {isLoading &&
                                            !isApplied &&
                                            !isReloading
                                                ? "Clearing..."
                                                : "Clear Filter"}
                                        </Button>
                                    </div>
                                </div>
                            </Card.Body>
                        </Card>
                    )}
                    <div className="mt-4">
                        <div className="w-100">
                            <TransactionsTable
                                transactionsList={transactionsList}
                                totalCount={totalTransactions}
                                limit={limit}
                                skip={skip}
                                appliedFilters={appliedFilters}
                                setLimit={setLimit}
                                setSkip={setSkip}
                                loadTransactions={loadTransactions}
                                isLoading={isLoading}
                            />
                        </div>
                    </div>
                </div>
            }
        />
    );
};

Transactions.defaultProps = {
    regionId: "",
    merchantId: "",
    setTabIsLoading: () => {},
};

Transactions.propTypes = {
    regionId: PropTypes.string,
    merchantId: PropTypes.string,
    setTabIsLoading: PropTypes.func,
};

export default Transactions;
