import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import paginationFactory, {
    PaginationProvider,
} from "react-bootstrap-table2-paginator";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import PropTypes from "prop-types";
import { BootstrapTable } from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faCalendar,
    faCoins,
    faCreditCard,
    faMap,
    faObjectGroup,
} from "FaICIconMap";
import { TransactionTypeColorCode } from "Data";
import { formatToCommonReadableFormat, toTitleCase } from "Utils";
import TransactionDetails from "Components/transactions/transactionHistory/TransactionDetails";
import { BootstrapTableOverlay } from "Components/utils/UtilComponents";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import SizePerPageRenderer from "Components/utils/table/sizePerPageRenderer/SizePerPageRenderer";
import NameIconTemplateForCamelCase from "Components/utils/table/NameIconTemplateForCamelCase";

const defaultSkip = 1;

const defaultColumnTemplate = ({ name, icon, ...rest }) => ({
    dataField: name,
    text: NameIconTemplateForCamelCase({ name, icon }),
    sort: false,
    ...rest,
});

const defaultCols = [
    { name: "cardNo", headerStyle: { width: "18%" }, icon: faCreditCard },
    { name: "transactionOn", headerStyle: { width: "20%" }, icon: faCalendar },
    { name: "points", headerStyle: { width: "10%" }, icon: faCoins },
    { name: "type", icon: faObjectGroup },
    { name: "location", icon: faMap },
];

const NoData = ({ loading }) =>
    loading ? null : <div>No transactions found for this merchant.</div>;

const TransactionsTable = ({
    transactionsList,
    totalCount,
    limit,
    skip,
    appliedFilters,
    setLimit,
    setSkip,
    loadTransactions,
    isLoading,
}) => {
    const [selectedTransaction, setSelectedTransaction] = useState({});
    const [showTransactionDetails, setShowTransactionDetails] = useState(false);

    const columns = useMemo(() => {
        const columns = [];

        defaultCols.forEach((item) => {
            columns.push(defaultColumnTemplate(item));
        });

        return columns;
    }, []);

    const data = useMemo(
        () =>
            transactionsList.map((transaction) => ({
                id: transaction?._id,
                transactionOn: transaction?.transactionOn ? (
                    formatToCommonReadableFormat(transaction.transactionOn)
                ) : (
                    <>
                        {applyBadgeStyling({
                            customValue: "Transaction date not found.",
                        })}
                    </>
                ),
                createdOn: transaction?.transactionOn ? (
                    formatToCommonReadableFormat(transaction.transactionOn)
                ) : (
                    <>
                        {applyBadgeStyling({
                            customValue: "Created date not found.",
                        })}
                    </>
                ),
                points: transaction.redeemablePoints,
                type: applyBadgeStyling({
                    text: toTitleCase(transaction?.type || ""),
                    variant: TransactionTypeColorCode[transaction?.type || ""],
                }),
                merchantText: transaction?.merchant?.merchantName,
                merchantLocationText:
                    transaction?.merchantLocation?.locationName,
                merchant:
                    transaction?.merchant?.merchantName ||
                    applyBadgeStyling({
                        customValue: "Merchant not found.",
                    }),
                location:
                    transaction?.merchantLocation?.locationName ||
                    applyBadgeStyling({
                        customValue: "Location not found.",
                    }),
                purchaseItems: transaction?.productItems,
                invoiceId: transaction?.invoiceData?.invoiceId,
                subType:
                    transaction?.transactionSubType?.name ||
                    transaction?.transactionSubTypeId ||
                    "",
                cardNoText: transaction?.cardNo,
                cardNo:
                    transaction?.cardNo ||
                    applyBadgeStyling({
                        customValue: "Card number not found.",
                    }),
                transactionAmount: transaction?.transactionAmount,
                memberId: transaction?.memberId,
                data: transaction,
            })),
        [transactionsList]
    );

    const hideDetailsModal = useCallback(
        (e) => {
            setShowTransactionDetails(false);
            setSelectedTransaction({});
        },
        [setShowTransactionDetails, setSelectedTransaction]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadTransactions(
                { limit: limit, skip: newSkip },
                appliedFilters,
                false
            );
        },
        [appliedFilters, limit, setSkip, loadTransactions]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadTransactions(
                { limit: newLimit, skip: defaultSkip },
                appliedFilters,
                false
            );
        },
        [appliedFilters, setLimit, setSkip, loadTransactions]
    );

    const onRowClick = useCallback(
        (rowData) => {
            setSelectedTransaction(rowData);
            setShowTransactionDetails(true);
        },
        [setShowTransactionDetails, setSelectedTransaction]
    );

    const tableRowEvents = {
        onClick: (e, row) => {
            onRowClick(row);
        },
    };

    const options = {
        page: skip,
        sizePerPage: limit,
        totalSize: totalCount,
        pageStartIndex: 1,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageList: [
            { text: "10", value: 10 },
            { text: "25", value: 25 },
            { text: "50", value: 50 },
        ],
        sizePerPageRenderer: SizePerPageRenderer,
        onPageChange: onChangePagination,
        onSizePerPageChange: onChangePageSize,
    };

    return (
        <div className="h-100">
            <PaginationProvider
                pagination={paginationFactory(options)}
                keyField="id"
                columns={columns}
                data={data}
            >
                {({ paginationTableProps }) => (
                    <ToolkitProvider
                        keyField="id"
                        data={data}
                        columns={columns}
                        columnToggle
                    >
                        {(props) => (
                            <div>
                                <BootstrapTable
                                    {...paginationTableProps}
                                    remote={{ search: true, pagination: true }}
                                    loading={isLoading}
                                    rowEvents={tableRowEvents}
                                    noDataIndication={
                                        <NoData loading={isLoading} />
                                    }
                                    overlay={BootstrapTableOverlay}
                                    {...props.baseProps}
                                />
                            </div>
                        )}
                    </ToolkitProvider>
                )}
            </PaginationProvider>
            {showTransactionDetails && (
                <TransactionDetails
                    showModel={showTransactionDetails}
                    hideModal={hideDetailsModal}
                    selectedTransaction={selectedTransaction}
                />
            )}
        </div>
    );
};

TransactionsTable.propTypes = {
    /**
     * Transaction data
     */
    transactionsList: PropTypes.array.isRequired,
    /**
     * Data limit
     */
    limit: PropTypes.number,
    /**
     * Skip
     */
    skip: PropTypes.number,
    /**
     * Set data limit
     */
    setLimit: PropTypes.func,
    /**
     * Set skip
     */
    setSkip: PropTypes.func,
    /**
     * Reload transaction data
     */
    loadTransactions: PropTypes.func,
    /**
     * State of data
     */
    isLoading: PropTypes.bool.isRequired,
};

export default TransactionsTable;
