import React from "react";
import PropTypes from "prop-types";
import { Form } from "@shoutout-labs/shoutout-themes-enterprise";

const TransactionsDropDown = ({
    onChangeSelect,
    selectOptions,
    placeHolder,
    selectedValue,
    isLoading,
    isApplied,
    appliedFilters,
}) => {
    return (
        <>
            {placeHolder ? (
                placeHolder[0].value === "TRANSACTION_TYPE" ? (
                    <Form.Select
                        id="basic-typeahead-single"
                        labelKey="name"
                        onChange={onChangeSelect}
                        options={selectOptions}
                        placeholder="Select Type"
                        selected={selectedValue}
                        disabled={isLoading || isApplied || appliedFilters}
                    />
                ) : placeHolder[0].value === "LOCATION" ? (
                    <Form.Select
                        id="basic-typeahead-single"
                        labelKey="name"
                        onChange={onChangeSelect}
                        options={selectOptions}
                        placeholder={
                            selectOptions && selectOptions.length === 0
                                ? "No locations found"
                                : "Select Location"
                        }
                        selected={selectedValue}
                        disabled={
                            isLoading ||
                            !selectOptions ||
                            (selectOptions && selectOptions.length === 0)
                        }
                    />
                ) : (
                    <Form.Select
                        id="basic-typeahead-single"
                        labelKey="name"
                        onChange={onChangeSelect}
                        options={selectOptions}
                        placeholder={placeHolder}
                        selected={selectedValue}
                        disabled={isLoading || isApplied || appliedFilters}
                    />
                )
            ) : null}
        </>
    );
};

TransactionsDropDown.propTypes = {
    onChangeSelect: PropTypes.func,
    selectOptions: PropTypes.array,
    placeHolder: PropTypes.string,
    selectedValue: PropTypes.array,
};

export default TransactionsDropDown;
