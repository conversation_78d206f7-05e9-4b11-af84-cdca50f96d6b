import React, { useState, useCallback, useEffect, useContext, useMemo, useRef } from "react";
import { toast } from 'react-toastify';
import PropTypes from "prop-types";
import {Button,  Modal, Form, Col, Row} from '@shoutout-labs/shoutout-themes-enterprise';
import { UserContext } from 'Contexts';
import { MerchantsContext } from 'Contexts/merchantsContext';
import { useToggle } from 'Hooks';
import { updateMerchant } from "Services";
import { isEmptyObject } from "Utils";

const EditContact = ({
    show,
    onHide,
    title,
    merchantId,
    loadContactDetails,
    contact,
    technicalContacts,
    billingContacts,
    contactType
}) => {
    const formRef = useRef();
    const { selectedRegion } = useContext(UserContext);
    const { onRefreshMerchants } = useContext(MerchantsContext);
    const [isSaving, toggleIsSaving] = useToggle(false);
    const [validatedMobileNumber, setValidatedMobileNumber] = useState(false);
    const [validated, setValidated] = useState(false);
    const [contactDetails, setContactDetails] = useState({
        name: "",
        email : "",
    });
    const [mobileNumber, setMobileNumber] = useState("");
    const [address, setAddress] = useState({
        line1: "",
        line2: "",
        line3 : "",
        city : "",
        stateOrProvince : "",
        zipOrPostcode : ""
    });

    const defaultCountryISO = useMemo(() => {
        return selectedRegion.defaultCountryISO2Code.toLowerCase();
      }, [selectedRegion]);

    const onChangeContactDetails = useCallback((e) => {

        setContactDetails({...contactDetails, [e.target.name] : e.target.value});

    },[
        contactDetails,
        setContactDetails
    ]);

    const onMobileNumberChange = useCallback((_status, _value, _countryData, _number, formattedNumber) => {
        setValidatedMobileNumber(_status);
        setMobileNumber(formattedNumber);
    }, [setValidatedMobileNumber,setMobileNumber]);

    const onChangeAddress = useCallback((e)=> {

        setAddress({...address, [e.target.name] : e.target.value})

    },[
        setAddress,
        address
    ]);

    const onHideDetailsModal = useCallback(()=> {

        onHide();
        setContactDetails({
            name: "",
            email : "",
            address : ""
        });
        setMobileNumber("");

    },[
        onHide,
        setContactDetails,
        setMobileNumber
    ])

    const editContact = useCallback(async()=> {
        const formValid = formRef.current.checkValidity()&&validatedMobileNumber;
        if(formValid){
            const contactPayload = {
                name : contactDetails.name,
                email : contactDetails.email,
                address : address,
                mobileNumber : mobileNumber
            }

            let payload;

            if(contactType === "tech"){

                const contactList = technicalContacts?.length > 0 && technicalContacts.filter(detail => detail._id !== contact._id);
                payload = {
                    technicalContacts : contactList.length > 0 ? [...contactList, contactPayload] : [contactPayload]
                }
            }else{

                const contactList = billingContacts?.length > 0 && billingContacts.filter(detail => detail._id !== contact._id);
                payload = {
                    billingContacts : contactList.length > 0 ? [...contactList,contactPayload ] : [contactPayload]
                }
            }

            try{

                toggleIsSaving();
                await updateMerchant(merchantId,  payload);
                onRefreshMerchants();
                toggleIsSaving();

                toast.success("Updated contact details successfully");

                onHideDetailsModal();
                loadContactDetails(merchantId);

            }catch (e){
                console.error(e);
                toast.error("Could not update contact details for this merchant!");
                toggleIsSaving();
            }
        }else {
            setValidated(true);
        }

    },[
        onHideDetailsModal,
        toggleIsSaving,
        loadContactDetails,
        merchantId,
        contactDetails,
        mobileNumber,
        address,
        billingContacts,
        technicalContacts,
        contactType,
        contact,
        onRefreshMerchants,
        validatedMobileNumber
    ]);

    useEffect(()=> {
        if(!isEmptyObject(contact)){
            setContactDetails({
                name : contact?.name,
                email : contact?.email
            });
            setAddress({
                line1: contact?.address?.line1,
                line2: contact?.address?.line2,
                line3 : contact?.address?.line3,
                city : contact?.address?.city,
                stateOrProvince : contact?.address?.stateOrProvince,
                zipOrPostcode : contact?.address?.zipOrPostcode
            });
            setMobileNumber(contact?.mobileNumber);
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[contact]);

    return(
        <>
            <Modal
                show={show}
                onHide={isSaving ? () => { /* Placeholder for function body */ } : onHide}
                size="md"
                backdrop={true}
                centered
            >
            <Modal.Header closeButton>
                <Modal.Title>
                   {title}
                </Modal.Title>
            </Modal.Header>
            <Modal.Body >
                <Form ref={formRef} validated={validated}>
                    <Form.Group>
                        <Form.Label>
                            Name
                        </Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter name"
                            value={contactDetails.name}
                            name="name"
                            required
                            onChange={onChangeContactDetails}
                        />
                    </Form.Group>
                    <Form.Group>
                        <Form.Label>
                            Email
                        </Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter email"
                            value={contactDetails.email}
                            name="email"
                            required
                            onChange={onChangeContactDetails}
                        />
                    </Form.Group>
                    <Form.Label>
                        Mobile Number
                    </Form.Label>
                    <div className="d-flex flex-column mb-4">
                        <Form.MobileNumberInput
                            defaultCountry={defaultCountryISO}
                            onPhoneNumberBlur={onMobileNumberChange}
                            onPhoneNumberChange={onMobileNumberChange}
                            format={true}
                            type="number"
                            formatOnInit={true}
                            defaultValue={contact?.mobileNumber}
                            validated={validated}
                        />

                    </div>
                    <Form.Label>
                        Address
                    </Form.Label>
                    <Row>
                        <Col>
                            <Form.Group>
                                <Form.Control
                                    type="text"
                                    name="line1"
                                    placeholder="Address Line One"
                                    value={address.line1}
                                    onChange={onChangeAddress}
                                />
                            </Form.Group>
                        </Col>
                        <Col>
                            <Form.Group>
                                <Form.Control
                                    type="text"
                                    name="line2"
                                    placeholder="Address Line Two"
                                    value={address.line2}
                                    onChange={onChangeAddress}
                                />
                            </Form.Group>
                        </Col>
                    </Row>
                    <Row>
                        <Col>
                            <Form.Group>
                                <Form.Control
                                    type="text"
                                    name="line3"
                                    placeholder="Address Line Three"
                                    value={address.line3}
                                    onChange={onChangeAddress}
                                />
                            </Form.Group>
                        </Col>
                    </Row>
                    <Form.Group>
                        <Form.Label>City</Form.Label>
                        <Form.Control
                            type="text"
                            name="city"
                            placeholder="Enter City"
                            value={address.city}
                            onChange={onChangeAddress}
                            required
                        />
                    </Form.Group>
                    <Form.Group>
                        <Form.Label>State/Province</Form.Label>
                        <Form.Control
                            type="text"
                            name="stateOrProvince"
                            placeholder="Enter State or Province"
                            value={address.stateOrProvince}
                            onChange={onChangeAddress}
                            required
                        />
                    </Form.Group>
                    <Form.Group>
                        <Form.Label>Zip/Post Code</Form.Label>
                        <Form.Control
                            type="text"
                            name="zipOrPostcode"
                            placeholder="Enter Zip Code or Post Code"
                            value={address.zipOrPostcode}
                            onChange={onChangeAddress}
                            required
                        />
                    </Form.Group>
                </Form>
            </Modal.Body>
            <Modal.Footer >
                <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={onHideDetailsModal}
                    disabled={isSaving}
                >
                    Cancel
                </Button>
                 <Button
                    size="sm"
                    variant="primary"
                    onClick={editContact}
                    disabled={isSaving}
                >
                    {isSaving?"Updating...":"Edit Contact Details"}
                </Button>
            </Modal.Footer>
        </Modal>
        </>
    )
}

EditContact.propTypes = {

    contact: PropTypes.object.isRequired,
    loadContactDetails : PropTypes.func,
    contactType : PropTypes.string,
    merchantId : PropTypes.string,
    title : PropTypes.string,
    show :  PropTypes.bool,
    onHide :  PropTypes.func,
    technicalContacts : PropTypes.array,
    billingContacts : PropTypes.array
};

export default  EditContact;
