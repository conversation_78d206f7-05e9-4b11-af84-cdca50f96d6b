import React, {useState, useCallback} from 'react';
import {<PERSON>, Button} from '@shoutout-labs/shoutout-themes-enterprise';
import PropTypes from "prop-types";
import EditContact from "./EditContact";

const ContactCard = ({
    contact,
    contactType,
    loadContactDetails,
    merchantId,
    technicalContact,
    billingContact
}) => {

  const [showEditModal, setShowEditModal] = useState(false);

  const onShowEditModal = useCallback(()=> {

        setShowEditModal(true);

  },[setShowEditModal]);

  const onHideEditModal = useCallback(()=> {

    setShowEditModal(false);

    },[setShowEditModal]);


    return (
        <>
            <Card className="py-3 mb-3">
                <div className="d-flex flex-row justify-content-between pt-2 px-3 mb-2">
                    <div className="d-flex flex-column mr-auto">
                        <div className="d-flex flex-row mr-auto">
                            <p className="font-weight-bold mb-0">
                                Name
                            </p>
                        </div>
                        <div>
                            <p className='my-0 text-muted'>
                                {contact.name}
                            </p>
                        </div>
                    </div>
                    <div className="d-flex flex-column mr-auto">
                        <div className="d-flex flex-row mr-auto">
                            <p className="font-weight-bold mb-0">
                                Address
                            </p>
                        </div>
                        <div>
                            <p className='my-0 text-muted'>
                                {contact?.address?.line1 &&  
                                    <span>{contact?.address?.line1},</span>
                                }
                                {contact?.address?.line2 &&  
                                    <span className="ml-1">{contact?.address?.line2},</span>
                                }
                                {contact?.address?.line3 &&  
                                    <span className="ml-1"> {contact?.address?.line3}</span>
                                }
                                <br/>
                                {contact?.address?.stateOrProvince &&  
                                    <span> {contact?.address?.stateOrProvince},</span>
                                }
                                {contact?.address?.zipOrPostcode &&  
                                    <span className="ml-1">{contact?.address?.zipOrPostcode},</span>
                                }
                            </p>
                        </div>
                    </div>
                    <div className="d-flex flex-column">
                        <Button
                            size="sm"
                            className="my-0 py-0"
                            variant="link"
                            onClick={onShowEditModal}
                        >
                            Edit
                        </Button>
                    </div>
                </div>
                <div className="d-flex flex-row justify-content-between pt-2 px-3 mb-2">
                    <div className="d-flex flex-column mr-auto">
                        <div className="d-flex flex-row mr-auto">
                            <p className="font-weight-bold mb-0">
                                Mobile Number
                            </p>
                        </div>
                        <div>
                            <p className='my-0 text-muted'>
                                {contact.mobileNumber}
                            </p>
                        </div>
                    </div>
                    <div className="d-flex flex-column mr-auto email-field">
                        <div className="d-flex flex-row mr-auto">
                            <p className="font-weight-bold mb-0">
                                Email
                            </p>
                        </div>
                        <div>
                            <p className='my-0 text-muted'>
                                {contact.email}
                            </p>
                        </div>
                    </div>
                </div>
            </Card> 
            <EditContact
                show = {showEditModal} 
                onHide = {onHideEditModal}
                title = {contactType === "tech" ? "Edit Technical - Contact Details" : "Edit Billing - Contact Details"}
                merchantId = {merchantId}
                loadContactDetails= {loadContactDetails}
                contact = {contact}
                technicalContact={technicalContact}
                billingContact ={billingContact}
                contactType={contactType}
            />
            </>      
            )
        }

ContactCard.propTypes = {
        
    contact: PropTypes.object.isRequired,
    loadContactDetails : PropTypes.func,
    contactType : PropTypes.string,
    merchantId : PropTypes.string,
    technicalContacts : PropTypes.array,
    billingContact : PropTypes.array
};

export default ContactCard;