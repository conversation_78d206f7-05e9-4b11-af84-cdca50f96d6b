import React, {
    use<PERSON><PERSON>back,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { useParams, useLocation, useHistory } from "react-router-dom";
import { toast } from "react-toastify";
import {
    Avatar,
    Badge,
    Button,
    Col,
    DropdownButton,
    DropdownItem,
    IcIcon,
    Heading,
    Row,
    SubHeading,
    Tab,
    Tabs,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faFileBookmarkAlt,
    faCoins,
    faEllipsisV,
    faEnvelope,
    faFile,
    faGift,
    faMedal,
    faPhone,
    faTachometerAlt,
    faUser,
    faImage,
    faPlus,
    faAngleLeftB,
    faHistory,
} from "FaICIconMap";
import { DataContext, MembersContext, UserContext } from "Contexts";
import {
    MemberStatus,
    AccessPermissionModules,
    AccessPermissionModuleNames,
    CardStatus,
    MemberTypes,
} from "Data";
import { useToggle } from "Hooks";
import { getAllCardsOfMember, getCharityById, updateCharity } from "Services";
import { formatToCommonReadableFormat, toTitleCase } from "Utils";
import BaseLayout from "Layout/BaseLayout";
import { LoadingComponent } from "../../utils/UtilComponents";
import { MemberReactivateContext } from "Components/members/memberProfile/memberReactivate/context/MemberReactivateContext";
import ActivityLog from "Components/members/memberProfile/tabViews/activityLog/ActivityLog";
import PointsPage from "Components/members/memberProfile/tabViews/points/PointsPage";
import Rewards from "Components/members/memberProfile/tabViews/rewards/Rewards";
import Notes from "Components/members/memberProfile/tabViews/notes/Notes";
import ImageEditor from "Components/common/imageEditor/ImageEditor";
import AddPoints from "Components/members/memberProfile/other/AddPoints";
import RedeemPoints from "Components/members/memberProfile/other/RedeemPoints";
import AdjustPoints from "Components/members/memberProfile/other/AdjustPoints";
import RedeemPartnerReward from "Components/members/memberProfile/other/RedeemPartnerReward";
import LoyaltyCards from "Components/members/memberProfile/memberAbout/loyaltyCards/LoyaltyCards";
import Tags from "Components/members/memberProfile/memberAbout/tags/Tags";
import CustomerPortal from "Components/members/memberProfile/memberAbout/customerPortal/CustomerPortal";
import MemberDetails from "Components/members/memberProfile/memberAbout/memberDetails/MemberDetails";
import SuspendAccount from "Components/members/memberProfile/other/SuspendAccount";
import EditBasicInfo from "Components/members/memberProfile/other/EditBasicInfo";
import ChangeAffinityGroup from "Components/members/memberProfile/other/ChangeAffinityGroup";
import ArchiveAccount from "Components/members/memberProfile/other/ArchiveAccount";
import CharityOverview from "./CharityOverview";
import EditViewCharityLogo from "./editViewCharityLogo/EditViewCharityLogo";
import ForgetAccount from "Components/members/memberProfile/other/ForgetAccount";
import AccountExportModal from "Components/members/memberProfile/other/AccountExportModal";
import UnauthorizedAccessControl from "../../utils/unauthorizedAccessControl/UnauthorizedAccessControl";
import ViewDescriptionModal from "./viewDescription/ViewDescriptionModal";
import { MemberReactivateWizard } from "Components/members/memberProfile/memberReactivate/memberReactivateWizard/MemberReactivateWizard";
import HistoryEvents from "../../members/memberProfile/tabViews/historyEvents/HistoryEvents";

import "Components/members/memberProfile/MemberProfile.scss";

const tabs = {
    overview: "Overview",
    activityLog: "ActivityLog",
    points: "Points",
    rewards: "Rewards",
    notes: "Notes",
    history: "History",
};

const DetailsTab = ({
    tab,
    member,
    selectedRegion,
    rewards,
    setIsDisabledTab,
}) => {
    if (!member._id) {
        return null;
    }

    switch (tab) {
        case tabs.overview: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.ACTIVITY}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.ACTIVITY
                            ].actions.ListActivities,
                        ],
                    }}
                    logic={"AND"}
                >
                    <CharityOverview
                        memberId={member._id}
                        points={member.points}
                        stats={member.stats}
                        setIsDisabledTab={setIsDisabledTab}
                    />
                </UnauthorizedAccessControl>
            );
        }
        case tabs.activityLog: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.ACTIVITY}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.ACTIVITY
                            ].actions.ListActivities,
                        ],
                    }}
                    logic={"AND"}
                >
                    <ActivityLog
                        memberId={member._id}
                        setIsDisabledTab={setIsDisabledTab}
                    />
                </UnauthorizedAccessControl>
            );
        }
        case tabs.points: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.TRANSACTION}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.TRANSACTION
                            ].actions.ListTransactions,
                        ],
                        [`${AccessPermissionModuleNames.REWARD}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.REWARD
                            ].actions.ListRewardRedemptionLogs,
                        ],
                    }}
                    logic={"OR"}
                >
                    <PointsPage
                        memberId={member._id}
                        rewards={rewards}
                        setIsDisabledTab={setIsDisabledTab}
                    />
                </UnauthorizedAccessControl>
            );
        }
        case tabs.rewards: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.REWARD}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.REWARD
                            ].actions.ListRewards,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.REWARD
                            ].actions.ListRewardRedemptionLogs,
                        ],
                    }}
                    logic={"OR"}
                >
                    <Rewards
                        memberId={member?._id}
                        loyaltyId={member?.loyaltyId}
                        availablePoints={member.points}
                        setIsDisabledTab={setIsDisabledTab}
                    />
                </UnauthorizedAccessControl>
            );
        }
        case tabs.notes: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.MEMBER_NOTE}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.MEMBER_NOTE
                            ].actions.ListMemberNotes,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.MEMBER_NOTE
                            ].actions.CreateMemberNote,
                        ],
                    }}
                    logic={"OR"}
                >
                    <Notes
                        memberId={member._id}
                        setIsDisabledTab={setIsDisabledTab}
                    />
                </UnauthorizedAccessControl>
            );
        }
        case tabs.history: {
            return (
                <UnauthorizedAccessControl
                    actionList={{
                        [`${AccessPermissionModuleNames.MEMBER}`]: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.MEMBER
                            ].actions.GetMember,
                        ],
                    }}
                    logic={"AND"}
                >
                    <HistoryEvents historyEvents={member?.historyEvents} />
                </UnauthorizedAccessControl>
            );
        }
        default: {
            return null;
        }
    }
};

const CharityProfile = () => {
    const { selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const { affinityGroups, rewards } = useContext(DataContext);
    const { updateMember } = useContext(MembersContext);
    const {
        showMemberReactivateWizard,
        setAttribute,
        setShowMemberReactivateWizard,
    } = useContext(MemberReactivateContext);
    const [tab, setTab] = useState(tabs.overview);
    const [member, setMember] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingCards, setIsLoadingCards] = useState(false);
    const [updatedAffinityGroup, setUpdatedAffinityGroup] = useState([]);
    const [accountStatus, setAccountStatus] = useState("");
    const [accountType, setAccountType] = useState("");
    const [showEdit, setShowEdit] = useState(false);
    const [showMobileEdit, setShowMobileEdit] = useState(false);
    const [showEditAddress, setShowEditAddress] = useState(false);
    const [showAffinityGroup, setShowAffinityGroup] = useState(false);
    const [showArchiveModal, setShowArchiveModal] = useState(false);
    const [showForgetModel, setShowForgetModel] = useState(false);
    const [showExportMemberModal, setShowExportMemberModal] = useState(false);
    const [pointsActionType, setPointsActionType] = useState("");
    const [showSuspendAccount, setShowSuspendAccount] = useState(false);
    const [showNameEdit, setShowNameEdit] = useState(false);
    const [showCharityLogo, toggleShowCharityLogo] = useToggle(false);
    const [addChangeCharityLogo, toggleAddChangeCharityLogo] = useToggle(false);
    const [isUpdating, setIsUpdating] = useState(false);
    const [isDisabledTab, setIsDisabledTab] = useState(false);
    const [loyaltyCards, setLoyaltyCards] = useState([]);
    const [totalCards, setTotalCards] = useState("");
    const [memberSuspendedCards, setMemberSuspendedCards] = useState([]);
    const [memberPrevCard, setMemberPrevCard] = useState({});
    const { id: charityId } = useParams();
    const history = useHistory();
    const location = useLocation();

    const [showDescription, toggleShowDescription] = useToggle(false);

    const {
        preferredName,
        profilePicture,
        email,
        mobileNumber,
        loyaltyId,
        lastSeenOn,
        tierPoints,
        points,
        residentialAddress,
        tier,
        tierData,
        description,
    } = member;

    const pointActionsMap = useMemo(() => {
        const options = {};
        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.CollectPointsAmount
            ) ||
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.CollectPointsBill
            )
        ) {
            options.addPoints = {
                id: "addPoints",
                name: "Add Points",
            };
        }

        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.RedeemPoints
            )
        ) {
            options.redeemPoints = {
                id: "redeemPoints",
                name: "Redeem Points",
            };
        }

        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.POINT,
                AccessPermissionModules[AccessPermissionModuleNames.POINT]
                    .actions.AdjustPoints
            )
        ) {
            options.adjustPoints = {
                id: "adjustPoints",
                name: "Adjust Points",
            };
        }

        return options;
    }, [isAuthorizedForAction]);

    const showPartnerReward = useMemo(
        () =>
            isAuthorizedForAction(
                AccessPermissionModuleNames.REWARD,
                AccessPermissionModules[AccessPermissionModuleNames.REWARD]
                    .actions.RedeemReward
            )
                ? rewards.reduce((result, item) => {
                    result[item._id] = { name: item.name };
                    return result;
                }, pointActionsMap)
                : pointActionsMap,
        [rewards, isAuthorizedForAction, pointActionsMap]
    );

    const selectedAffinityGroup = useMemo(
        () =>
            affinityGroups
                ?.filter((group) => group._id === member.affinityGroupId)
                .map((group) => ({ value: group._id, name: group.name })) || [],
        [affinityGroups, member.affinityGroupId]
    );

    const loadProfile = useCallback(async () => {
        try {
            setIsLoading(true);
            const memberProfileResponse = await getCharityById(charityId);
            setMember(memberProfileResponse);
            setAccountType(memberProfileResponse.type);
            setAccountStatus(memberProfileResponse.status);
            updateMember(memberProfileResponse);
        } catch (e) {
            console.error(e);
        } finally {
            setIsLoading(false);
        }
    }, [
        charityId,
        updateMember,
        setMember,
        setAccountType,
        setAccountStatus,
        setIsLoading,
    ]);

    const loadCardData = useCallback(async () => {
        const queryObj = { regionId: selectedRegion._id, memberId: charityId };
        try {
            setLoyaltyCards([]);
            setTotalCards(0);
            setIsLoadingCards(true);

            const cards = await getAllCardsOfMember(queryObj);

            const activeCards = cards?.items.filter(
                (card) =>
                    card.status === CardStatus.ACTIVE ||
                    card.status === CardStatus.ASSIGNED
            );
            const otherCards = cards?.items
                .filter(
                    (card) =>
                        card.status === CardStatus.DEACTIVATED ||
                        card.status === CardStatus.SUSPENDED
                )
                .sort((a, b) => a?.status.localeCompare(b?.status))
                .reverse();
            let cardList = [];

            if (activeCards.length > 0 && otherCards.length > 0) {
                cardList = [...activeCards, ...otherCards];
                setLoyaltyCards([...activeCards, ...otherCards]);
            } else if (activeCards.length > 0 && otherCards.length === 0) {
                cardList = [...activeCards];
                setLoyaltyCards(activeCards);
            } else {
                setLoyaltyCards(otherCards);
                cardList = [...otherCards];
            }
            setTotalCards(cards.total);

            // * Card data is prepared for member reactivation logic.
            const cardsSortedByLastUsed = cardList.sort(
                (a, b) => new Date(b?.updatedOn) - new Date(a?.updatedOn)
            );
            const prevCard =
                cardsSortedByLastUsed.find(
                    (sC) => sC?.status === CardStatus.ASSIGNED
                ) ||
                cardsSortedByLastUsed[0] ||
                {};

            setMemberSuspendedCards(
                cardsSortedByLastUsed.filter(
                    (cSBLU) => cSBLU?.status === CardStatus.SUSPENDED
                )
            );
            setMemberPrevCard(prevCard);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    {`Failed to load `}
                    {member.firstName || member.lastName
                        ? `${member.firstName} ${member.lastName}`
                        : "Member"}
                    's card data!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoadingCards(false);
        }
    }, [
        selectedRegion._id,
        charityId,
        member.firstName,
        member.lastName,
        setIsLoadingCards,
        setLoyaltyCards,
        setTotalCards,
        setMemberSuspendedCards,
        setMemberPrevCard,
    ]);

    const showEditEmail = useCallback(() => {
        setShowEdit(true);
    }, [setShowEdit]);

    const onHideEditEmail = useCallback(
        (e, data) => {
            if (data) {
                setMember(data);
            }
            setShowEdit(false);
        },
        [setShowEdit, setMember]
    );

    const showEditName = useCallback(() => {
        setShowNameEdit(true);
    }, [setShowNameEdit]);

    const onHideEditName = useCallback(
        (e, data) => {
            if (data) {
                setMember(data);
            }
            setShowNameEdit(false);
        },
        [setMember, setShowNameEdit]
    );

    const showEditMobile = useCallback(() => {
        setShowMobileEdit(true);
    }, [setShowMobileEdit]);

    const onHideEditMobile = useCallback(
        (e, data) => {
            if (data) {
                setMember(data);
            }
            setShowMobileEdit(false);
        },
        [setShowMobileEdit, setMember]
    );

    const onShowAddressEdit = useCallback(
        () => setShowEditAddress(true),
        [setShowEditAddress]
    );

    const onHideAddressEdit = useCallback(
        (e, data) => {
            if (data) {
                setMember(data);
            }
            setShowEditAddress(false);
        },
        [setMember, setShowEditAddress]
    );

    const onChangeUserSettingActions = useCallback(
        (e) => {
            if (e === "Archive Member") {
                setShowArchiveModal(true);
            } else if (e === "Forget Member") {
                setShowForgetModel(true);
            } else {
                setShowExportMemberModal(true);
            }
        },
        [setShowArchiveModal, setShowExportMemberModal, setShowForgetModel]
    );

    const onHideArchiveModal = useCallback(() => {
        setShowArchiveModal(false);
    }, [setShowArchiveModal]);

    const onHideForgetModal = useCallback(() => {
        setShowForgetModel(false);
    }, [setShowForgetModel]);

    const onHideExportMemberModal = useCallback(() => {
        setShowExportMemberModal(false);
    }, [setShowExportMemberModal]);

    const onHidePointModal = useCallback(
        (e, data) => {
            if (data) {
                loadProfile();
            }
            setPointsActionType("");
        },
        [setPointsActionType, loadProfile]
    );

    const onShowSuspendAccount = useCallback(
        () => setShowSuspendAccount(true),
        [setShowSuspendAccount]
    );

    const onHideSuspendAccount = useCallback(() => {
        setShowSuspendAccount(false);
    }, [setShowSuspendAccount]);

    const onShowAffinityGroup = useCallback(
        () => setShowAffinityGroup(true),
        [setShowAffinityGroup]
    );

    const onHideAffinityGroup = useCallback(() => {
        setShowAffinityGroup(false);
        setUpdatedAffinityGroup([]);
    }, [setShowAffinityGroup, setUpdatedAffinityGroup]);

    const onToggleImageEditor = useCallback(() => {
        if (showCharityLogo) {
            toggleShowCharityLogo();
            toggleAddChangeCharityLogo();
        } else if (addChangeCharityLogo) {
            toggleAddChangeCharityLogo();
            toggleShowCharityLogo();
        }
    }, [
        showCharityLogo,
        addChangeCharityLogo,
        toggleShowCharityLogo,
        toggleAddChangeCharityLogo,
    ]);

    const onShowMemberReactivateWizard = useCallback(
        () => setShowMemberReactivateWizard(true),
        [setShowMemberReactivateWizard]
    );

    const onNavigatingBack = useCallback(() => {
        history.push("/charity-list");
    }, [history]);

    const onSaveChanges = useCallback(
        async (imageUrl) => {
            if (imageUrl) {
                try {
                    const charityLogoPayload = { profilePicture: imageUrl };

                    setIsUpdating(true);
                    const updatedCharity = await updateCharity(
                        charityId,
                        charityLogoPayload
                    );
                    toast.success(
                        `Successfully ${
                            !profilePicture ? "added" : "updated"
                        } charity logo.`
                    );
                    setIsUpdating(false);
                    setMember(updatedCharity);
                    toggleAddChangeCharityLogo();
                } catch (e) {
                    setIsUpdating(false);
                    console.error(e);
                    toast.error(
                        <div>
                            {`Failed to ${
                                !profilePicture ? "add" : "update"
                            } charity logo!`}
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            }
        },
        [
            charityId,
            profilePicture,
            setIsUpdating,
            setMember,
            toggleAddChangeCharityLogo,
        ]
    );

    useEffect(() => {
        if (updatedAffinityGroup.length > 0) {
            onShowAffinityGroup();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [updatedAffinityGroup]);

    useEffect(() => {
        if (location.state?.member) {
            setMember(location.state.member);
        }
    }, [location]);

    useEffect(() => {
        if (
            charityId &&
            isAuthorizedForAction(
                AccessPermissionModuleNames.CHARITIES,
                AccessPermissionModules[AccessPermissionModuleNames.CHARITIES]
                    .actions.GetCharity
            )
        ) {
            loadProfile();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [charityId]);

    useEffect(() => {
        if (
            charityId &&
            isAuthorizedForAction(
                AccessPermissionModuleNames.CHARITIES,
                AccessPermissionModules[AccessPermissionModuleNames.CHARITIES]
                    .actions.GetCharity
            )
        ) {
            loadCardData();
        }
        // eslint-disable-next-line
    }, [charityId]);

    useEffect(() => {
        if (charityId && accountStatus === MemberStatus.SUSPENDED) {
            setAttribute({ key: "memberId", value: charityId });
        }
    }, [charityId, accountStatus, setAttribute]);

    const pointAdjustModal = useCallback(() => {
        switch (pointsActionType) {
            case pointActionsMap.addPoints?.id: {
                return (
                    <AddPoints
                        show={true}
                        onHide={onHidePointModal}
                        memberId={charityId}
                    />
                );
            }

            case pointActionsMap.redeemPoints?.id: {
                return (
                    <RedeemPoints
                        show={
                            pointsActionType === pointActionsMap.redeemPoints.id
                        }
                        onHide={onHidePointModal}
                        memberId={charityId}
                    />
                );
            }

            case pointActionsMap.adjustPoints?.id: {
                return (
                    <AdjustPoints
                        show={
                            pointsActionType === pointActionsMap.adjustPoints.id
                        }
                        onHide={onHidePointModal}
                        memberId={charityId}
                    />
                );
            }
            default: {
                if (pointsActionType !== "") {
                    return (
                        <RedeemPartnerReward
                            show={true}
                            onHide={onHidePointModal}
                            memberId={charityId}
                            member={member}
                            reward={rewards.find(
                                (pR) => pR?._id === pointsActionType
                            )}
                        />
                    );
                }
                return null;
            }
        }
    }, [
        pointsActionType,
        pointActionsMap.addPoints?.id,

        pointActionsMap.redeemPoints?.id,
        pointActionsMap.adjustPoints?.id,
        charityId,
        member,
        rewards,
        onHidePointModal,
    ]);

    return (
        <div className="member-profile">
            <BaseLayout
                topLeft={<Heading text="Charity" />}
                topRight={
                    <div className="d-flex">
                        <Button
                            className="btn shadow-none"
                            variant="link"
                            size="sm"
                            disabled={isLoading}
                            onClick={onNavigatingBack}
                        >
                            <div className="d-flex align-items-center">
                                <IcIcon
                                    icon={faAngleLeftB}
                                    size="lg"
                                    className="mr-1"
                                />
                                Back
                            </div>
                        </Button>
                        <DropdownButton
                            data-testid="region-selection-dropdown"
                            variant="primary"
                            size="sm"
                            title={
                                <>
                                    <IcIcon
                                        className="mr-2"
                                        size="w-16"
                                        icon={faUser}
                                    />
                                    <span>Charity Settings</span>
                                </>
                            }
                            className="mr-2"
                            onSelect={onChangeUserSettingActions}
                            disabled={isLoading}
                        >
                            <DropdownItem
                                eventKey="Export Member"
                                key="Export Member"
                                disabled={
                                    !isAuthorizedForAction(
                                        AccessPermissionModuleNames.CHARITIES,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames
                                                .CHARITIES
                                        ].actions.ExportCharity
                                    )
                                }
                            >
                                Export Charity
                            </DropdownItem>
                            <DropdownItem
                                eventKey="Forget Member"
                                key="Forget Member"
                                disabled={
                                    !isAuthorizedForAction(
                                        AccessPermissionModuleNames.CHARITIES,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames
                                                .CHARITIES
                                        ].actions.EraseCharity
                                    )
                                }
                            >
                                Forget Charity
                            </DropdownItem>
                            <DropdownItem
                                eventKey="Archive Member"
                                key="Archive Member"
                                disabled={
                                    !isAuthorizedForAction(
                                        AccessPermissionModuleNames.CHARITIES,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames
                                                .CHARITIES
                                        ].actions.DeleteCharity
                                    )
                                }
                            >
                                <span
                                    className={`${
                                        isAuthorizedForAction(
                                            AccessPermissionModuleNames.CHARITIES,
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames
                                                    .CHARITIES
                                            ].actions.DeleteCharity
                                        ) && "text-danger"
                                    }`}
                                >
                                    Archive Charity
                                </span>
                            </DropdownItem>
                        </DropdownButton>
                    </div>
                }
                bottom={
                    <>
                        {isLoading ? (
                            <LoadingComponent />
                        ) : (
                            <div className="basic-details-top">
                                <Row className="mb-3 pt-3 basic-detail-row">
                                    <Col
                                        xl="3"
                                        lg="3"
                                        md="3"
                                        className="border-solid-right d-flex align-items-center"
                                    >
                                        <div className="left-panel profile-div">
                                            <div className="d-flex flex-row align-items-center">
                                                <div className="pt-3 mt-1 mr-3 avatar-container">
                                                    <Avatar
                                                        className="avatar-image"
                                                        name={
                                                            !profilePicture &&
                                                            preferredName
                                                        }
                                                        src={profilePicture}
                                                        alt={profilePicture}
                                                        size="80"
                                                    />
                                                    <div className="pt-3 mt- mr-3 avatar-middle">
                                                        <div className="avatar-text d-flex flex-column justify-content-center">
                                                            <UnauthorizedAccessControl
                                                                actionList={{
                                                                    [`${AccessPermissionModuleNames.CHARITIES}`]:
                                                                        [
                                                                            AccessPermissionModules[
                                                                                AccessPermissionModuleNames
                                                                                    .CHARITIES
                                                                            ]
                                                                                .actions
                                                                                .UpdateCharity,
                                                                        ],
                                                                }}
                                                                logic={"OR"}
                                                                renderEmpty={
                                                                    true
                                                                }
                                                            >
                                                                <div className="py-1">
                                                                    {!profilePicture ? (
                                                                        <>
                                                                            <IcIcon
                                                                                size="lg"
                                                                                icon={
                                                                                    faPlus
                                                                                }
                                                                            />
                                                                            <div
                                                                                onClick={
                                                                                    toggleAddChangeCharityLogo
                                                                                }
                                                                                className="cursor-pointer edit-text"
                                                                            >
                                                                                Add
                                                                            </div>
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <IcIcon
                                                                                size="lg"
                                                                                icon={
                                                                                    faImage
                                                                                }
                                                                            />
                                                                            <div
                                                                                onClick={
                                                                                    toggleShowCharityLogo
                                                                                }
                                                                                className="cursor-pointer edit-text"
                                                                            >
                                                                                View
                                                                            </div>
                                                                        </>
                                                                    )}
                                                                </div>
                                                            </UnauthorizedAccessControl>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="d-flex flex-column mr-1">
                                                    <div className="">
                                                        <UnauthorizedAccessControl
                                                            actionList={{
                                                                [`${AccessPermissionModuleNames.CHARITIES}`]:
                                                                    [
                                                                        AccessPermissionModules[
                                                                            AccessPermissionModuleNames
                                                                                .CHARITIES
                                                                        ]
                                                                            .actions
                                                                            .UpdateCharity,
                                                                    ],
                                                            }}
                                                            logic={"OR"}
                                                            renderEmpty={true}
                                                        >
                                                            <Button
                                                                size="sm"
                                                                variant="link"
                                                                onClick={
                                                                    showEditName
                                                                }
                                                                className="cursor-pointer dropdown-btn float-right"
                                                            >
                                                                <p className="mb-0">
                                                                    Edit
                                                                </p>
                                                            </Button>
                                                        </UnauthorizedAccessControl>
                                                    </div>
                                                    <h3 className="my-0 py-0">
                                                        {preferredName}
                                                    </h3>
                                                    <p className="text-muted mb-0">
                                                        {loyaltyId}
                                                    </p>
                                                    <small className="mb-0">
                                                        Last seen on{" "}
                                                        {lastSeenOn
                                                            ? formatToCommonReadableFormat(
                                                                lastSeenOn
                                                            )
                                                            : "unknown"}
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </Col>
                                    <Col
                                        xl="9"
                                        lg="9"
                                        md="9"
                                        className="d-flex align-items-center right-side"
                                    >
                                        <div className="d-flex flex-row">
                                            <div className="border-solid-right pr-1 contact-div">
                                                <div className="top-panel-border-bottom panel pr-2">
                                                    <div className="d-flex flex-row justify-content-between">
                                                        <div className="d-flex flex-row mr-auto">
                                                            <p className="py-1 mb-0 mx-2">
                                                                Email
                                                            </p>
                                                        </div>
                                                        <UnauthorizedAccessControl
                                                            actionList={{
                                                                [`${AccessPermissionModuleNames.CHARITIES}`]:
                                                                    [
                                                                        AccessPermissionModules[
                                                                            AccessPermissionModuleNames
                                                                                .CHARITIES
                                                                        ]
                                                                            .actions
                                                                            .UpdateCharity,
                                                                    ],
                                                            }}
                                                            logic={"OR"}
                                                            renderEmpty={true}
                                                        >
                                                            <div className="d-flex flex-row">
                                                                <Button
                                                                    size="sm"
                                                                    variant="link"
                                                                    onClick={
                                                                        showEditEmail
                                                                    }
                                                                    className="cursor-pointer dropdown-btn"
                                                                >
                                                                    <p className="mb-0">
                                                                        Edit
                                                                    </p>
                                                                </Button>
                                                            </div>
                                                        </UnauthorizedAccessControl>
                                                    </div>
                                                    <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                        <IcIcon
                                                            icon={faEnvelope}
                                                            className="mr-2 text-primary"
                                                            size="w-10"
                                                        />
                                                        {email || "unknown"}
                                                    </p>
                                                </div>
                                                <div className="pr-2 pt-2">
                                                    <div className="d-flex flex-row justify-content-between">
                                                        <div className="d-flex flex-row mr-auto">
                                                            <p className="py-1 mb-0 mx-2">
                                                                Contact No
                                                            </p>
                                                        </div>
                                                        <div className="d-flex flex-row">
                                                            <UnauthorizedAccessControl
                                                                actionList={{
                                                                    [`${AccessPermissionModuleNames.CHARITIES}`]:
                                                                        [
                                                                            AccessPermissionModules[
                                                                                AccessPermissionModuleNames
                                                                                    .CHARITIES
                                                                            ]
                                                                                .actions
                                                                                .UpdateCharity,
                                                                        ],
                                                                }}
                                                                logic={"OR"}
                                                                renderEmpty={
                                                                    true
                                                                }
                                                            >
                                                                <Button
                                                                    size="sm"
                                                                    variant="link"
                                                                    onClick={
                                                                        showEditMobile
                                                                    }
                                                                    className="cursor-pointer dropdown-btn"
                                                                >
                                                                    <p className="mb-0">
                                                                        Edit
                                                                    </p>
                                                                </Button>
                                                            </UnauthorizedAccessControl>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                            <IcIcon
                                                                icon={faPhone}
                                                                className="mr-2 text-primary"
                                                                size="w-10"
                                                            />{" "}
                                                            {mobileNumber ||
                                                                "unknown"}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="border-solid-right px-3 account-div">
                                                <div className="top-panel-border-bottom  panel">
                                                    <div className="d-flex flex-row justify-content-between">
                                                        <div className="d-flex flex-row mr-auto">
                                                            <p className="py-1 mb-0 mr-2">
                                                                Account Type
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <Badge
                                                        className="py-2 mb-0 px-2"
                                                        variant="secondary"
                                                    >
                                                        {toTitleCase(
                                                            accountType
                                                        )}
                                                    </Badge>
                                                </div>
                                                <div className="pr-2 pt-2">
                                                    <p className="py-1 mb-0 mr-2">
                                                        Account Status
                                                    </p>
                                                    {accountStatus ===
                                                    MemberStatus.ARCHIVED ? (
                                                        <Badge
                                                            className="py-2 px-2 mb-0"
                                                            variant="danger"
                                                        >
                                                            {toTitleCase(
                                                                accountStatus
                                                            )}
                                                        </Badge>
                                                    ) : (
                                                        <DropdownButton
                                                            variant={
                                                                accountStatus ===
                                                                MemberStatus.ACTIVE
                                                                    ? "success"
                                                                    : "orange"
                                                            }
                                                            size="sm"
                                                            title={
                                                                <span className="mr-2 font-weight-bold">
                                                                    {toTitleCase(
                                                                        accountStatus
                                                                    ) || "-"}
                                                                </span>
                                                            }
                                                        >
                                                            {accountStatus !==
                                                                MemberStatus.ACTIVE && (
                                                                <DropdownItem
                                                                    eventKey={
                                                                        MemberStatus.ACTIVE
                                                                    }
                                                                    key={
                                                                        MemberStatus.ACTIVE
                                                                    }
                                                                >
                                                                    <Button
                                                                        className="mb-2"
                                                                        variant="outline-success"
                                                                        size="sm"
                                                                        disabled={
                                                                            isLoading
                                                                        }
                                                                        onClick={
                                                                            onShowMemberReactivateWizard
                                                                        }
                                                                    >
                                                                        Reactivate
                                                                        Account
                                                                    </Button>
                                                                </DropdownItem>
                                                            )}
                                                            {accountStatus !==
                                                                MemberStatus.SUSPENDED && (
                                                                <DropdownItem
                                                                    eventKey={
                                                                        MemberStatus.SUSPENDED
                                                                    }
                                                                    key={
                                                                        MemberStatus.SUSPENDED
                                                                    }
                                                                >
                                                                    <Button
                                                                        className="mb-2"
                                                                        variant="outline-orange"
                                                                        size="sm"
                                                                        disabled={
                                                                            isLoading
                                                                        }
                                                                        onClick={
                                                                            onShowSuspendAccount
                                                                        }
                                                                    >
                                                                        Suspend
                                                                        Account
                                                                    </Button>
                                                                </DropdownItem>
                                                            )}
                                                        </DropdownButton>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="border-solid-right px-3 point-div">
                                                <div className="top-panel-border-bottom panel">
                                                    <div className="d-flex flex-row justify-content-between">
                                                        <div className="d-flex flex-row mr-auto">
                                                            <p className="py-1 mb-0 mx-2">
                                                                Redeemable
                                                                Points
                                                            </p>
                                                        </div>
                                                        <div className="d-flex flex-row">
                                                            <DropdownButton
                                                                data-testid="points-dropdown"
                                                                bsPrefix="dropdown-btn single-dropdown-toggle"
                                                                title={
                                                                    <IcIcon
                                                                        className="text-primary"
                                                                        size="w-10"
                                                                        icon={
                                                                            faEllipsisV
                                                                        }
                                                                    />
                                                                }
                                                                className=""
                                                                onSelect={
                                                                    setPointsActionType
                                                                }
                                                            >
                                                                {Object.entries(
                                                                    showPartnerReward
                                                                ).map(
                                                                    ([
                                                                        key,
                                                                        item,
                                                                    ]) => (
                                                                        <DropdownItem
                                                                            eventKey={
                                                                                key
                                                                            }
                                                                            key={
                                                                                key
                                                                            }
                                                                        >
                                                                            {
                                                                                item.name
                                                                            }
                                                                        </DropdownItem>
                                                                    )
                                                                )}
                                                            </DropdownButton>
                                                        </div>
                                                    </div>
                                                    <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                        {points || 0}
                                                    </p>
                                                </div>
                                                <div className="pr-2 pt-2">
                                                    <p className="py-1 mb-0 mx-2">
                                                        Tier Points
                                                    </p>
                                                    <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                        {tierPoints || 0}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="pl-3 tier-div">
                                                <div className="top-panel-border-bottom panel">
                                                    <p className="py-1 mb-0 mx-2">
                                                        Tier
                                                    </p>
                                                    <div className="d-flex justify-content-start mb-0 mx-2">
                                                        <IcIcon
                                                            icon={faMedal}
                                                            className="mr-2 text-primary"
                                                            size="w-10"
                                                        />
                                                        <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                            {tierData?.name ||
                                                                tier?.name ||
                                                                tier?.tierId ||
                                                                "-"}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="pr-2 pt-2">
                                                    <div>
                                                        <p className="py-1 mb-0 mx-2">
                                                            Description
                                                        </p>
                                                        <Button
                                                            variant={"link"}
                                                            onClick={
                                                                toggleShowDescription
                                                            }
                                                        >
                                                            Show
                                                        </Button>
                                                        {/* <p className="py-1 mb-0 mx-2">
                                                            View Description
                                                        </p> */}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                            </div>
                        )}
                        {isLoading ? (
                            <LoadingComponent />
                        ) : (
                            <Row className="mt-3">
                                <Col xl="3" lg="3" md="3">
                                    <div className="border-solid-right bottom-panel">
                                        <SubHeading text="About Member" />
                                        <LoyaltyCards
                                            isLoading={isLoadingCards}
                                            address={residentialAddress}
                                            name={`${member.firstName} ${member.lastName}`}
                                            memberId={charityId}
                                            loyaltyCards={loyaltyCards}
                                            totalCards={totalCards}
                                            setLoyaltyCards={setLoyaltyCards}
                                            loadProfile={loadProfile}
                                            loadCardData={loadCardData}
                                        />
                                        <Tags
                                            profileType={MemberTypes.CHARITY}
                                            memberId={charityId}
                                            memberTags={member?.tags || []}
                                            hasEditPermission={isAuthorizedForAction(
                                                AccessPermissionModuleNames.CHARITIES,
                                                AccessPermissionModules[
                                                    AccessPermissionModuleNames
                                                        .CHARITIES
                                                ].actions.UpdateCharity
                                            )}
                                            isLoading={isLoading}
                                            loadProfile={loadProfile}
                                        />
                                        {member && (
                                            <CustomerPortal
                                                portalData={
                                                    member.portal_metadata
                                                }
                                            />
                                        )}
                                        <MemberDetails
                                            member={{
                                                residentialAddress:
                                                    member.residentialAddress,
                                                country: member.country,
                                                countryCode: member.countryCode,
                                                createdBy: member.createdBy,
                                                createdOn: member.createdOn,
                                                email: member.email,
                                                lastSeenOn: member.lastSeenOn,
                                                loyaltyId: member.loyaltyId,
                                                merchantLocationId:
                                                    member.merchantLocationId,
                                                mobileNumber:
                                                    member.mobileNumber,
                                                organizationId:
                                                    member.organizationId,
                                                points: member.points,
                                                preferredName:
                                                    member.preferredName,
                                                purchasesCount:
                                                    member.purchasesCount,
                                                purchasesValue:
                                                    member.purchasesValue,
                                                regionId: member.regionId,
                                                registeredOn:
                                                    member.registeredOn,
                                                status: member.status,
                                                tags: member.tags,
                                                tierPoints: member.tierPoints,
                                                type: member.type,
                                                updatedOn: member.updatedOn,
                                                customAttributes:
                                                    member?.customAttributes ||
                                                    {},
                                                _id: member._id,
                                            }}
                                            loadProfile={loadProfile}
                                            setMember={setMember}
                                            hasEditPermission={isAuthorizedForAction(
                                                AccessPermissionModuleNames.CHARITIES,
                                                AccessPermissionModules[
                                                    AccessPermissionModuleNames
                                                        .CHARITIES
                                                ].actions.UpdateCharity
                                            )}
                                            profileType={MemberTypes.CHARITY}
                                        />
                                    </div>
                                </Col>
                                <Col xl="9" lg="9" md="9">
                                    <div>
                                        <Tabs
                                            activeKey={tab}
                                            transition={false}
                                            id="noanim-tab-example"
                                            className="ml-n3 mb-3 border-solid-bottom"
                                            onSelect={setTab}
                                        >
                                            <Tab
                                                eventKey={tabs.overview}
                                                disabled={isDisabledTab}
                                                title={
                                                    <>
                                                        <IcIcon
                                                            className="mr-2"
                                                            size="w-10"
                                                            icon={
                                                                faTachometerAlt
                                                            }
                                                        />
                                                        <span className="mr-2">
                                                            Overview
                                                        </span>
                                                    </>
                                                }
                                            />
                                            <Tab
                                                eventKey={tabs.activityLog}
                                                disabled={isDisabledTab}
                                                title={
                                                    <>
                                                        <IcIcon
                                                            className="mr-2"
                                                            size="w-10"
                                                            icon={
                                                                faFileBookmarkAlt
                                                            }
                                                        />
                                                        <span className="mr-2">
                                                            Activity Log
                                                        </span>
                                                    </>
                                                }
                                            />
                                            <Tab
                                                eventKey={tabs.history}
                                                title={
                                                    <>
                                                        <IcIcon
                                                            className="mr-2"
                                                            size="w-10"
                                                            icon={faHistory}
                                                        />
                                                        <span className="mr-2">
                                                            History Events
                                                        </span>
                                                    </>
                                                }
                                                disabled={isDisabledTab}
                                            />
                                            <Tab
                                                eventKey={tabs.points}
                                                disabled={isDisabledTab}
                                                title={
                                                    <>
                                                        <IcIcon
                                                            className="mr-2"
                                                            size="w-10"
                                                            icon={faCoins}
                                                        />
                                                        <span className="mr-2">
                                                            Points
                                                        </span>
                                                    </>
                                                }
                                            />
                                            <Tab
                                                eventKey={tabs.rewards}
                                                disabled={isDisabledTab}
                                                title={
                                                    <>
                                                        <IcIcon
                                                            className="mr-2"
                                                            size="w-10"
                                                            icon={faGift}
                                                        />
                                                        <span className="mr-2">
                                                            Rewards
                                                        </span>
                                                    </>
                                                }
                                            />
                                            <Tab
                                                eventKey={tabs.notes}
                                                disabled={isDisabledTab}
                                                title={
                                                    <>
                                                        <IcIcon
                                                            className="mr-2"
                                                            size="w-10"
                                                            icon={faFile}
                                                        />
                                                        <span className="mr-2">
                                                            Notes
                                                        </span>
                                                    </>
                                                }
                                            />
                                        </Tabs>
                                        <DetailsTab
                                            tab={tab}
                                            member={member}
                                            selectedRegion={selectedRegion}
                                            rewards={rewards}
                                            setIsDisabledTab={setIsDisabledTab}
                                        />
                                    </div>
                                </Col>
                            </Row>
                        )}
                    </>
                }
            />
            {showSuspendAccount && (
                <SuspendAccount
                    show={showSuspendAccount}
                    onHide={onHideSuspendAccount}
                    memberId={charityId}
                    loadProfile={loadProfile}
                    loadCardData={loadCardData}
                    profileType={MemberTypes.CHARITY}
                />
            )}

            {showEdit && (
                <EditBasicInfo
                    member={member}
                    type="Email Address"
                    value={email}
                    show={showEdit}
                    onHide={onHideEditEmail}
                    showEdit={setShowEdit}
                    loadProfile={loadProfile}
                    profileType={MemberTypes.CHARITY}
                />
            )}
            {showNameEdit && (
                <EditBasicInfo
                    member={member}
                    type="Charity"
                    value={preferredName}
                    show={showNameEdit}
                    onHide={onHideEditName}
                    showEdit={setShowNameEdit}
                    loadProfile={loadProfile}
                    profileType={MemberTypes.CHARITY}
                />
            )}
            {showMobileEdit && (
                <EditBasicInfo
                    member={member}
                    type="Contact Number"
                    value={mobileNumber}
                    show={showMobileEdit}
                    onHide={onHideEditMobile}
                    showEdit={setShowMobileEdit}
                    profileType={MemberTypes.CHARITY}
                    loadProfile={loadProfile}
                />
            )}
            {showEditAddress && (
                <EditBasicInfo
                    member={member}
                    type="Address"
                    value={{
                        residentialAddress: residentialAddress,
                        country: member.country,
                        countryCode: member.countryCode,
                    }}
                    show={showEditAddress}
                    onHide={onHideAddressEdit}
                    showEdit={onShowAddressEdit}
                    loadProfile={loadProfile}
                />
            )}
            {showAffinityGroup && (
                <ChangeAffinityGroup
                    memberId={charityId}
                    show={showAffinityGroup}
                    onHide={onHideAffinityGroup}
                    loadProfile={loadProfile}
                    affinityGroup={selectedAffinityGroup}
                    updatedAffinityGroup={updatedAffinityGroup}
                />
            )}
            {showArchiveModal && (
                <ArchiveAccount
                    show={showArchiveModal}
                    memberId={charityId}
                    member={member}
                    profileType={MemberTypes.CHARITY}
                    onHide={onHideArchiveModal}
                    onNavigatingBack={onNavigatingBack}
                />
            )}
            {showForgetModel && (
                <ForgetAccount
                    show={showForgetModel}
                    memberId={charityId}
                    member={member}
                    profileType={MemberTypes.CHARITY}
                    onHide={onHideForgetModal}
                    onNavigatingBack={onNavigatingBack}
                />
            )}
            {showExportMemberModal && (
                <AccountExportModal
                    show={showExportMemberModal}
                    onHide={onHideExportMemberModal}
                    memberId={charityId}
                    profileType={MemberTypes.CHARITY}
                />
            )}

            {showCharityLogo && (
                <EditViewCharityLogo
                    show={showCharityLogo}
                    onHide={toggleShowCharityLogo}
                    currentDetails={{ charityLogoUrl: profilePicture }}
                    memberId={charityId}
                    onShowEditor={onToggleImageEditor}
                />
            )}
            {addChangeCharityLogo && (
                <ImageEditor
                    editingImage={!profilePicture}
                    title={`${!profilePicture ? "Add" : "Change"} Charity Logo`}
                    show={addChangeCharityLogo}
                    onHide={toggleAddChangeCharityLogo}
                    onCloseEditor={onToggleImageEditor}
                    isChanging={isUpdating}
                    onSave={onSaveChanges}
                    size={400}
                />
            )}
            {showDescription && (
                <ViewDescriptionModal
                    show={showDescription}
                    onHide={toggleShowDescription}
                    text={description}
                />
            )}
            {charityId &&
                accountStatus === MemberStatus.SUSPENDED &&
                showMemberReactivateWizard && (
                    <MemberReactivateWizard
                        memberId={charityId}
                        profileType={MemberTypes.CHARITY}
                        suspendedCards={memberSuspendedCards}
                        previousCard={memberPrevCard}
                        loadProfile={loadProfile}
                        loadCardData={loadCardData}
                    />
                )}
            {pointAdjustModal()}
        </div>
    );
};

export default CharityProfile;
