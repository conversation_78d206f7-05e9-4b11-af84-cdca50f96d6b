import React from "react";
import {Route, Switch} from "react-router-dom";
import UnauthorizedAccessControl from "../utils/unauthorizedAccessControl/UnauthorizedAccessControl";
import {AccessPermissionModuleNames, AccessPermissionModules} from "../../Data";
import Charity from "./CharityList";
import CharityProfilePage from "./charity/CharityProfilePage";

const CharityPage=()=>{
    return(
        <Switch>
            <Route
                exact
                path="/charity-list"
            >
                <UnauthorizedAccessControl
                    actionList={{ [`${AccessPermissionModuleNames.CHARITIES}`]:[
                            AccessPermissionModules[AccessPermissionModuleNames.CHARITIES].actions.ListCharities,
                            AccessPermissionModules[AccessPermissionModuleNames.CHARITIES].actions.GetCharity,
                            AccessPermissionModules[AccessPermissionModuleNames.CHARITIES].actions.CreateCharity,
                            AccessPermissionModules[AccessPermissionModuleNames.CHARITIES].actions.UpdateCharity,
                            AccessPermissionModules[AccessPermissionModuleNames.CHARITIES].actions.DeleteCharity,
                            AccessPermissionModules[AccessPermissionModuleNames.CHARITIES].actions.ExportCharity,
                            AccessPermissionModules[AccessPermissionModuleNames.CHARITIES].actions.EraseCharity,
                        ]}}
                    logic={"OR"}>
                    <Charity/>
                </UnauthorizedAccessControl>
            </Route>

            <Route
                exact
                path="/charity-list/charity-profile/:id">
                <UnauthorizedAccessControl
                    actionList={{ [`${AccessPermissionModuleNames.CHARITIES}`]:[
                            AccessPermissionModules[AccessPermissionModuleNames.CHARITIES].actions.GetCharity,
                        ]}}
                    logic={"OR"}>
                    <CharityProfilePage/>
                </UnauthorizedAccessControl>
            </Route>
        </Switch>
    )
}

export default CharityPage;