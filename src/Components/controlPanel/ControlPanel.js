import React, { useCallback, useContext, useMemo, useState } from "react";
import { Switch, useHistory, Route, useLocation } from "react-router";
import {
    Tab,
    Tabs,
    Heading,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faLayerGroup, faPlug, faUsers } from "FaICIconMap";
import { AccessControlContext } from "Contexts/accessControl/accessControlContext";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import BaseLayout from "Layout/BaseLayout";
import UnauthorizedAccessControl from "../utils/unauthorizedAccessControl/UnauthorizedAccessControl";
import UsersPage from "./users/UsersPage";
import GroupManagePage from "./group/GroupMangePage";
import IntegrationsPage from "./integrations/IntegrationsPage";

const tabs = {
    USERS: "users",
    GROUPS: "groups",
    INTEGRATION: "integration",
};

const ControlPanel = () => {
    const { isLoading: isLoadingGroups } = useContext(AccessControlContext);
    const [isDisabledTab, setIsDisabledTab] = useState(false);
    const history = useHistory();
    const location = useLocation();

    const selectTab = useCallback(
        (key) => {
            history.push(`/control-panel/access-control/${key}`);
        },
        [history]
    );

    const currentTab = useMemo(() => {
        if (location.pathname.includes(tabs.INTEGRATION)) {
            return tabs.INTEGRATION;
        } else if (location.pathname.includes(tabs.GROUPS)) {
            return tabs.GROUPS;
        }

        return tabs.USERS;
    }, [location.pathname]);

    return (
        <BaseLayout
            topLeft={<Heading text="Access Control" />}
            bottom={
                <>
                    <Tabs
                        defaultActiveKey={currentTab}
                        transition={false}
                        onSelect={selectTab}
                    >
                        <Tab
                            eventKey={tabs.USERS}
                            title={
                                <div className="d-flex align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faUsers}
                                    />
                                    User Management
                                </div>
                            }
                            disabled={isLoadingGroups || isDisabledTab}
                        />
                        <Tab
                            eventKey={tabs.GROUPS}
                            title={
                                <div className="d-flex align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faLayerGroup}
                                    />
                                    Group Management
                                </div>
                            }
                            disabled={isLoadingGroups || isDisabledTab}
                        />
                        <Tab
                            eventKey={tabs.INTEGRATION}
                            title={
                                <div className="d-flex align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faPlug}
                                    />
                                    Integration Management
                                </div>
                            }
                            disabled={isLoadingGroups || isDisabledTab}
                        />
                    </Tabs>
                    <Switch>
                        <Route
                            exact
                            path="/control-panel/access-control/integration"
                        >
                            <UnauthorizedAccessControl
                                actionList={{
                                    [`${AccessPermissionModuleNames.USERS}`]: [
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.ListUsers,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.CreateUser,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.GetUser,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.UpdateUser,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.DeleteUser,
                                    ],
                                }}
                                logic={"OR"}
                            >
                                <IntegrationsPage
                                    setIsDisabledTab={setIsDisabledTab}
                                />
                            </UnauthorizedAccessControl>
                        </Route>
                        <Route
                            exact
                            path="/control-panel/access-control/groups"
                        >
                            <UnauthorizedAccessControl
                                actionList={{
                                    [`${AccessPermissionModuleNames.GROUPS}`]: [
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.GROUPS
                                        ].actions.ListGroups,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.GROUPS
                                        ].actions.CreateGroup,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.GROUPS
                                        ].actions.UpdateGroup,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.GROUPS
                                        ].actions.DeleteGroup,
                                    ],
                                }}
                                logic={"OR"}
                            >
                                <GroupManagePage
                                    setIsDisabledTab={setIsDisabledTab}
                                />
                            </UnauthorizedAccessControl>
                        </Route>
                        <Route>
                            <UnauthorizedAccessControl
                                actionList={{
                                    [`${AccessPermissionModuleNames.USERS}`]: [
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.ListUsers,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.CreateUser,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.GetUser,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.UpdateUser,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.DeleteUser,
                                        AccessPermissionModules[
                                            AccessPermissionModuleNames.USERS
                                        ].actions.ResetPassword,
                                    ],
                                }}
                                logic={"OR"}
                            >
                                <UsersPage
                                    setIsDisabledTab={setIsDisabledTab}
                                />
                            </UnauthorizedAccessControl>
                        </Route>
                    </Switch>
                </>
            }
        />
    );
};

export default ControlPanel;
