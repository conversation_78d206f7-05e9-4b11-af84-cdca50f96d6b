import React from "react";
import PropTypes from "prop-types";
import { CreateGroupContextProvider } from "./create/context/CreateGroupContext";
import GroupManagement from "./GroupManagement";

const GroupManagePage = ({ setIsDisabledTab }) => (
    <CreateGroupContextProvider>
        <GroupManagement setIsDisabledTab={setIsDisabledTab} />
    </CreateGroupContextProvider>
);

GroupManagePage.defaultProps = { setIsDisabledTab: () => {} };
GroupManagePage.propTypes = { setIsDisabledTab: PropTypes.func };

export default GroupManagePage;
