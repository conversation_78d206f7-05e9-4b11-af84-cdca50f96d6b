import React, { use<PERSON><PERSON>back, useContext, useMemo } from "react";
import PropTypes from "prop-types";
import { Button, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faPlus } from "FaICIconMap";
import { AddUserPermissionContextProvider } from "Contexts/accessControl/addUserPermissionContext";
import {
    CreateUserContext,
    CreateUserContextProvider,
} from "Components/controlPanel/users/createUser/context/CreateUserContext";
import {
    CreateIntegrationContext,
    CreateIntegrationContextProvider,
} from "../context/IntegrationsContext";
import CreateIntegrationView from "./CreateIntegrationView";
import IntegrationSecretsModal from "../shared/IntegrationSecretsModal";

const CreateIntegrationWizard = ({
    show,
    disabled,
    isCreating,
    isAddingPermissions,
    createIntegrationSuccessResponse,
    isSecretCopied,
    setIsSecretCopied,
    handleShow,
    closeModal,
    onCloseModalAfterIntegrationCreation,
}) => {
    CreateIntegrationWizard.defaultProps = {
        show: false,
        disabled: false,
        isCreating: false,
        isAddingPermissions: false,
        createIntegrationSuccessResponse: null,
        isSecretCopied: false,
        setIsSecretCopied: () => {},
        handleShow: () => {},
        closeModal: () => {},
        onCloseModalAfterIntegrationCreation: () => {},
    };

    CreateIntegrationWizard.propTypes = {
        show: PropTypes.bool,
        disabled: PropTypes.bool,
        isCreating: PropTypes.bool,
        isAddingPermissions: PropTypes.bool,
        createIntegrationSuccessResponse: PropTypes.any,
        isSecretCopied: PropTypes.bool,
        setIsSecretCopied: PropTypes.func,
        handleShow: PropTypes.func,
        closeModal: PropTypes.func,
        onCloseModalAfterIntegrationCreation: PropTypes.func,
    };

    return (
        <>
            <Button
                variant="primary"
                size="sm"
                disabled={disabled || isCreating || isAddingPermissions}
                onClick={handleShow}
            >
                <div className="d-flex align-items-center">
                    <IcIcon className="mr-2" size="lg" icon={faPlus} />
                    Create Integration
                </div>
            </Button>
            <CreateIntegrationView
                show={show}
                onHide={closeModal}
                disabled={disabled}
                isCreating={isCreating}
                isAddingPermissions={isAddingPermissions}
            />
            {createIntegrationSuccessResponse !== null && (
                <IntegrationSecretsModal
                    show={createIntegrationSuccessResponse !== null}
                    clientData={
                        createIntegrationSuccessResponse?.clientData || {}
                    }
                    isSecretCopied={isSecretCopied}
                    setIsSecretCopied={setIsSecretCopied}
                    onHide={onCloseModalAfterIntegrationCreation}
                />
            )}
        </>
    );
};

const CreateIntegrationWizardPage = ({
    isLoading,
    reloadAllIntegrationsDataAndResetPagination,
}) => {
    const { isAddingPermissions } = useContext(CreateUserContext);
    const {
        show,
        setShow,
        isCreating,
        createIntegrationSuccessResponse,
        isSecretCopied,
        setIsSecretCopied = () => {},
        closeIntegrationCreateResponse = () => {},
        reset = () => {},
    } = useContext(CreateIntegrationContext);

    const handleShow = useCallback(() => setShow(true), [setShow]);

    const onHide = useCallback(() => {
        reset();
        setShow(false);
    }, [reset, setShow]);

    const onCloseModalAfterIntegrationCreation = useCallback(() => {
        reloadAllIntegrationsDataAndResetPagination();
        closeIntegrationCreateResponse();
    }, [
        reloadAllIntegrationsDataAndResetPagination,
        closeIntegrationCreateResponse,
    ]);

    return useMemo(
        () => (
            <CreateIntegrationWizard
                show={show}
                handleShow={handleShow}
                closeModal={onHide}
                disabled={isLoading}
                isCreating={isCreating}
                isAddingPermissions={isAddingPermissions}
                createIntegrationSuccessResponse={
                    createIntegrationSuccessResponse
                }
                isSecretCopied={isSecretCopied}
                setIsSecretCopied={setIsSecretCopied}
                onCloseModalAfterIntegrationCreation={
                    onCloseModalAfterIntegrationCreation
                }
            />
        ),
        [
            isLoading,
            show,
            isCreating,
            isAddingPermissions,
            createIntegrationSuccessResponse,
            isSecretCopied,
            setIsSecretCopied,
            handleShow,
            onHide,
            onCloseModalAfterIntegrationCreation,
        ]
    );
};

const CreateIntegration = ({
    isLoading,
    reloadAllIntegrationsDataAndResetPagination,
}) => {
    return (
        <AddUserPermissionContextProvider>
            <CreateUserContextProvider>
                <CreateIntegrationContextProvider>
                    <CreateIntegrationWizardPage
                        isLoading={isLoading}
                        reloadAllIntegrationsDataAndResetPagination={
                            reloadAllIntegrationsDataAndResetPagination
                        }
                    />
                </CreateIntegrationContextProvider>
            </CreateUserContextProvider>
        </AddUserPermissionContextProvider>
    );
};

CreateIntegration.propTypes = {
    isLoading: PropTypes.bool.isRequired,
    reloadAllIntegrationsDataAndResetPagination: PropTypes.func.isRequired,
};

export default CreateIntegration;
