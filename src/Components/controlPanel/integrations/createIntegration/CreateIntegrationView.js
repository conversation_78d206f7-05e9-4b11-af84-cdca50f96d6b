import React, { useMemo } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, Wizard } from "@shoutout-labs/shoutout-themes-enterprise";
import CreateIntegrationFirstPage from "./CreateIntegrationFirstPage";
import CreateIntegrationSecondPage from "./CreateIntegrationSecondPage";
import CreateIntegrationThirdPage from "./CreateIntegrationThirdPage";

const CreateIntegrationView = ({
    show,
    onHide,
    disabled,
    isCreating,
    isAddingPermissions,
}) => {
    const finishButtonText = useMemo(() => {
        if (isCreating) {
            return "Creating Integration...";
        } else if (isAddingPermissions) {
            return "Assigning Permissions...";
        } else {
            return "Create";
        }
    }, [isCreating, isAddingPermissions]);

    return (
        <Modal
            className="create-user border-0"
            show={show}
            onHide={onHide}
            size="xl"
            centered
            backdrop="static"
        >
            <Modal.Header
                closeButton={!(disabled || isCreating || isAddingPermissions)}
            >
                <Modal.Title bsPrefix="modal-title">
                    Create Integration
                </Modal.Title>
            </Modal.Header>
            <Modal.Body className="pt-3 mt-2 px-2">
                <Wizard
                    validate
                    finishButtonClick={() => {}}
                    color="primary"
                    disabled={disabled || isCreating || isAddingPermissions}
                    finishButtonText={finishButtonText}
                    steps={[
                        {
                            stepName: "Basic Information",
                            component: CreateIntegrationFirstPage,
                            showNextBtn: true,
                        },
                        {
                            stepName: "Permissions",
                            component: CreateIntegrationSecondPage,
                            showNextBtn: true,
                        },
                        {
                            stepName: "Summary",
                            component: CreateIntegrationThirdPage,
                            showNextBtn: true,
                        },
                    ]}
                />
            </Modal.Body>
        </Modal>
    );
};

CreateIntegrationView.defaultProps = {
    show: false,
    disabled: false,
    isCreating: false,
    isAddingPermissions: false,
    onHide: () => {},
};

CreateIntegrationView.propTypes = {
    show: PropTypes.bool,
    disabled: PropTypes.bool,
    isCreating: PropTypes.bool,
    isAddingPermissions: PropTypes.bool,
    onHide: PropTypes.func,
};

export default CreateIntegrationView;
