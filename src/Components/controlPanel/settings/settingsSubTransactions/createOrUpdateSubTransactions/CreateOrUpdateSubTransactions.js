import React, { useState, useCallback, useMemo } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Button, Form, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { TransactionTypes } from "Data";
import { createSubTransaction, editSubTransaction } from "Services";
import { isEqualObjects, toTitleCase } from "Utils";
import { OperationTypes } from "../data";

const typesOfTransactions = Object.values(TransactionTypes).map((tT) => ({
    label: toTitleCase(tT),
    value: tT,
}));

const operationTypes = Object.values(OperationTypes).map((oT) => ({
    label: toTitleCase(oT),
    value: oT,
}));

const CreateOrUpdateSubTransactions = ({
    show,
    operation = "CREATE",
    currentData = {},
    onHide,
}) => {
    const [isStateChanging, setIsStateChanging] = useState(false);
    const [subTransactionData, setSubTransactionData] = useState({
        referenceId: currentData?.referenceId || "",
        name: currentData?.name || "",
        description: currentData?.description || "",
    });
    const [transactionType, setTransactionType] = useState([]);
    const [operationType, setOperationType] = useState([]);
    const [validated, setValidated] = useState(false);

    const disableUpdate = useMemo(
        () =>
            operation === "UPDATE" &&
            isEqualObjects(currentData, {
                ...currentData,
                ...subTransactionData,
            }),
        [currentData, operation, subTransactionData]
    );

    const onChange = useCallback(
        (e) =>
            setSubTransactionData({
                ...subTransactionData,
                [e.target.name]: e.target.value,
            }),
        [subTransactionData, setSubTransactionData]
    );

    const onSelectTransactionType = useCallback(
        (e) => {
            setTransactionType(e);

            if (e[0]?.value !== TransactionTypes.ADJUSTMENT) {
                const operationTypeMapForTransactionType =
                    e[0]?.value === TransactionTypes.COLLECTION
                        ? OperationTypes.ADD
                        : OperationTypes.SUBTRACT;
                const defaultOperationTypeForTransactionType =
                    operationTypes.filter(
                        (oT) => oT.value === operationTypeMapForTransactionType
                    );
                setOperationType(defaultOperationTypeForTransactionType);
            } else {
                setOperationType([]);
            }
        },
        [setTransactionType]
    );

    const onSelectOperationType = useCallback(
        (e) => setOperationType(e),
        [setOperationType]
    );

    const onCreateSubTransaction = useCallback(async () => {
        try {
            setIsStateChanging(true);
            await createSubTransaction({
                ...subTransactionData,
                transactionType: transactionType[0]?.value,
                operationType: operationType[0]?.value,
            });
            setIsStateChanging(false);
            toast.success(
                `Successfully created the new sub transaction: "${subTransactionData.name}".`
            );
            onHide(null, "Created");
        } catch (e) {
            setIsStateChanging(false);
            toast.error(
                <div>
                    Failed to create a new sub transaction!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [onHide, subTransactionData, operationType, transactionType]);

    const onUpdateSubTransaction = useCallback(async () => {
        try {
            setIsStateChanging(true);
            await editSubTransaction(currentData?._id, {
                transactionType: currentData?.transactionType,
                operationType: currentData?.operationType,
                ...subTransactionData,
            });
            setIsStateChanging(false);
            toast.success(
                `Successfully updated the sub transaction${
                    subTransactionData.name
                        ? ' "' + subTransactionData.name + '"'
                        : ""
                }.`
            );
            onHide(null, "Updated");
        } catch (e) {
            setIsStateChanging(false);
            toast.error(
                <div>
                    Failed to update the sub transaction
                    {subTransactionData.name
                        ? ' "' + subTransactionData.name + '"'
                        : ""}
                    !
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        currentData?._id,
        currentData?.transactionType,
        currentData?.operationType,
        onHide,
        subTransactionData,
    ]);

    const onSubmit = useCallback(
        async (e) => {
            e.preventDefault();
            if (e.target.checkValidity()) {
                if (operation === "CREATE") {
                    await onCreateSubTransaction();
                } else {
                    await onUpdateSubTransaction();
                }
            } else {
                setValidated(true);
            }
        },
        [operation, onCreateSubTransaction, onUpdateSubTransaction]
    );

    return (
        <Modal show={show} onHide={onHide} size="lg" centered backdrop="static">
            <Modal.Header closeButton={!isStateChanging}>
                <Modal.Title>
                    {toTitleCase(operation)} Sub Transaction
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form onSubmit={onSubmit} validated={validated} noValidate>
                    <Form.Group>
                        <Form.Label className="d-flex align-items-center">
                            Name
                            <div className="ml-1 text-danger">*</div>
                        </Form.Label>
                        <Form.Control
                            name="name"
                            placeholder="Enter sub transaction name..."
                            value={subTransactionData.name}
                            disabled={isStateChanging}
                            onChange={onChange}
                            required
                        />
                        {validated && !subTransactionData.name && (
                            <Form.Text className="text-danger">
                                * Sub transaction name cannot be empty!
                            </Form.Text>
                        )}
                    </Form.Group>
                    <Form.Group>
                        <Form.Label className="d-flex align-items-center">
                            Reference Id
                            <div className="ml-1 text-danger">*</div>
                        </Form.Label>
                        <Form.Control
                            name="referenceId"
                            placeholder="Enter sub transaction reference id..."
                            value={subTransactionData.referenceId}
                            disabled={isStateChanging}
                            onChange={onChange}
                            required
                        />
                        {validated && !subTransactionData.referenceId && (
                            <Form.Text className="text-danger">
                                * Sub transaction reference id cannot be empty!
                            </Form.Text>
                        )}
                    </Form.Group>
                    {operation === "CREATE" && (
                        <>
                            <Form.Group>
                                <Form.Label>
                                    <span>Transaction Type</span>
                                    <span className="ml-1 text-danger">*</span>
                                </Form.Label>
                                <Form.Select
                                    id="transaction-type-selector"
                                    placeholder="Select a transaction type..."
                                    options={typesOfTransactions}
                                    selected={transactionType}
                                    disabled={isStateChanging}
                                    onChange={onSelectTransactionType}
                                    required
                                />
                                {validated && transactionType.length === 0 && (
                                    <Form.Text className="text-danger">
                                        * Transaction type cannot be empty!
                                    </Form.Text>
                                )}
                            </Form.Group>
                            <Form.Group>
                                <Form.Label>
                                    <span>Operation Type</span>
                                    <span className="ml-1 text-danger">*</span>
                                </Form.Label>
                                <Form.Select
                                    id="operation-type-selector"
                                    placeholder="Select an operation type..."
                                    options={operationTypes}
                                    selected={operationType}
                                    disabled={
                                        transactionType[0]?.value !==
                                            TransactionTypes.ADJUSTMENT ||
                                        isStateChanging
                                    }
                                    onChange={onSelectOperationType}
                                    required
                                />
                                {validated && operationType.length === 0 && (
                                    <Form.Text className="text-danger">
                                        * Operation type cannot be empty!
                                    </Form.Text>
                                )}
                            </Form.Group>
                        </>
                    )}
                    <Form.Group>
                        <Form.Label className="d-flex align-items-center">
                            Description
                            <div className="ml-1 text-danger">*</div>
                        </Form.Label>
                        <Form.Control
                            as="textarea"
                            name="description"
                            placeholder="Enter a description for sub transaction....."
                            value={subTransactionData.description}
                            rows={3}
                            disabled={isStateChanging}
                            onChange={onChange}
                            required
                        />
                        {validated && !subTransactionData.description && (
                            <Form.Text className="text-danger">
                                * Sub transaction description cannot be empty!
                            </Form.Text>
                        )}
                    </Form.Group>
                    <div className="text-right mt-5">
                        <Button
                            className="mr-2"
                            variant="outline-primary"
                            size="sm"
                            disabled={isStateChanging}
                            onClick={onHide}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            size="sm"
                            type="submit"
                            disabled={isStateChanging || disableUpdate}
                        >
                            {operation === "CREATE" ? (
                                <>
                                    {isStateChanging
                                        ? "Creating..."
                                        : "Create Sub Transaction"}
                                </>
                            ) : (
                                <>
                                    {isStateChanging
                                        ? "Updating..."
                                        : "Update Sub Transaction"}
                                </>
                            )}
                        </Button>
                    </div>
                </Form>
            </Modal.Body>
        </Modal>
    );
};

CreateOrUpdateSubTransactions.defaultProps = {
    show: false,
    onHide: () => {},
};

CreateOrUpdateSubTransactions.propTypes = {
    show: PropTypes.bool,
    operation: PropTypes.string.isRequired,
    currentData: PropTypes.object.isRequired,
    onHide: PropTypes.func,
};

export default CreateOrUpdateSubTransactions;
