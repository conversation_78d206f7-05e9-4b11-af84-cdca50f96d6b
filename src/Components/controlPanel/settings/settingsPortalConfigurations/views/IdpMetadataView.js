import React, { useContext } from "react";
import PropTypes from "prop-types";
import { Accordion, Card } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { isEmptyObject, toTitleCaseFromCamelCase } from "Utils";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";
import PortalConfigurationsContextAwareToggle from "../shared/PortalConfigurationsContextAwareToggle";

const IdpMetadataView = ({ keyValue, idpMetadata }) => {
    const { isAuthorizedForAction } = useContext(UserContext);

    return (
        <Accordion defaultActiveKey="0">
            <Card key={keyValue} className="my-3 border">
                <Card.Header>
                    <PortalConfigurationsContextAwareToggle
                        eventKey={keyValue}
                        title={keyValue}
                        isAuthorizedForAction={isAuthorizedForAction}
                    />
                </Card.Header>
                <Accordion.Collapse eventKey={keyValue}>
                    <Card.Body>
                        {idpMetadata &&
                        typeof idpMetadata === "object" &&
                        !isEmptyObject(idpMetadata) ? (
                            Object.entries(idpMetadata).map(([key, value]) => (
                                <div className="m-2">
                                    <DetailsAsLabelValue
                                        label={
                                            key
                                                ? toTitleCaseFromCamelCase(key)
                                                : "~ unknown"
                                        }
                                        value={value || "~ unknown"}
                                    />
                                </div>
                            ))
                        ) : (
                            <div className="font-weight-bold text-center p-3 rounded grey-bg">
                                No idp metadata found.
                            </div>
                        )}
                    </Card.Body>
                </Accordion.Collapse>
            </Card>
        </Accordion>
    );
};

IdpMetadataView.defaultProps = { keyValue: "", idpMetadata: {} };

IdpMetadataView.propTypes = {
    keyValue: PropTypes.string,
    idpMetadata: PropTypes.object,
};

export default IdpMetadataView;
