import React, {useContext} from "react";
import { Card, Col, IcIcon, Row, SubHeading} from "@shoutout-labs/shoutout-themes-enterprise";
import BaseLayout from "../../../../Layout/BaseLayout";
import {UserContext} from "Contexts";
import {faAddressCard} from "FaICIconMap";
import "./SettingsNotifications.scss"
const SettingsNotifications =()=>{
    const {organization} = useContext(UserContext);
    return(
        <BaseLayout
            topLeft={<SubHeading text="Notifications" />}
            bottom={
                     <>
                         <Card className="settings-notifications">
                             <Card.Body>
                                 <div  className=" d-flex flex-row align-items-center">
                                     <div className="circles-wrapper">
                                         <IcIcon style={{color:"#00355F"}}  icon={faAddressCard} />
                                     </div>
                                     <SubHeading  text="Default From Address" />
                                 </div>
                                 <br/>
                                 {organization.regions.length!==0?
                                     Object.keys(organization.regions).map(function(key) {
                                         return <>
                                             <Card>
                                                 <Card.Header>
                                                     <Row className='p-3'>
                                                         <Col>
                                                             <Row>
                                                                 <div className="notifications-img-wrapper-div">
                                                                     <img
                                                                         src={organization.regions[key].regionIconUrl}
                                                                         className="notifications-img-width"
                                                                         alt="no"
                                                                     />
                                                                 </div>
                                                                 <div className="d-flex flex-row align-items-center ml-3"  >
                                                                     <span className="mr-1 font-weight-bold">{organization.regions[key].regionName}</span>
                                                                 </div>
                                                             </Row>
                                                         </Col>
                                                         <div className="border-left"/>
                                                         <Col>
                                                             <p>Default Email From Address</p>
                                                             <span> {organization.regions[key].notificationConfiguration.emailConfiguration.fromAddress}</span>
                                                         </Col>
                                                         <div className="border-left"/>
                                                         <Col>
                                                             <p>Default SMS From Address </p>
                                                             <span>{organization.regions[key].notificationConfiguration.smsConfiguration.alphanumericSenderId}</span>
                                                         </Col>
                                                         <div className="border-left"/>
                                                         <Col>
                                                             <p>Default SMS From Number </p>
                                                             <span>{organization.regions[key].notificationConfiguration.smsConfiguration.phoneNumber}</span>
                                                         </Col>
                                                     </Row>
                                                 </Card.Header>
                                             </Card>
                                            <br/>
                                         </>
                                     })
                                     :<p className="text-center">default from address not found</p>
                                 }
                             </Card.Body>
                         </Card>
                     </>

            }
        />
    )
}

export default SettingsNotifications
