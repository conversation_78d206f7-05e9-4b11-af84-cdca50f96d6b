import React, { useCallback, useContext, useState } from "react";
import { toast } from "react-toastify";
import cronstrue from "cronstrue";
import {
    Accordion,
    Badge,
    Button,
    SubHeading,
    Card,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { getAllAffinityGroups } from "Services";
import BaseLayout from "Layout/BaseLayout";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";
import PopUpWindow from "../../shared/popUpWindow/PopUpWindow";
import ContextAwareToggle from "../shared/ContextAwareToggle";

const SettingsMembers = () => {
    const {
        isLoadingOrganization,
        organization,
        loadOrganization,
        isAuthorizedForAction,
    } = useContext(UserContext);
    const [isLoadingAffinityGroups, setIsLoadingAffinityGroups] =
        useState(false);
    const [affinityGroups, setAffinityGroups] = useState([]);
    const [show, setShow] = useState(false);
    const [popUpState, setPopUpState] = useState(null);
    const [showEditAffinityGroup, setShowEditAffinityGroup] = useState(false);

    const onAccordionClick = useCallback(
        async (regionId) => {
            const region = organization.regions.find((r) => r._id === regionId);

            try {
                setIsLoadingAffinityGroups(true);
                const affinityGroupsRes = await getAllAffinityGroups({
                    regionId,
                });
                setAffinityGroups(affinityGroupsRes);
                setIsLoadingAffinityGroups(false);
            } catch (e) {
                setIsLoadingAffinityGroups(false);
                console.error(e);
                toast.error(
                    <div>
                        Failed to load affinity groups for region "
                        {region?.regionName || "~ unknown"}"!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [organization.regions, setIsLoadingAffinityGroups, setAffinityGroups]
    );

    const getDefaultAffinityGroup = useCallback(
        (isLoading, defaultAffinityGroupId) => {
            if (isLoading) return "Loading...";
            return (
                affinityGroups.find((aG) => aG?._id === defaultAffinityGroupId)
                    ?.name || "~ unknown"
            );
        },
        [affinityGroups]
    );

    const onEditMemberAffinityGroupClick = useCallback(
        async (event) => {
            event.stopPropagation();
            event.preventDefault();
            const region =
                organization.regions.find(
                    (r) => r._id === event.currentTarget.id
                ) || {};
            setPopUpState({
                modalHeaderName: `Edit Members Affinity Group Settings`,
                renderingComponentName: "EDIT_MS",
                secondaryAccountsInfo: region,
                modalFooterVisibility: false,
            });
            setShowEditAffinityGroup(true);
        },
        [setShowEditAffinityGroup, setPopUpState, organization.regions]
    );

    const handleCloseST = useCallback(() => {
        setShow(false);
    }, [setShow]);

    const handleCloseAffinityGroupSettings=useCallback(()=>{
        setShowEditAffinityGroup(false);
    },[setShowEditAffinityGroup])

    const onClickEditSecondaryAccountsSettings = useCallback((event) => {
        event.stopPropagation();
        event.preventDefault();
        const region =
            organization.regions.find(
                (r) => r._id === event.currentTarget.id
            ) || {};
        setPopUpState({
            modalHeaderName: `Edit Members Secondary Accounts Settings`,
            renderingComponentName: "EDIT_MSA",
            secondaryAccountsInfo: region,
            modalFooterVisibility: false,
        });
        setShow(true);
    }, [setShow, setPopUpState, organization.regions]);

    return (
        <BaseLayout
            topLeft={<SubHeading text="Members" />}
            bottom={
                <>
                    <Accordion defaultActiveKey="0" className="validity-limits">
                        {organization.regions.length !== 0 ? (
                            Object.keys(organization.regions).map((key) => {
                                const jobEnabled =
                                    organization.regions[key]
                                        ?.memberConfiguration
                                        ?.affinityGroupExpiryJobEnabled ||
                                    false;
                                const jobFrequency =
                                    organization.regions[key]
                                        ?.memberConfiguration
                                        ?.affinityGroupExpiryJobFrequency ||
                                    null;
                                const jobFrequencyToReadableFormat =
                                    jobFrequency
                                        ? cronstrue.toString(jobFrequency)
                                        : "~ unknown";
                                const defaultAffinityGroupId =
                                    organization.regions[key]
                                        .memberConfiguration
                                        .defaultAffinityGroupId || "~ unknown";

                                return (
                                    <Card
                                        key={organization.regions[key]._id}
                                        className="my-4 border"
                                    >
                                        <Card.Header>
                                            <ContextAwareToggle
                                                eventKey={
                                                    organization.regions[key]
                                                        ._id
                                                }
                                                region={
                                                    organization.regions[key]
                                                        .regionName
                                                }
                                                flags={
                                                    organization.regions[key]
                                                        .regionIconUrl
                                                }
                                                isAuthorizedForAction={
                                                    isAuthorizedForAction
                                                }
                                                disabled={
                                                    isLoadingOrganization ||
                                                    isLoadingAffinityGroups
                                                }
                                                // onEdit={
                                                //     onEditMemberSettingClick
                                                // }
                                                onAccordionClick={
                                                    onAccordionClick
                                                }
                                            />
                                        </Card.Header>
                                        <Accordion.Collapse
                                            eventKey={
                                                organization.regions[key]._id
                                            }
                                        >
                                            <Card.Body>
                                                <div>
                                                    <div className="d-flex justify-content-between">
                                                        <h3>Affinity Group</h3>
                                                        <Button
                                                            size="sm"
                                                            variant="primary"
                                                            onClick={
                                                                onEditMemberAffinityGroupClick
                                                            }
                                                            id={organization.regions[key]._id}
                                                        >
                                                            Edit
                                                        </Button>
                                                    </div>
                                                    <div className="mb-3 mx-3">
                                                        <DetailsAsLabelValue
                                                            label="Default Affinity Group"
                                                            value={getDefaultAffinityGroup(
                                                                isLoadingAffinityGroups,
                                                                defaultAffinityGroupId
                                                            )}
                                                        />
                                                    </div>
                                                    <div className="pb-3 d-flex justify-content-between align-items-center">
                                                        <div
                                                            className={`mx-3 ${
                                                                jobEnabled
                                                                    ? "w-50"
                                                                    : "w-100"
                                                            }`}
                                                        >
                                                            <DetailsAsLabelValue
                                                                label="Affinity Group Expiry Job"
                                                                value={
                                                                    <Badge
                                                                        className="px-3 py-2"
                                                                        variant={
                                                                            organization
                                                                                .regions[
                                                                                key
                                                                            ]
                                                                                ?.memberConfiguration
                                                                                ?.affinityGroupExpiryJobEnabled
                                                                                ? "success"
                                                                                : "warning"
                                                                        }
                                                                    >
                                                                        {organization
                                                                            .regions[
                                                                            key
                                                                        ]
                                                                            ?.memberConfiguration
                                                                            ?.affinityGroupExpiryJobEnabled
                                                                            ? "Enabled"
                                                                            : "Disabled"}
                                                                    </Badge>
                                                                }
                                                            />
                                                        </div>
                                                        {jobEnabled && (
                                                            <div className="mx-3 w-50">
                                                                <DetailsAsLabelValue
                                                                    label="Affinity Group Expiry Job Frequency"
                                                                    value={
                                                                        jobFrequencyToReadableFormat
                                                                    }
                                                                />
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                                <hr />
                                                <div>
                                                    <div className="d-flex justify-content-between">
                                                        <h3>
                                                            Secondary Accounts
                                                        </h3>
                                                        <Button
                                                            size="sm"
                                                            variant="primary"
                                                            onClick={onClickEditSecondaryAccountsSettings}
                                                            id={organization.regions[key]._id}
                                                        >
                                                            Edit
                                                        </Button>
                                                    </div>

                                                    <div className="mx-3">
                                                        <DetailsAsLabelValue
                                                            label="Maximum Secondary Accounts per Primary Account"
                                                            value={
                                                                organization
                                                                    .regions[
                                                                    key
                                                                ]
                                                                    .memberConfiguration
                                                                    .maxSecondaryAccounts ||
                                                                "0"
                                                            }
                                                        />
                                                    </div>
                                                </div>
                                            </Card.Body>
                                        </Accordion.Collapse>
                                    </Card>
                                );
                            })
                        ) : (
                            <h3 className="border-top pt-3 text-center">
                                Region data not found.
                            </h3>
                        )}
                    </Accordion>
                    {showEditAffinityGroup ? (
                        <PopUpWindow
                            handleClose={handleCloseAffinityGroupSettings}
                            show={showEditAffinityGroup}
                            popUpState={popUpState}
                            refreshEditedItem={loadOrganization}
                        />
                    ) : null}

{show ? (
                       <PopUpWindow
                       handleClose={handleCloseST}
                       show={show}
                       popUpState={popUpState}
                       refreshEditedItem={loadOrganization}
                   />
                    ) : null}
                </>
            }
        />
    );
};
export default SettingsMembers;
