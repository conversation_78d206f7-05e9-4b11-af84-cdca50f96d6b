import React, { useCallback, useContext, useState } from "react";
import { AccordionContext, useAccordionToggle } from "react-bootstrap";
import {
    Accordion,
    Badge,
    Button,
    Card,
    Col,
    IcIcon,
    OverlayTrigger,
    Row,
    SubHeading,
    Tooltip,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faAngleDown, faAngleUp, faPen } from "FaICIconMap";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import BaseLayout from "Layout/BaseLayout";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";
import PopUpWindow from "Components/controlPanel/shared/popUpWindow/PopUpWindow";
import { LoadingComponent } from "Components/utils";
import { EditConfigurationType } from "../data";

const ContextAwareToggle = ({
    eventKey,
    isAuthorizedForAction,
    callback,
    onEdit,
    onAccordionClick = () => {},
}) => {
    const currentEventKey = useContext(AccordionContext);

    const decoratedOnClick = useAccordionToggle(eventKey, () => {
        currentEventKey !== eventKey && onAccordionClick(eventKey);
        return callback ? callback(eventKey) : () => {};
    });

    const isCurrentEventKey = currentEventKey === eventKey;

    return (
        <Row className="cursor-pointer" onClick={decoratedOnClick}>
            <Col>
                <h3 className="mb-0">"{eventKey}" Webhook Configuration</h3>
            </Col>
            <Col className="d-flex justify-content-end align-items-center">
                {isCurrentEventKey &&
                    onEdit !== undefined &&
                    (isAuthorizedForAction(
                        AccessPermissionModuleNames.ORGANIZATION,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.ORGANIZATION
                        ].actions.UpdateOrganization
                    ) ? (
                        <Button
                            className="mr-3"
                            variant="primary"
                            name={eventKey}
                            size="sm"
                            id={eventKey}
                            onClick={onEdit}
                        >
                            <IcIcon className="mr-2" size="lg" icon={faPen} />
                            Edit
                        </Button>
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={
                                <Tooltip id="tooltip-disabled">
                                    User does not have permission to edit the
                                    organization.
                                </Tooltip>
                            }
                        >
                            <span className="d-inline-block">
                                <Button
                                    className="mr-3"
                                    variant="primary"
                                    size="sm"
                                    disabled
                                    style={{
                                        pointerEvents: "none",
                                    }}
                                >
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faPen}
                                    />
                                    Edit
                                </Button>
                            </span>
                        </OverlayTrigger>
                    ))}
                <div className="text-primary ml-2">
                    {isCurrentEventKey ? "See Less" : "See More"}
                    <IcIcon
                        className="ml-2"
                        size="lg"
                        icon={isCurrentEventKey ? faAngleUp : faAngleDown}
                    />
                </div>
            </Col>
        </Row>
    );
};

const SettingsWebhookConfigurations = () => {
    const {
        isLoadingOrganization,
        organization,
        loadOrganization,
        isAuthorizedForAction,
    } = useContext(UserContext);
    const [show, setShow] = useState(false);
    const [popUpState, setPopUpState] = useState({});

    const onShowPopup = useCallback(
        (event) => {
            event.stopPropagation();
            setPopUpState({
                submitButtonName: "Update",
                modalHeaderName: `Edit "${event.currentTarget.name}" Webhook Configuration`,
                renderingComponentName: "EDIT_ORGANIZATION_CONFIGURATIONS",
                modalFooterVisibility: false,
                organizationInfo: organization,
                configurationType: EditConfigurationType.WEBHOOK,
                configurationData: {
                    webhookConfigKey: event.currentTarget.name,
                    toastSuccessMsg: "Successfully updated organization's webhook configurations.",
                    toastErrorMsg: "Failed to update organization's webhook configurations!",
                    disabledBtnOverlayText: "Webhook configurations have not changed.",
                },
            });
            setShow(true);
        },
        [setShow, setPopUpState, organization]
    );
    const handleClose = useCallback(() => {
        setShow(false);
    }, [setShow]);

    return (
        <BaseLayout
            topLeft={<SubHeading text="Webhook Configurations" />}
            bottom={
                <div className="border-top pt-3">
                    {isLoadingOrganization ? (
                        <div className="font-weight-bold text-center rounded default-info-box-bg">
                            <LoadingComponent />
                            <h3 className="pb-3">
                                Loading organization configurations...
                            </h3>
                        </div>
                    ) : (
                        <>
                            {organization?.configuration
                                ?.webhookConfiguration &&
                            Object.keys(
                                organization.configuration.webhookConfiguration
                            ).length !== 0 ? (
                                <div>
                                    <Accordion defaultActiveKey="0">
                                        {Object.entries(
                                            organization.configuration
                                                .webhookConfiguration
                                        ).map(([key, value]) => {
                                            return (
                                                <Card
                                                    key={key}
                                                    className="my-3 border"
                                                >
                                                    <Card.Header>
                                                        <ContextAwareToggle
                                                            eventKey={key}
                                                            isAuthorizedForAction={
                                                                isAuthorizedForAction
                                                            }
                                                            onEdit={onShowPopup}
                                                        />
                                                    </Card.Header>
                                                    <Accordion.Collapse
                                                        eventKey={key}
                                                    >
                                                        <Card.Body>
                                                            <div className="m-2">
                                                                <DetailsAsLabelValue
                                                                    label="Status"
                                                                    value={
                                                                        <Badge
                                                                            className="px-3 py-2"
                                                                            variant={
                                                                                value?.status
                                                                                    ? "success"
                                                                                    : "warning"
                                                                            }
                                                                        >
                                                                            {value?.status
                                                                                ? "Enabled"
                                                                                : "Disabled"}
                                                                        </Badge>
                                                                    }
                                                                />
                                                            </div>
                                                            <div className="m-2">
                                                                <DetailsAsLabelValue
                                                                    label="Description"
                                                                    value={
                                                                        value?.description
                                                                    }
                                                                />
                                                            </div>
                                                        </Card.Body>
                                                    </Accordion.Collapse>
                                                </Card>
                                            );
                                        })}
                                    </Accordion>
                                    {show && (
                                        <PopUpWindow
                                            show={show}
                                            size="lg"
                                            popUpState={popUpState}
                                            refreshEditedItem={loadOrganization}
                                            handleClose={handleClose}
                                        />
                                    )}
                                </div>
                            ) : (
                                <div className="font-weight-bold text-center p-3 rounded default-info-box-bg">
                                    <h3 className="mb-0">
                                        No webhook configurations found for this
                                        organization.
                                    </h3>
                                </div>
                            )}
                        </>
                    )}
                </div>
            }
        />
    );
};

export default SettingsWebhookConfigurations;
