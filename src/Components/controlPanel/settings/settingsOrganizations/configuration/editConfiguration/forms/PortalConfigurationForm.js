import React, { useCallback, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    Form,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faPlus, faTimes } from "FaICIconMap";

import "./PortalConfigurationForm.scss";

const AddOrigin = ({
    allowedOrigins,
    disabled,
    setSelectedOrganizationEdit,
}) => {
    AddOrigin.defaultProps = {
        allowedOrigins: [],
        disabled: false,
        setSelectedOrganizationEdit: () => {},
    };

    AddOrigin.propTypes = {
        allowedOrigins: PropTypes.array,
        disabled: PropTypes.bool,
        setSelectedOrganizationEdit: PropTypes.func,
    };

    const [originToAdd, setOriginToAdd] = useState("");
    const [validated, setValidated] = useState(false);

    const onChangeOriginToAdd = useCallback(
        (e) => setOriginToAdd(e.currentTarget.value),
        [setOriginToAdd]
    );

    const onAddOrigin = useCallback(
        (e) => {
            e.preventDefault();
            e.stopPropagation();

            if (e.target.checkValidity()) {
                try {
                    if (allowedOrigins.find((cAO) => cAO === originToAdd))
                        throw new Error("Orgin is already added.");

                    if (!new RegExp("https?://.+").test(originToAdd))
                        throw new Error("Orgin to add is not a valid url.");

                    setSelectedOrganizationEdit((previous) => {
                        return {
                            ...previous,
                            configuration: {
                                ...previous.configuration,
                                portalConfiguration: {
                                    ...previous.configuration
                                        .portalConfiguration,
                                    allowedOrigins: [
                                        ...previous.configuration
                                            .portalConfiguration.allowedOrigins,
                                        originToAdd,
                                    ],
                                },
                            },
                        };
                    });
                    setOriginToAdd("");
                    setValidated(false);
                } catch (e) {
                    console.error(e);
                    toast.error(
                        <div>
                            Failed to add a new origin!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setValidated(true);
            }
        },
        [
            allowedOrigins,
            originToAdd,
            setSelectedOrganizationEdit,
            setOriginToAdd,
            setValidated,
        ]
    );

    return allowedOrigins?.length < 10 ? (
        <div className="portal-configuration-form-view">
            <Form onSubmit={onAddOrigin} validated={validated} noValidate>
                <Form.Group>
                    <Form.Label>Add a New Origin</Form.Label>
                    <div className="d-flex justify-content-between align-items-center">
                        <Form.Control
                            placeholder={`Enter a url to add as an origin and press "Enter" or click "+ Add Origin"`}
                            value={originToAdd}
                            name="originToAdd"
                            disabled={disabled}
                            onChange={onChangeOriginToAdd}
                            pattern="https?://.+"
                            required
                        />
                        <Button
                            className="p-0 shadow-none ml-3 add-origin-btn-width d-flex align-items-center"
                            type="submit"
                            variant="link"
                            size="sm"
                            disabled={disabled}
                        >
                            <IcIcon size="2x" icon={faPlus} />
                            Add Origin
                        </Button>
                    </div>
                    {validated && !originToAdd && (
                        <Form.Text className="text-danger">
                            * Origin value to be added cannot be empty!
                        </Form.Text>
                    )}
                    {validated &&
                        originToAdd &&
                        !new RegExp("https?://.+").test(originToAdd) && (
                            <Form.Text className="text-danger">
                                * Origin value to be added must be a valid url!
                            </Form.Text>
                        )}
                </Form.Group>
            </Form>
        </div>
    ) : null;
};

const PortalConfigurationForm = ({
    validated,
    isUpdating,
    allowedOrigins,
    setSelectedOrganizationEdit,
}) => {
    const onRemoveOrigin = useCallback(
        (e) => {
            const originToRemove = e.currentTarget.value || "";
            try {
                setSelectedOrganizationEdit((previous) => {
                    const updatedOrigins =
                        previous.configuration.portalConfiguration.allowedOrigins.filter(
                            (pO) => pO !== originToRemove
                        );

                    return {
                        ...previous,
                        configuration: {
                            ...previous.configuration,
                            portalConfiguration: {
                                ...previous.configuration.portalConfiguration,
                                allowedOrigins: updatedOrigins,
                            },
                        },
                    };
                });
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to remove the origin "{originToRemove}"!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [setSelectedOrganizationEdit]
    );

    return (
        <Form.Group>
            <Form.Label>
                Allowed Origins
                <span className="text-danger">*</span>
            </Form.Label>
            <Form.Group>
                {allowedOrigins.length !== 0 ? (
                    <div className="border rounded">
                        {allowedOrigins.map((aO, index, self) => (
                            <div
                                key={aO}
                                className={`w-100 ${
                                    index === self.length - 1
                                        ? ""
                                        : "border-bottom"
                                } px-3 py-2 ${
                                    index % 2 !== 0 ? "grey-bg" : ""
                                } d-flex justify-content-between align-items-center`}
                            >
                                <div className="d-flex align-items-start">
                                    <div className="mr-2">{index + 1}.</div>
                                    {aO}
                                </div>
                                <Button
                                    key={aO}
                                    className="shadow-none text-danger m-0 p-0"
                                    variant="link"
                                    size="sm"
                                    value={aO}
                                    disabled={isUpdating}
                                    onClick={onRemoveOrigin}
                                >
                                    <IcIcon size="2x" icon={faTimes} />
                                </Button>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="font-weight-bold text-center border rounded grey-bg p-3">
                        Please add 1 or up to 10 origins.
                    </div>
                )}
                {validated && allowedOrigins.length === 0 && (
                    <Form.Text className="text-danger">
                        * Allowed origins cannot be empty!
                    </Form.Text>
                )}
            </Form.Group>
        </Form.Group>
    );
};

PortalConfigurationForm.defaultProps = {
    validated: false,
    isUpdating: false,
    allowedOrigins: [],
    setSelectedOrganizationEdit: () => {},
};

PortalConfigurationForm.propTypes = {
    validated: PropTypes.bool,
    isUpdating: PropTypes.bool,
    allowedOrigins: PropTypes.array,
    setSelectedOrganizationEdit: PropTypes.func,
};

export { AddOrigin };
export default PortalConfigurationForm;
