import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { useHistory, useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import {
    Avatar,
    Button,
    Col,
    IcIcon,
    FormSearch,
    Heading,
    Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faCalendar, faCoins, faListAlt, faArchive, faUser } from "FaICIconMap";
import { MembersContextProvider, UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules, MembersContextCaller } from "Data";
import { archiveCharity, getCharities, getCharitiesCount } from "Services";
import { formatToCommonReadableFormat, toTitleCase } from "Utils";
import BaseLayout from "Layout/BaseLayout";
import PopUpWindow from "./shared/popUpWindow/PopUpWindow";
import SharedTable from "./shared/sharedTableView/SharedTableView";
import EnrollMember from "../members/enrollMember/EnrollMember";
import { CreateMemberContextProvider } from "../members/shared/enrollMemberWizard/context/EnrollMembersContext";
import UnauthorizedAccessControl from "../utils/unauthorizedAccessControl/UnauthorizedAccessControl";

const nameIconTemplate = ({ name, icon }) => (
    <div className="d-flex align-items-center">
        {icon && <IcIcon className="mr-2 text-black" size="lg" icon={icon} />}
        <div className="mt-1">{toTitleCase(name)}</div>
    </div>
);
const nameRow = ({ name, image }) => {
    return (
        <div className="name-container d-flex flex-row align-items-center">
            <Avatar name={!image && (name || "U")} size="md" src={image} />

            <div className="ml-3">
                <div>{name}</div>
            </div>
        </div>
    );
};
const columns = [
    {
        dataField: "charity",
        text: nameIconTemplate({ name: "Charity", icon: faUser }),
        headerStyle: { width: "20%" },
    },
    {
        dataField: "mobileNumber",
        text: nameIconTemplate({ name: "Contact Number", icon: faListAlt }),
    },
    {
        dataField: "points",
        text: nameIconTemplate({ name: "Points", icon: faCoins }),
    },
    {
        dataField: "registeredOn",
        text: nameIconTemplate({ name: "Date", icon: faCalendar }),
    },
    {
        dataField: "createdBy",
        text: nameIconTemplate({ name: "Created By", icon: faCalendar }),
    },
    {
        dataField: "archive",
    },
];
const defaultSkip = 1,
    defaultLimit = 25;
let searchStateUpdateTimeout;

const CharityList = () => {
    const [searchText, setSearchText] = useState("");
    const { isAuthorizedForAction } = useContext(UserContext);
    const [isLoading, setIsLoading] = useState(false);
    const { regionId } = useContext(UserContext);
    const [data, setData] = useState([]);
    const [skip, setSkip] = useState(defaultSkip);
    const [limit, setLimit] = useState(defaultLimit);
    const [isSave, setIsSave] = useState(false);
    const [show, setShow] = useState(false);
    const [popUpState, setPopUpState] = useState({
        submitButtonName: "",
        modalHeaderName: "",
        renderingComponentName: "",
        modalFooterVisibility: false,
    });

    const [charityCount, setCharityCount] = useState(0);
    const history = useHistory();
    const location = useLocation();

    const loadCharityList = useCallback(
        async ({ limit, skip, searchKey, countMembers = false }) => {
            try {
                setData([]);
                setIsLoading(true);

                const promises = [
                    (async () => {
                        const charityResponse = await getCharities({
                            skip: (skip - 1) * limit,
                            regionId: regionId,
                            limit,
                            searchKey,
                            projection: [
                                "name",
                                "preferredName",
                                "contact",
                                "points",
                                "registeredOn",
                                "createdBy",
                                "profilePicture",
                            ],
                        });

                        setData(charityResponse.data.items);
                        setIsLoading(false);
                    })(),
                ];
                if (countMembers) {
                    promises.push(
                        (async () => {
                            try {
                                const charityCountResponse =
                                    await getCharitiesCount({
                                        regionId,
                                        searchKey,
                                    });
                                setCharityCount(
                                    charityCountResponse?.count?.count ||
                                        charityCountResponse?.count
                                );
                            } catch (error) {
                                setCharityCount("unknown");
                                throw error;
                            }
                        })()
                    );
                }
                await Promise.all(promises);
            } catch (e) {
                setIsLoading(false);
                toast.error(
                    <div>
                        Failed to load charities
                        {countMembers ? " and charity count" : ""}!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [setIsLoading, regionId, setCharityCount]
    );

    const refreshTable = useCallback(() => {
        loadCharityList({
            skip: skip,
            limit: limit,
            searchKey: searchText,
            countMembers: true,
        });
    }, [loadCharityList, skip, limit, searchText]);

    const onCharityClick = useCallback(
        (event) => {
            event.stopPropagation();
            setPopUpState({
                submitButtonName: "Archive",
                modalHeaderName: "Archive Charity",
                renderingComponentName: "ARCHIVE_CHARITY",
                modalFooterVisibility: true,
                _id: event.currentTarget.id,
            });
            setShow(true);
        },
        [setShow]
    );

    const onArchiveCharity = useCallback(
        async (event) => {
            event.preventDefault();
            try {
                setIsSave(true);
                await archiveCharity(popUpState._id);
                setShow(false);
                loadCharityList({
                    skip: skip,
                    limit: limit,
                    searchKey: searchText,
                    countMembers: true,
                });
                setIsSave(false);
                toast.success("Successfully Archive Charity");
            } catch (e) {
                setIsSave(false);
                setShow(false);
                toast.error(e.message);
            }
        },
        [skip, limit, searchText, popUpState._id, setIsSave, loadCharityList]
    );

    const tableData = useMemo(
        () =>
            data.map((charity) => {
                charity["archive"] = (
                    <Button
                        onClick={onCharityClick}
                        name="ARCHIVE_CHARITY"
                        id={charity._id}
                        size="sm"
                        variant="outline-danger"
                    >
                        <div className="d-flex align-items-center">
                            <IcIcon
                                size="lg"
                                className="mr-2"
                                icon={faArchive}
                            />
                            Archive
                        </div>
                    </Button>
                );
                charity["charity"] = nameRow({
                    name: charity.preferredName,
                    image: charity.profilePicture,
                });
                charity["registeredOn"] = formatToCommonReadableFormat(
                    charity.registeredOn
                );
                return charity;
            }),
        [data, onCharityClick]
    );

    const setSearch = useCallback(
        (searchText) => {
            if (searchStateUpdateTimeout) {
                clearTimeout(searchStateUpdateTimeout);
            }
            setSearchText(searchText);

            searchStateUpdateTimeout = setTimeout(async () => {
                loadCharityList({
                    skip: skip,
                    limit: limit,
                    searchKey: searchText,
                    countMembers: true,
                });
            }, 2000);
        },
        [setSearchText, loadCharityList, skip, limit]
    );
    const handleClose = useCallback(() => {
        setShow(false);
    }, [setShow]);

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadCharityList({
                skip: newSkip,
                limit: limit,
                searchKey: searchText,
                countMembers: false,
            });
        },
        [setSkip, limit, loadCharityList, searchText]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadCharityList({
                skip: defaultSkip,
                limit: newLimit,
                searchKey: searchText,
                countMembers: false,
            });
        },
        [loadCharityList, setSkip, setLimit, searchText]
    );

    const onRowClick = useCallback(
        (rowData) => {
            if (
                isAuthorizedForAction(
                    AccessPermissionModuleNames.CHARITIES,
                    AccessPermissionModules[
                        AccessPermissionModuleNames.CHARITIES
                    ].actions.GetCharity
                )
            ) {
                history.push(
                    `${location.pathname}/charity-profile/${rowData._id}`
                );
            }
        },
        [isAuthorizedForAction, history, location.pathname]
    );
    const tableRowEvents = {
        onClick: (e, row) => {
            onRowClick(row);
        },
    };

    useEffect(() => {
        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.CHARITIES,
                AccessPermissionModules[AccessPermissionModuleNames.CHARITIES]
                    .actions.ListCharities
            )
        ) {
            loadCharityList({
                limit,
                skip: defaultSkip,
                countMembers: true,
            });
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <BaseLayout
            topLeft={<Heading text="Charity List" />}
            bottom={
                <div>
                    <br />
                    <Row>
                        <Col sm={5}>
                            <UnauthorizedAccessControl
                                actionList={{
                                    [`${AccessPermissionModuleNames.CHARITIES}`]:
                                        [
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames
                                                    .CHARITIES
                                            ].actions.ListCharities,
                                        ],
                                }}
                                logic={"OR"}
                                renderEmpty={true}
                            >
                                <FormSearch
                                    placeholder="Search"
                                    selected={searchText}
                                    onChange={setSearch}
                                    id="search-cards"
                                />
                            </UnauthorizedAccessControl>
                        </Col>
                        <Col>
                            <UnauthorizedAccessControl
                                actionList={{
                                    [`${AccessPermissionModuleNames.CHARITIES}`]:
                                        [
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames
                                                    .CHARITIES
                                            ].actions.CreateCharity,
                                        ],
                                }}
                                logic={"OR"}
                                renderEmpty={true}
                            >
                                <div className="float-right">
                                    <EnrollMember
                                        action="charity"
                                        refreshTable={refreshTable}
                                    />
                                </div>
                            </UnauthorizedAccessControl>
                        </Col>
                    </Row>
                    <br />
                    <UnauthorizedAccessControl
                        actionList={{
                            [`${AccessPermissionModuleNames.CHARITIES}`]: [
                                AccessPermissionModules[
                                    AccessPermissionModuleNames.CHARITIES
                                ].actions.ListCharities,
                            ],
                        }}
                        logic={"OR"}
                    >
                        <SharedTable
                            columns={columns}
                            data={tableData}
                            sizePerPage={limit}
                            page={skip}
                            rowEvents={tableRowEvents}
                            onChangePagination={onChangePagination}
                            onChangePageSize={onChangePageSize}
                            isLoading={isLoading}
                            totalCount={charityCount}
                        />
                    </UnauthorizedAccessControl>
                    {show ? (
                        <PopUpWindow
                            detailsSubmitHandler={onArchiveCharity}
                            show={show}
                            popUpState={popUpState}
                            handleClose={handleClose}
                            isSave={isSave}
                        />
                    ) : null}
                </div>
            }
        />
    );
};

const Charity = () => {
    return (
        <MembersContextProvider mainCaller={MembersContextCaller.MEMBERS_VIEW}>
            <CreateMemberContextProvider>
                <CharityList />
            </CreateMemberContextProvider>
        </MembersContextProvider>
    );
};
export default Charity;
