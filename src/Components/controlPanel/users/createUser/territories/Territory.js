import React, { useCallback, useContext, useEffect, useState } from "react";
import { UserContext } from "Contexts";
import { Button, Card, DropdownItem, IcIcon, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { faPlus, faEllipsisV, faTimesCircle } from 'FaICIconMap';
import Permissions from "./Permissions";
import { CreateUserContext } from "../context/CreateUserContext";
import { DropdownButton } from "react-bootstrap";

const Territory = ({ key, number, removeTerritory }) => {
    const { organization } = useContext(UserContext);
    const { 
        // territoryData, 
        regionName, 
        merchant, 
        location, 
        group, 
        setPageValue 
    } = useContext(CreateUserContext);
    const [permissions, setPermissions] = useState([]);
    const [permissionCount, setPermissionCount] = useState(0);

    const addPermission = useCallback(() => setPermissionCount(permissionCount + 1), [permissionCount]);
    const deleteTerritory = useCallback(() => {
        removeTerritory(number)
        setPermissions([]);
        setPermissionCount();
    }, [number, removeTerritory]);
    const onChangeHandler = useCallback((e) => setPageValue(e), [setPageValue]);

    useEffect(() => {
        const arrayPerm = [];

        for (var i=1; i <= permissionCount; i+=1) {
            arrayPerm.push(
                <Permissions 
                    key={i} 
                    number={i} 
                    organization={organization} 
                    onChangeHandler={onChangeHandler}
                    merchant={merchant}
                    location={location}
                    group={group}
                />
            );
            setPermissions(arrayPerm);
        }
    }, [permissionCount, organization, merchant, location, group, onChangeHandler]);
    
    return (
        <div className="mt-4 text-left">
            <Card>
                <Card.Body>
                    <Form>
                        <Form.Group >
                            <div className="d-flex flex-row">
                                <div>
                                    <Form.Control
                                        as="select"
                                        placeholder="Select"
                                        name="regionName"
                                        value={regionName}
                                        onChange={onChangeHandler}
                                        required
                                    >
                                        <option value="" disabled className="text-muted">
                                        Select
                                        </option>
                                        {organization.regions
                                        ? organization.regions.map(({regionName,_id}) => {
                                            return (
                                                <option value={regionName} key={_id} id={_id}>
                                                {regionName}
                                                </option>
                                            );
                                            })
                                        : null}
                                    </Form.Control>
                                </div>
                                <div className="border-solid-right px-3 point-div mt-1 ml-2">
                                    <DropdownButton 
                                        data-testid="territory-dropdown" 
                                        bsPrefix="dropdown-btn single-dropdown-toggle" 
                                        title={<IcIcon className="text-primary" size="w-10" icon={faEllipsisV} />} 
                                    >
                                        <DropdownItem
                                            eventKey="Delete Account"
                                            key="Delete Account"
                                            className="text-danger"
                                            onClick={deleteTerritory}
                                        >
                                            <IcIcon className="mr-2" size="w-16" icon={faTimesCircle} />
                                            <span>Delete</span>
                                        </DropdownItem>
                                    </DropdownButton>
                                </div>
                            </div>
                        </Form.Group>
                        <Form.Group>
                            {permissionCount !== 0 ? permissions 
                            :
                            <div className="mt-3 text-center">
                                <p>No permissions specified.</p>
                            </div>
                            }
                        </Form.Group>
                        <Button size="sm" variant="link" onClick={addPermission}>
                            <IcIcon className="mr-2" size="w-10" icon={faPlus} /> Add Permission
                        </Button>
                    </Form>
                </Card.Body>
            </Card>
        </div>
    );
};

export default Territory;