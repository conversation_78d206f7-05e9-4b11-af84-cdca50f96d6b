import React, { useCallback, useEffect, useState, useContext } from "react";
import PropTypes from "prop-types";
import { Modal, Button, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { toast } from "react-toastify";
import { updateIdentityUser } from "Services";
import { isEmptyObject } from "Utils";
import { UserContext } from "Contexts";

const EditMobileNumber = ({
  defaultCountry,
  show,
  onHide,
  currentDetails,
  userId,
}) => {
  const [validated, setValidated] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [mobileNumber, setMobileNumber] = useState("");
  const [isValidMobileNumber,setIsValidMobileNumber]=useState(false);
  const onSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      if (e.target.checkValidity()&&mobileNumber&&isValidMobileNumber) {
        try {
          setIsCreating(true);
          const updateResponse = await updateIdentityUser(userId, {
            userData: { contact:{mobileNumber: mobileNumber} },
          });
          setIsCreating(false);
          toast.success("Update details successfully.");
          onHide(null, updateResponse);
        } catch (e) {
          setIsCreating(false);
          toast.error(
            "Could not update the contact number. Please recheck and try again."
          );
        }
      } else {
        setValidated(true);
      }
    },
    [setValidated, onHide, setIsCreating, mobileNumber, userId,isValidMobileNumber]
  );

  const onChangeMobileNumber = useCallback(
    (status, value, countryData, number) => {
      console.debug("Status:",status)
      const mobile = number.replace(/[^0-9]/gi, "");
      setMobileNumber(mobile);
      setIsValidMobileNumber(status);
    },
    [setMobileNumber,setIsValidMobileNumber]
  );
  useEffect(() => {
    if (!isEmptyObject(currentDetails)) {
      if (
        currentDetails.mobileNumber &&
        !currentDetails.mobileNumber.startsWith("+")
      ) {
        setMobileNumber("+" + currentDetails.mobileNumber);
      } else {
        setMobileNumber(currentDetails.mobileNumber);
      }
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentDetails]);

  console.debug("mobile:",mobileNumber!==""&&isValidMobileNumber)
  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>Edit Contact Number</Modal.Title>
      </Modal.Header>
      <Form onSubmit={onSubmit} validated={validated} noValidate>
        <Modal.Body>
          <Form.Group>
            <Form.Label>Contact No.</Form.Label>
            <Form.MobileNumberInput
              defaultCountry={defaultCountry}
              onPhoneNumberBlur={onChangeMobileNumber}
              onPhoneNumberChange={onChangeMobileNumber}
              format={true}
              formatOnInit={true}
              defaultValue={mobileNumber}
              inputClassName={`form-control input-control form-control-sm ${validated?((mobileNumber!==""&&isValidMobileNumber)?"is-valid":"is-invalid"):""}`}
              containerClassName="intl-tel-input w-100"
            />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button
            size="sm"
            variant="outline-primary"
            onClick={onHide}
            type="button"
            disabled={isCreating}
          >
            Cancel
          </Button>
          <Button
            size="sm"
            variant="primary"
            type="submit"
            disabled={isCreating}
          >
            {isCreating ? "Updating..." : "Update"}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
EditMobileNumber.defaultProps = {
  defaultCountry: "us",
};
EditMobileNumber.propTypes = {
  /**
   * Show edit view
   */
  show: PropTypes.bool.isRequired,

  /**
   * Callback on close
   */
  onHide: PropTypes.func.isRequired,

  /**
   * current known data
   */

  currentDetails: PropTypes.object,

  /**
   * user Id
   */
  userId: PropTypes.string.isRequired,

  /**
   * default iso code
   */
  defaultCountry: PropTypes.string.isRequired,
};

const EditMobileNumberContainer = (props) => {
  const { selectedRegion } = useContext(UserContext);
  return (
    <EditMobileNumber
      {...props}
      defaultCountry={selectedRegion?.defaultCountryISO2Code?.toLowerCase()}
    />
  );
};
export default EditMobileNumberContainer;
export { EditMobileNumber };
