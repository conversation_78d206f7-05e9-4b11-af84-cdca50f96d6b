import React, { useCallback, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Modal, Button, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { toast } from "react-toastify";

import { isEmptyObject } from "Utils";
import { updateIdentityUser } from "Services";

const EditName = ({ show, onHide, currentDetails ,userId}) => {
  const [validated, setValidated] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  // const [username, setUsername] = useState("");
  const onSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      if (e.target.checkValidity()) {
        try {
          setIsCreating(true);
          const updateResponse=await updateIdentityUser(userId,{userData:{firstName,lastName}});
          setIsCreating(false);
          toast.success("Update details successfully.");
          onHide(null,updateResponse);
        } catch (e) {
          console.error(e);
          setIsCreating(false);
          toast.error("Couldn't update the details. Please recheck the details");
        }
      } else {
        setValidated(true);
      }
    },
    [setValidated, onHide, setIsCreating,firstName,lastName,userId]
  );

  const onChangeFirstName = useCallback(
    (e) => {
      setFirstName(e.target.value);
    },
    [setFirstName]
  );
  const onChangeLastName = useCallback(
    (e) => {
      setLastName(e.target.value);
    },
    [setLastName]
  );

  // const onChangeUsername = useCallback(
  //   (e) => {
  //     setUsername(e.target.value);
  //   },
  //   [setUsername]
  // );

  useEffect(() => {
    if (!isEmptyObject(currentDetails)) {
      setFirstName(currentDetails.firstName);
      setLastName(currentDetails.lastName);
      // setUsername(currentDetails.username);
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentDetails]);
  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>Edit Name</Modal.Title>
      </Modal.Header>
      <Form onSubmit={onSubmit} validated={validated} noValidate>
        <Modal.Body>
          <Form.Group>
            <Form.Label>First Name</Form.Label>
            <Form.Control
              type="text"
              required
              value={firstName}
              onChange={onChangeFirstName}
              minLength={1}
              placeholder="Enter first name"
            />
          </Form.Group>
          <Form.Group>
            <Form.Label>Last Name</Form.Label>
            <Form.Control
              type="text"
              required
              value={lastName}
              onChange={onChangeLastName}
              minLength={1}
              placeholder="Enter last name"
            />
          </Form.Group>

          {/* <Form.Group>
            <Form.Label>Username</Form.Label>
            <Form.Control
              type="text"
              
              value={currentDetails.username}
              // onChange={onChangeUsername}
              minLength={1}
              placeholder="Enter username"
              disabled
            />
          </Form.Group> */}
        </Modal.Body>
        <Modal.Footer>
          <Button
            size="sm"
            variant="outline-primary"
            onClick={onHide}
            type="button"
            disabled={isCreating}
          >
            Cancel
          </Button>
          <Button
            size="sm"
            variant="primary"
            type="submit"
            disabled={isCreating}
          >
            Update
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

EditName.defaultProps = {
  currentDetails: {},
};
EditName.propTypes = {
  /**
   * Show edit view
   */
  show: PropTypes.bool.isRequired,

  /**
   * Callback on close
   */
  onHide: PropTypes.func.isRequired,

  /**
   * current known data
   */

  currentDetails: PropTypes.object,

  /**
   * user Id
   */
  userId:PropTypes.string.isRequired,
};

export default EditName;
