import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    OverlayTrigger,
    Tooltip,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext, UserContext } from "Contexts";
import { AccessControlContext } from "Contexts/accessControl/accessControlContext";
import { AddUserPermissionContextProvider } from "Contexts/accessControl/addUserPermissionContext";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    UserBoundaryType,
} from "Data";
import { useToggle } from "Hooks";
import { getUserPermissions } from "Services";
import { isEmptyObject } from "Utils";
import LoadingComponent from "Components/utils/LoadingComponent";
import AddPermission from "./AddPermissionModal";
import { PermissionViewCardWithEdit } from "./PermissionViewCard";

import "../UserProfile.scss";

const AssignedPermissions = ({
    userId,
    regions,
    merchants,
    groups,
    merchantLocations,
    boundary,
    ...rest
}) => {
    const [permissionDetails, setPermissionDetails] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [showAddPermission, setShowAddPermission] = useToggle(false);
    const { organization, isAuthorizedForAction } = useContext(UserContext);

    const shouldShowAddPermission = useMemo(() => {
        if (boundary === UserBoundaryType.GLOBAL) {
            return true;
        }

        return isEmptyObject(permissionDetails);
    }, [boundary, permissionDetails]);

    const availableRegions = useMemo(() => {
        const currentSelectedRegions = new Set();
        permissionDetails?.forEach(({ regionId }) => {
            currentSelectedRegions.add(regionId);
        });
        return (
            organization.regions?.filter(
                (item) => !currentSelectedRegions.has(item._id)
            ) || []
        );
    }, [organization.regions, permissionDetails]);

    const loadUserPermission = useCallback(async () => {
        try {
            setIsLoading(true);
            const permissionDetailsResponse = await getUserPermissions({
                userId,
            });
            setPermissionDetails(permissionDetailsResponse.items);
        } catch (e) {
            toast.error(
                <div>
                    Failed to load user permission details!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoading(false);
        }
    }, [userId, setIsLoading]);

    const onHideAddPermission = useCallback(
        (e, data) => {
            setShowAddPermission();
            if (data) {
                loadUserPermission();
            }
        },
        [setShowAddPermission, loadUserPermission]
    );

    useEffect(() => {
        if (
            userId &&
            isAuthorizedForAction(
                AccessPermissionModuleNames.USER_PERMISSION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.USER_PERMISSION
                ].actions.ListUserPermissions
            )
        ) {
            loadUserPermission(userId);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userId, isAuthorizedForAction]);

    if (isLoading) {
        return <LoadingComponent />;
    }

    return (
        <AddUserPermissionContextProvider>
            <div>
                <div className="d-flex flex-row justify-content-end">
                    {isAuthorizedForAction(
                        AccessPermissionModuleNames.USER_PERMISSION,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.USER_PERMISSION
                        ].actions.CreateUserPermission
                    ) && shouldShowAddPermission ? (
                        <Button
                            variant="primary"
                            onClick={setShowAddPermission}
                            size="sm"
                        >
                            Add Permission
                        </Button>
                    ) : (
                        <OverlayTrigger
                            placement="bottom"
                            overlay={
                                <Tooltip
                                    id="tooltip-disabled"
                                    className="permission-tooltip"
                                >
                                    User has access only for one region and an
                                    access configuration for that region already
                                    exists. Please edit the existing region
                                    group.
                                </Tooltip>
                            }
                        >
                            <span className="d-inline-block">
                                <Button
                                    variant="primary"
                                    size="sm"
                                    disabled
                                    style={{ pointerEvents: "none" }}
                                >
                                    Add Permission
                                </Button>
                            </span>
                        </OverlayTrigger>
                    )}
                </div>
                {permissionDetails.map(
                    ({ _id, regionId, permissions }, index) => {
                        const regionDetails = regions[regionId] || {};

                        return (
                            <PermissionViewCardWithEdit
                                key={_id || index}
                                id={_id || index}
                                regionDetails={regionDetails}
                                permissions={
                                    permissions && permissions.length !== 0
                                        ? permissions
                                        : []
                                }
                                showEdit={isAuthorizedForAction(
                                    AccessPermissionModuleNames.USER_PERMISSION,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames
                                            .USER_PERMISSION
                                    ].actions.UpdateUserPermission
                                )}
                                merchants={merchants}
                                groups={groups}
                                merchantLocations={merchantLocations}
                                setShowEditPermission={setShowAddPermission}
                            />
                        );
                    }
                )}
                {showAddPermission && (
                    <AddPermission
                        show={showAddPermission}
                        onHide={onHideAddPermission}
                        currentPermissions={permissionDetails}
                        userId={userId}
                        boundary={boundary}
                        regions={availableRegions}
                        showDelete={isAuthorizedForAction(
                            AccessPermissionModuleNames.USER_PERMISSION,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.USER_PERMISSION
                            ].actions.DeleteUserPermission
                        )}
                        {...rest}
                    />
                )}
            </div>
        </AddUserPermissionContextProvider>
    );
};

AssignedPermissions.defaultProps = {
    regions: {},
    merchants: {},
    groups: {},
    boundary: UserBoundaryType.MERCHANT,
};

AssignedPermissions.propTypes = {
    userId: PropTypes.string,
    regions: PropTypes.object,
    merchants: PropTypes.object,
    groups: PropTypes.object,
    merchantLocations: PropTypes.array,
    boundary: PropTypes.string,
};

const AssignedPermissionsContainer = (props) => {
    const { organization } = useContext(UserContext);
    const { merchants, merchantLocations } = useContext(DataContext);
    const { groupList = [] } = useContext(AccessControlContext);

    const regions = useMemo(() => {
        return (
            organization.regions?.reduce((result, region) => {
                if (props.boundaryRegionId) {
                    if (props.boundaryRegionId === region._id) {
                        result[region._id] = {
                            _id: region._id,
                            regionName: region.regionName,
                            regionIconUrl: region.regionIconUrl,
                        };
                    }
                } else {
                    result[region._id] = {
                        _id: region._id,
                        regionName: region.regionName,
                        regionIconUrl: region.regionIconUrl,
                    };
                }

                return result;
            }, {}) || {}
        );
    }, [organization.regions, props.boundaryRegionId]);

    const merchantsObject = useMemo(() => {
        return merchants.reduce((result, item) => {
            result[item._id] = item;
            return result;
        }, {});
    }, [merchants]);

    const groupsObject = useMemo(() => {
        return (
            groupList.reduce((result, item) => {
                result[item._id] = item;
                return result;
            }, {}) || {}
        );
    }, [groupList]);

    const locations = useMemo(() => {
        return (
            Object.values(merchantLocations).reduce((result, item) => {
                result = { ...result, ...item };
                return result;
            }, {}) || {}
        );
    }, [merchantLocations]);
    return (
        <AssignedPermissions
            {...props}
            regions={regions}
            merchants={merchantsObject}
            groups={groupsObject}
            merchantLocations={locations}
        />
    );
};

AssignedPermissionsContainer.propTypes = { boundaryRegionId: PropTypes.string };

export { AssignedPermissions };
export default AssignedPermissionsContainer;
