import React, { useCallback, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Modal, Button, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { AddUserPermissionContext } from "Contexts/accessControl/addUserPermissionContext";
import { UserBoundaryType } from "Data";
import AddPermissionContainer from "./AddPermission";

const AddPermissionModal = ({
    show,
    onHide,
    addRegion,
    permissionSets,
    currentPermissions,
    userId,
    saveUserPermissions,
    mode,
    isLoadingModal,
    setIsLoadingModal,
    ...rest
}) => {
    const [validated, setValidated] = useState(false);
    const [isCreating, setIsCreating] = useState(false);

    const onSubmit = useCallback(
        async (e) => {
            e.preventDefault();
            if (e.target.checkValidity()) {
                try {
                    setIsLoadingModal(true);
                    setIsCreating(true);
                    const addPermissionResponse = await saveUserPermissions(
                        userId,
                        currentPermissions
                    );
                    setIsLoadingModal(false);
                    setIsCreating(false);
                    toast.success("Permission added successfully");
                    onHide(null, addPermissionResponse);
                } catch (e) {
                    toast.error(
                        <div>
                            Failed to add permission!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                    setIsLoadingModal(false);
                    setIsCreating(false);
                }
            } else {
                setValidated(true);
            }
        },
        [
            userId,
            currentPermissions,
            saveUserPermissions,
            onHide,
            setIsLoadingModal,
            setValidated,
            setIsCreating,
        ]
    );

    useEffect(() => {
        if (permissionSets.length === 0) addRegion();
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <Modal show={show} onHide={onHide} size="lg" backdrop="static" centered>
            <Modal.Header closeButton={!(isLoadingModal || isCreating)}>
                <Modal.Title>
                    {mode === "edit" ? "Edit" : "Add"} Permission
                </Modal.Title>
            </Modal.Header>
            <Form onSubmit={onSubmit} validated={validated} noValidate>
                <Modal.Body className="mb-4">
                    {/* <div className="text-right">
            <Button type="button" onClick={addRegion} variant="primary">
              Add Territory
            </Button>
          </div> */}
                    {permissionSets.map((item, index) => (
                        <AddPermissionContainer
                            key={item.index}
                            {...item}
                            index={index}
                            currentPermissions={currentPermissions}
                            {...rest}
                        />
                    ))}
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        size="sm"
                        variant="outline-primary"
                        onClick={onHide}
                        type="button"
                        disabled={isCreating || isLoadingModal}
                    >
                        Cancel
                    </Button>
                    <Button
                        size="sm"
                        variant="primary"
                        type="submit"
                        disabled={isCreating || isLoadingModal}
                    >
                        {mode === "edit"
                            ? isCreating
                                ? "Saving..."
                                : "Save Permission"
                            : isCreating
                            ? "Adding..."
                            : "Add Permission"}
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal>
    );
};

AddPermissionModal.defaultProps = {
    permissionSets: [],
    currentPermissions: [],
    boundary: UserBoundaryType.MERCHANT,
    showDelete: true,
};
AddPermissionModal.propTypes = {
    /**
     * Show edit view
     */
    show: PropTypes.bool.isRequired,

    /**
     * Callback on close
     */
    onHide: PropTypes.func.isRequired,

    /**
     * onClick on add territory
     */
    addRegion: PropTypes.func.isRequired,
    /**
     * permission sets
     */
    permissionSets: PropTypes.array.isRequired,

    /**
     * Existing permission sets
     */
    currentPermissions: PropTypes.array,

    /**
     * on save permissions fn
     */

    saveUserPermissions: PropTypes.func.isRequired,

    /**
     * current user id
     */
    userId: PropTypes.string.isRequired,

    /**
     * current user boundary
     *
     */
    boundary: PropTypes.oneOf([
        UserBoundaryType.GLOBAL,
        UserBoundaryType.REGIONAL,
        UserBoundaryType.MERCHANT,
    ]),

    /**
     * boundary region ID
     */
    boundaryRegionId: PropTypes.string,

    /**
     * boundary merchant ID
     */
    boundaryMerchantId: PropTypes.string,

    /**
     * show delete option
     */
    showDelete: PropTypes.bool,
};

const AddPermissionModalContainer = ({ onHide, ...props }) => {
    const {
        addRegion,
        permissionSets,
        saveUserPermissions,
        mode,
        isLoadingModal,
        setIsLoadingModal,
        reset,
    } = useContext(AddUserPermissionContext);

    const onClose = useCallback(
        (e, data) => {
            reset();
            onHide(e, data);
        },
        [onHide, reset]
    );

    return (
        <AddPermissionModal
            {...props}
            onHide={onClose}
            mode={mode}
            addRegion={addRegion}
            permissionSets={permissionSets}
            saveUserPermissions={saveUserPermissions}
            isLoadingModal={isLoadingModal}
            setIsLoadingModal={setIsLoadingModal}
        />
    );
};
// const AddPermissionModalContainerWrapper = (props) => {
//   return (
//     <AddUserPermissionContextProvider>
//       <AddPermissionModalContainer {...props} />
//     </AddUserPermissionContextProvider>
//   );
// };
export default AddPermissionModalContainer;

export { AddPermissionModal };
