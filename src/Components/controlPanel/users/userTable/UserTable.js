import React, { use<PERSON><PERSON>back, useContext, useMemo, useState } from "react";
import paginationFactory, {
    PaginationProvider,
} from "react-bootstrap-table2-paginator";
import { useHistory } from "react-router-dom";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    IcIcon,
    BootstrapTable,
    Button,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faUser,
    faKey,
    faLockAccess,
    faPower,
    faUserSquare,
    faCalendar,
    faArchive,
} from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    UserBoundaryTypeColorCode,
    UserStatus,
    UserStatusColorCode,
    UserStatusOppositeColorCode,
    UserStatusOppositePresentTense,
} from "Data";
import { deleteUserAccount, updateIdentityUserStatus } from "Services";
import {
    formatToCommonReadableFormat,
    getTruncatedStringWithTooltip,
    isUpperOrSameLevelBoundary,
    toTitleCase,
} from "Utils";
import DeleteItem from "Components/controlPanel/shared/actionModals/DeleteItem";
import UpdateStatus from "Components/controlPanel/shared/actionModals/UpdateStatus";
import { BootstrapTableOverlay } from "Components/utils/UtilComponents";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import NameIconTemplate from "Components/utils/table/NameIconTemplate";
import SizePerPageRenderer from "Components/utils/table/sizePerPageRenderer/SizePerPageRenderer";

const defaultColumnTemplate = ({ name, icon, ...rest }) => ({
    dataField: name,
    text: NameIconTemplate({ name, icon }),
    sort: false,
    ...rest,
});

const defaultCols = [
    {
        name: "username",
        icon: faUserSquare,
        sort: false,
        headerStyle: { width: "20%" },
    },
    {
        name: "first_name",
        icon: faUser,
        sort: false,
    },
    {
        name: "last_name",
        icon: faUser,
        sort: false,
    },
    {
        name: "access_type",
        icon: faKey,
        sort: false,
        headerStyle: { width: "11%" },
    },
    {
        name: "status",
        icon: faPower,
        headerStyle: { width: "10%" },
        sort: false,
    },
    {
        name: "created_on",
        icon: faCalendar,
        sort: false,
        headerStyle: { width: "15%" },
    },
    { name: "_id", hidden: true, sort: false },
    { name: "", icon: null, headerStyle: { width: "20%" }, sort: false },
];

const NoData = ({ loading }) => {
    NoData.defaultProps = { loading: false };
    NoData.propTypes = { loading: PropTypes.bool };

    if (loading) return null;
    return <div>No users found.</div>;
};

const defaultSkip = 1;

const IdUserTable = ({
    usersList,
    isLoading,
    totalCount,
    limit,
    skip,
    searchText,
    appliedFilters,
    setLimit,
    setSkip,
    loadIdUsers,
    reloadAllUsersDataAndResetPagination,
}) => {
    const { isAuthorizedForAction, userBoundaryType } = useContext(UserContext);
    const [selectedUserData, setSelectedUserData] = useState({
        id: "",
        name: "",
    });
    const [status, setStatus] = useState("");
    const [showUpdateStatusModal, setShowUpdateStatusModal] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);
    const [showArchiveModal, setShowArchiveModal] = useState(false);
    const [isArchiving, setIsArchiving] = useState(false);
    const history = useHistory();

    const columns = useMemo(() => {
        const columns = [];
        defaultCols.forEach((item) => {
            columns.push(defaultColumnTemplate(item));
        });
        return columns;
    }, []);

    const onClickUpdateStatus = useCallback(
        (e) => {
            e.stopPropagation();
            setSelectedUserData({
                id: e.currentTarget.dataset.id,
                name: e.currentTarget.dataset.name || "",
            });
            setStatus(e.currentTarget.dataset.status);
            setShowUpdateStatusModal(true);
        },
        [setSelectedUserData, setShowUpdateStatusModal, setStatus]
    );

    const onClickArchive = useCallback(
        (e) => {
            e.stopPropagation();
            setSelectedUserData({
                id: e.currentTarget.dataset.id,
                name: e.currentTarget.dataset.name || "",
            });
            setShowArchiveModal(true);
        },
        [setSelectedUserData, setShowArchiveModal]
    );

    const onHideUpdateStatus = useCallback(
        (e, data) => {
            if (data) {
                reloadAllUsersDataAndResetPagination();
            }
            setSelectedUserData({ id: "", name: "" });
            setStatus("");
            setShowUpdateStatusModal(false);
        },
        [
            reloadAllUsersDataAndResetPagination,
            setSelectedUserData,
            setStatus,
            setShowUpdateStatusModal,
        ]
    );

    const onHideDeleteModal = useCallback(
        (e, data) => {
            if (data) {
                reloadAllUsersDataAndResetPagination();
            }
            setSelectedUserData({ id: "", name: "" });
            setShowArchiveModal(false);
        },
        [
            reloadAllUsersDataAndResetPagination,
            setSelectedUserData,
            setShowArchiveModal,
        ]
    );

    const onUpdateUserStatus = useCallback(async () => {
        try {
            setIsUpdating(true);
            await updateIdentityUserStatus(
                selectedUserData.id,
                status === "REACTIVATE"
                    ? UserStatus.ACTIVE
                    : UserStatus.SUSPENDED
            );
            setIsUpdating(false);
            toast.success(
                `Successfully updated the user${
                    selectedUserData.name
                        ? " " + selectedUserData.name + "'s"
                        : "'s"
                } status to "${
                    status === "REACTIVATE"
                        ? UserStatus.ACTIVE
                        : UserStatus.SUSPENDED
                }".`
            );
            onHideUpdateStatus(null, status);
        } catch (e) {
            console.error(e);
            setIsUpdating(false);
            toast.error(
                <div>
                    Failed to {status || "update"} the user
                    {selectedUserData.name
                        ? ' "' + selectedUserData.name + '"'
                        : ""}
                    !
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        selectedUserData.id,
        selectedUserData.name,
        status,
        onHideUpdateStatus,
        setIsUpdating,
    ]);

    const onArchiveUser = useCallback(async () => {
        try {
            setIsArchiving(true);
            await deleteUserAccount(selectedUserData.id);
            setIsArchiving(false);
            toast.success(
                `Successfully archived the user ${
                    selectedUserData.name
                        ? ' "' + selectedUserData.name + '"'
                        : ""
                }.`
            );
            onHideDeleteModal(null, "Deleted");
        } catch (e) {
            console.error(e);
            setIsArchiving(false);
            toast.error(
                <div>
                    Failed to archive the user
                    {selectedUserData.name
                        ? ' "' + selectedUserData.name + '"'
                        : ""}
                    !
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        selectedUserData.id,
        selectedUserData.name,
        setIsArchiving,
        onHideDeleteModal,
    ]);

    const actionButtonsColumnData = useCallback(
        (user = {}) => (
            <div className="text-center">
                {isUpperOrSameLevelBoundary(userBoundaryType, user.boundary) ? (
                    <IcIcon
                        className="mr-2 text-black"
                        size="2x"
                        icon={faLockAccess}
                    />
                ) : (
                    <>
                        {isAuthorizedForAction(
                            AccessPermissionModuleNames.USERS,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.USERS
                            ].actions.UpdateUser
                        ) ||
                        isAuthorizedForAction(
                            AccessPermissionModuleNames.USERS,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.USERS
                            ].actions.DeleteUser
                        ) ? (
                            <div className="d-flex justify-content-center align-items-center">
                                {isAuthorizedForAction(
                                    AccessPermissionModuleNames.USERS,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.USERS
                                    ].actions.UpdateUser
                                ) && (
                                    <Button
                                        className="mx-1"
                                        variant={
                                            UserStatusOppositeColorCode[
                                                user?.status
                                            ]
                                        }
                                        size="sm"
                                        data-id={user?._id}
                                        data-name={
                                            user?.userData?.username ||
                                            user?.userData?.firstName ||
                                            user?.userData?.lastName ||
                                            ""
                                        }
                                        data-status={
                                            UserStatusOppositePresentTense[
                                                user?.status
                                            ] || ""
                                        }
                                        disabled={
                                            user?.status ===
                                                UserStatus.ARCHIVED ||
                                            !UserStatusOppositePresentTense[
                                                user?.status
                                            ]
                                        }
                                        onClick={onClickUpdateStatus}
                                    >
                                        <div className="d-flex justify-content-center align-items-center">
                                            <IcIcon
                                                className="mr-2"
                                                size="lg"
                                                icon={faPower}
                                            />
                                            {UserStatusOppositePresentTense[
                                                user?.status
                                            ]
                                                ? toTitleCase(
                                                    UserStatusOppositePresentTense[
                                                        user?.status
                                                    ]
                                                )
                                                : "~ unknown"}
                                        </div>
                                    </Button>
                                )}
                                {isAuthorizedForAction(
                                    AccessPermissionModuleNames.USERS,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.USERS
                                    ].actions.DeleteUser
                                ) && (
                                    <Button
                                        className="mx-1"
                                        variant="outline-danger"
                                        size="sm"
                                        data-id={user?._id}
                                        data-name={
                                            user?.userData?.username ||
                                            user?.userData?.firstName ||
                                            user?.userData?.lastName ||
                                            ""
                                        }
                                        disabled={
                                            user?.status === UserStatus.ARCHIVED
                                        }
                                        onClick={onClickArchive}
                                    >
                                        <div className="d-flex justify-content-center align-items-center">
                                            <IcIcon
                                                className="mr-2"
                                                size="lg"
                                                icon={faArchive}
                                            />
                                            Archive
                                        </div>
                                    </Button>
                                )}
                            </div>
                        ) : (
                            applyBadgeStyling({
                                customValue: "No actions allowed.",
                            })
                        )}
                    </>
                )}
            </div>
        ),
        [
            userBoundaryType,
            isAuthorizedForAction,
            onClickUpdateStatus,
            onClickArchive,
        ]
    );

    const data = useMemo(
        () =>
            usersList.map((user) => ({
                _id: user?._id,
                username: getTruncatedStringWithTooltip({
                    value: user?.userData?.username,
                    valueMaxLength: 27,
                    customUnknownValue: applyBadgeStyling({
                        customValue: "Username not found.",
                    }),
                }),
                first_name: getTruncatedStringWithTooltip({
                    value: user?.userData?.firstName,
                    valueMaxLength: 18,
                    customUnknownValue: applyBadgeStyling({
                        customValue: "First name not found.",
                    }),
                }),
                last_name: getTruncatedStringWithTooltip({
                    value: user?.userData?.lastName,
                    valueMaxLength: 18,
                    customUnknownValue: applyBadgeStyling({
                        customValue: "Last name not found.",
                    }),
                }),
                access_type: applyBadgeStyling({
                    text: user?.boundary || "",
                    variant: UserBoundaryTypeColorCode[user?.boundary],
                }),
                accessTypeText: user?.boundary || "~ unknown",
                status: applyBadgeStyling({
                    text: user?.status || "",
                    variant: UserStatusColorCode[user?.status],
                }),
                statusText: user?.status || "",
                created_on: formatToCommonReadableFormat(user?.createdOn),
                "": actionButtonsColumnData(user),
            })),
        [usersList, actionButtonsColumnData]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadIdUsers({ skip: newSkip, limit, searchText }, appliedFilters);
        },
        [limit, searchText, appliedFilters, setSkip, loadIdUsers]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadIdUsers(
                { skip: defaultSkip, limit: newLimit, searchText },
                appliedFilters
            );
        },
        [searchText, appliedFilters, setLimit, setSkip, loadIdUsers]
    );

    const rowStyle = useCallback(
        (row) => {
            if (
                isUpperOrSameLevelBoundary(
                    userBoundaryType,
                    row.accessTypeText?.toUpperCase()
                ) ||
                row?.statusText === UserStatus.ARCHIVED
            ) {
                return {
                    pointerEvents: "none",
                    cursor: "none",
                };
            }
            return { cursor: "pointer" };
        },
        [userBoundaryType]
    );

    const onRowClick = useCallback(
        (rowData) => {
            try {
                if (
                    isAuthorizedForAction(
                        AccessPermissionModuleNames.USERS,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.USERS
                        ].actions.GetUser
                    ) &&
                    !isUpperOrSameLevelBoundary(
                        userBoundaryType,
                        rowData?.accessTypeText
                    )
                ) {
                    const userId = rowData?._id;
                    history.push(
                        `/control-panel/access-control/users/${userId}`
                    );
                }
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to navigate to the user profile!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [history, isAuthorizedForAction, userBoundaryType]
    );

    const tableRowEvents = {
        onClick: (e, row) => onRowClick(row),
    };

    const options = {
        page: skip,
        sizePerPage: limit,
        totalSize: totalCount,
        paginationSize: 5,
        pageStartIndex: 1,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageList: [
            { text: "25", value: 25 },
            { text: "50", value: 50 },
            { text: "100", value: 100 },
        ],
        sizePerPageRenderer: SizePerPageRenderer,
        onPageChange: onChangePagination,
        onSizePerPageChange: onChangePageSize,
    };

    const onTableChange = (_type, _newState) => {
        // * When "remote" is enabled, the "onTableChange" prop is activated automatically, so if we don't provide this method react-bootstrap will throw an error.
    };

    return (
        <div className="h-100">
            <PaginationProvider
                pagination={paginationFactory(options)}
                keyField="id"
                data={data}
                columns={columns}
                columnToggle
            >
                {({ paginationTableProps }) => (
                    <BootstrapTable
                        {...paginationTableProps}
                        remote={{ pagination: true }}
                        keyField="id"
                        data={data}
                        columns={columns}
                        columnToggle
                        loading={isLoading}
                        onTableChange={onTableChange}
                        noDataIndication={<NoData loading={isLoading} />}
                        rowEvents={tableRowEvents}
                        overlay={BootstrapTableOverlay}
                        rowStyle={rowStyle}
                    />
                )}
            </PaginationProvider>
            {showUpdateStatusModal && (
                <UpdateStatus
                    showModal={showUpdateStatusModal}
                    isUpdating={isUpdating}
                    itemType="USER"
                    itemName={selectedUserData.name}
                    status={status}
                    onUpdateStatus={onUpdateUserStatus}
                    onHide={onHideUpdateStatus}
                />
            )}
            {showArchiveModal && (
                <DeleteItem
                    showModal={showArchiveModal}
                    isDeleting={isArchiving}
                    itemType="USER"
                    itemName={selectedUserData.name}
                    onDeleteItem={onArchiveUser}
                    onHide={onHideDeleteModal}
                />
            )}
        </div>
    );
};

IdUserTable.defaultProps = {
    usersList: [],
    isLoading: false,
    searchText: "",
    appliedFilters: [],
    setLimit: () => {},
    setSkip: () => {},
    loadIdUsers: () => {},
    reloadAllUsersDataAndResetPagination: () => {},
};

IdUserTable.propTypes = {
    usersList: PropTypes.array,
    isLoading: PropTypes.bool,
    totalCount: PropTypes.number.isRequired,
    limit: PropTypes.number.isRequired,
    skip: PropTypes.number.isRequired,
    searchText: PropTypes.string,
    appliedFilters: PropTypes.array,
    setLimit: PropTypes.func,
    setSkip: PropTypes.func,
    loadIdUsers: PropTypes.func,
    reloadAllUsersDataAndResetPagination: PropTypes.func,
};

export default IdUserTable;
