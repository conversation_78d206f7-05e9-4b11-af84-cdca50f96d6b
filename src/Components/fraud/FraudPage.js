import React, { useCallback, useContext } from "react";
import FraudIncidentsPage from "./fraudIncidents/FraudIncidentsPage";
import FraudRulesPage from "./fraudRules/FraudRulesPage";
import { AccessPermissionModuleNames, AccessPermissionModules, FraudIncidentStatus, FraudMainTabs } from "../../Data";
import { Tab, Tabs } from "@shoutout-labs/shoutout-themes-enterprise";
import UnauthorizedAccessControl from "../utils/unauthorizedAccessControl/UnauthorizedAccessControl";
import { FraudContext } from "./context/FraudContext";



const FraudPage=()=>{
    const {mainTab,setStates} = useContext(FraudContext);

    const onSelectTab = useCallback((tab) => {
        setStates({mainTab:tab,fraudIncidentsSkip:1, fraudIncidentsLimit:25,fraudIncidentSubTab:FraudIncidentStatus.ALL,reloadFraudIncidents:true});
    }, [setStates]);

    return(
        <div className="container-fluid">
            <Tabs
                onSelect={onSelectTab}
                activeKey={mainTab}
                transition={false}
                className="ml-n3 mb-3 border-solid-bottom"
            >
                <Tab
                    eventKey={FraudMainTabs.FRAUD_INCIDENTS}
                    title={<span className="mr-2">Fraud Incidents</span>}
                >
                    <UnauthorizedAccessControl
                        actionList={
                            {[`${AccessPermissionModuleNames.INCIDENT}`]:[
                                    AccessPermissionModules[AccessPermissionModuleNames.INCIDENT].actions.ListIncidents,
                                ],
                                [`${AccessPermissionModuleNames.RULE_TYPE}`]:[
                                    AccessPermissionModules[AccessPermissionModuleNames.RULE_TYPE].actions.ListRuleTypes,
                                ],
                                [`${AccessPermissionModuleNames.RULE}`]:[
                                    AccessPermissionModules[AccessPermissionModuleNames.RULE].actions.ListRules,
                                ]
                            }
                        }
                        logic={"OR"}
                    >
                        <FraudIncidentsPage/>
                    </UnauthorizedAccessControl>
                </Tab>
                <Tab
                    eventKey={FraudMainTabs.FRAUD_RULES}
                    title={<span className="mr-2">Fraud Rules</span>}
                >
                    <UnauthorizedAccessControl
                        actionList={
                            { [`${AccessPermissionModuleNames.RULE_TYPE}`]:[
                                    AccessPermissionModules[AccessPermissionModuleNames.RULE_TYPE].actions.ListRuleTypes,
                                ],
                                [`${AccessPermissionModuleNames.RULE}`]:[
                                    AccessPermissionModules[AccessPermissionModuleNames.RULE].actions.GetRule,
                                    AccessPermissionModules[AccessPermissionModuleNames.RULE].actions.UpdateRule,
                                    AccessPermissionModules[AccessPermissionModuleNames.RULE].actions.ListRules,
                                    AccessPermissionModules[AccessPermissionModuleNames.RULE].actions.DeleteRule,
                                    AccessPermissionModules[AccessPermissionModuleNames.RULE].actions.CreateRule,
                                ] }
                        }
                        logic={"OR"}
                    >
                        <FraudRulesPage/>
                    </UnauthorizedAccessControl>
                </Tab>
            </Tabs>
        </div>
    )
}
export default FraudPage;
