import React, { useCallback } from "react";
import { Button, Form, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faTimes } from "../../../../FaICIconMap";

const EmailAddressInput=({emailIndex, item, removeEmail, setEmail, emailQueueLength, disabled})=>{

    const onChangeEmailAddress = useCallback(e => {
        const email = e.target.value;
        setEmail(emailIndex, email);
    }, [emailIndex, setEmail]);

    const onRemoveEmail = useCallback(() => removeEmail(emailIndex), [emailIndex, removeEmail]);
    return(
        <div className="d-flex flex-row mt-3">
            <Form.Control
                type="email"
                value={item}
                disabled={disabled}
                onChange={onChangeEmailAddress}
                placeholder="Enter Email Address"
                required
            />
            <div className="ml-2 mt-1">
                <Button
                    className="text-danger"
                    variant="link"
                    size="sm"
                    disabled={disabled || emailQueueLength === 1}
                    onClick={onRemoveEmail}
                >
                    {emailQueueLength !== 1 && <IcIcon size="md" icon={faTimes} />}
                </Button>
            </div>
        </div>
    )
}

export default EmailAddressInput
