import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import {
    Button,
    Card,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faCalendar,
    faExport,
    faFilter,
    faFilterSlash,
    faObjectGroup,
    faPower,
    faShieldCheck,
    faSync,
    faTransaction,
    faUserExclamation,
} from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    MemberPrimaryAttributeObjectPropertyName,
    SortOrderReadable,
    SortOrders,
} from "Data";
import { useToggle } from "Hooks";
import BaseLayout from "Layout/BaseLayout";
import {
    exportListOfIncidents,
    getFraudIncidents,
    getRulesList,
} from "Services";
import {
    defaultColumnTemplate,
    formatToCommonReadableFormat,
    getDefaultValuesOfFilters,
    getQueryFilters,
    getTruncatedStringWithTooltip,
    headerFormatterForSort,
    toTitleCase,
} from "Utils";
import QueryParamFilters from "Components/common/queryParamFilters/QueryParamFilters";
import ReportsViaEmail from "Components/common/reportsViaEmail/ReportsViaEmail";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import UnauthorizedAccessControl from "Components/utils/unauthorizedAccessControl/UnauthorizedAccessControl";
import { FraudContext } from "../context/FraudContext";
import FraudTableView from "../shared/fraudTableView/FraudTableView";
import { FraudFunctionalAccessControl } from "../FraudFunctionalAccessControl";

const fraudIncidentFilters = [
    { label: "Rule Name", value: "ruleId" },
    { label: "Rule Type", value: "ruleTypeId" },
    { label: "Transactions", value: "defectsCount" },
    { label: "Fraudulent Member", value: "memberIdentifier" },
    { label: "Generated Date", value: "createdOnDateRange" }, // * Date range attributes - "fromDate" "toDate"
];

const inputKeys = [
    {
        key: "select",
        values: ["ruleId", "ruleTypeId"],
    },
    { key: "number", values: ["defectsCount"] },
    { key: "text", values: ["memberIdentifier"] },
    {
        key: "date-range",
        values: {
            createdOnDateRange: {
                fromDateKey: "fromDate",
                toDateKey: "toDate",
            },
        },
    },
];

const defaultSortBy = "createdOn",
    defaultSortDirection = SortOrders.desc;

const defaultSorted = [
    { dataField: defaultSortBy, order: defaultSortDirection },
];

const columnDataProperties = [
    {
        dataField: "ruleName",
        displayName: "Rule Name",
        icon: faShieldCheck,
        headerStyle: { width: "20%" },
    },
    {
        dataField: "ruleType",
        displayName: "Rule Type",
        icon: faObjectGroup,
        headerStyle: { width: "20%" },
    },
    {
        dataField: "memberIdentifier",
        displayName: "Fraudulent Member",
        icon: faUserExclamation,
        headerStyle: { width: "20%" },
    },
    {
        dataField: "defectsCount",
        displayName: "Transactions",
        icon: faTransaction,
        sortEnabled: true,
        headerStyle: { width: "15%" },
        headerFormatter: headerFormatterForSort,
    },
    {
        dataField: "createdOn",
        displayName: "Generated Date",
        icon: faCalendar,
        sortEnabled: true,
        headerStyle: { width: "15%" },
        headerFormatter: headerFormatterForSort,
    },
    {
        dataField: "status",
        displayName: "Status",
        headerStyle: { width: "10%" },
        icon: faPower,
    },
];

const columns = columnDataProperties.map((cDP) =>
    defaultColumnTemplate({ ...cDP })
);

const FraudIncidentsHoC = ({ activeStatus }) => {
    const { organization, selectedRegion, isAuthorizedForAction } =
        useContext(UserContext);
    const {
        ruleTypes,
        fraudRules: fraudRulesForFilters,
        fraudIncidentSubTab,
        setStates,
        fraudIncidentsSkip,
        fraudIncidentsLimit,
    } = useContext(FraudContext);
    const [sort, setSort] = useState({
        sortBy: defaultSortBy,
        sortDirection: defaultSortDirection,
    });
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [fraudRules, setFraudRules] = useState({});
    const [isReloading, setIsReloading] = useState(false);
    const [appliedFilterRows, setAppliedFilterRows] = useState([]);
    const [appliedFilters, setAppliedFilters] = useState([]);
    const [showExportIncidents, setShowExportIncidents] = useState(false);

    const memberIdentifierLabel = useMemo(
        () =>
            MemberPrimaryAttributeObjectPropertyName[
                organization?.configuration?.memberPrimaryAttribute
            ] ?? "identifier",
        [organization?.configuration?.memberPrimaryAttribute]
    );

    const getQueryParamData = useCallback(
        (arrayToReduce) =>
            arrayToReduce.reduce((result, item) => {
                const filterInput =
                    inputKeys.find((iK) => {
                        if (iK.key === "date-range") {
                            return Object.keys(iK.values).includes(item.value);
                        } else {
                            return iK.values.includes(item.value);
                        }
                    })?.key ?? "";
                let options = [];
                let labelKey = "label";
                let valueKey = "value";
                let groupBy = "";
                let placeholder = "";

                const defaultValues = getDefaultValuesOfFilters(
                    { filterInput, filterKey: item.value },
                    inputKeys
                );

                if (filterInput === "select") {
                    switch (item.value) {
                        case "ruleId":
                            options = fraudRulesForFilters;
                            break;
                        case "ruleTypeId":
                            options = ruleTypes.ruleTypes;
                            break;
                        default:
                            break;
                    }
                } else if (filterInput === "number") {
                    placeholder = "transaction amount";
                } else if (filterInput === "text") {
                    placeholder = memberIdentifierLabel;
                }

                result[item.value] = {
                    filterInput,
                    key: item.value,
                    id: item.value,
                    name: item.value,
                    valueKey,
                    placeholder:
                        placeholder ?? item?.label?.toLowerCase() ?? "",
                    ...defaultValues,
                    ...(filterInput === "select"
                        ? { options, labelKey, groupBy }
                        : {}),
                    ...(filterInput === "number"
                        ? { minimumValue: 0, useCustomOperators: true }
                        : {}),
                };

                return result;
            }, {}),
        [fraudRulesForFilters, ruleTypes.ruleTypes, memberIdentifierLabel]
    );

    const tabQueryFilterData = useMemo(
        () => ({
            queryParamFilterOptions: fraudIncidentFilters,
            queryParamFilterMetadata: getQueryParamData(fraudIncidentFilters),
        }),
        [getQueryParamData]
    );

    const loadFraudIncidents = useCallback(
        async ({ skip, limit }, filters = [], sort = {}) => {
            try {
                setData([]);
                setTotalCount(0);
                setIsLoading(true);

                let queryObj = {
                    regionId: selectedRegion._id,
                    limit: limit,
                    skip: (skip - 1) * limit,
                    ...(activeStatus !== "ALL" ? { status: activeStatus } : {}),
                };

                if (filters.length !== 0) {
                    queryObj = { ...queryObj, ...getQueryFilters(filters) };
                }

                if (Object.values(sort).length !== 0) {
                    sort = {
                        ...sort,
                        sortDirection: sort?.sortDirection?.toUpperCase(),
                    };
                    queryObj = { ...queryObj, ...sort };
                }
                const [fraudIncidentsData, fraudRulesList] = await Promise.all([
                    getFraudIncidents(queryObj),
                    getRulesList({
                        limit: 1000,
                        skip: 0,
                        regionId: selectedRegion._id,
                    }),
                ]);
                let fraudRules = {};
                if (fraudRulesList?.items?.length !== 0) {
                    fraudRulesList?.items.forEach(
                        (fraudRule) =>
                            (fraudRules[fraudRule?._id ?? "-"] = fraudRule)
                    );
                }
                setFraudRules(fraudRules);
                setTotalCount(fraudIncidentsData.total);
                setData(fraudIncidentsData.items);
                setIsLoading(false);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load Fraud Incidents!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
                setIsLoading(false);
            }
        },
        [selectedRegion, activeStatus]
    );

    const onReloadFraudIncidents = useCallback(async () => {
        setIsReloading(true);
        await loadFraudIncidents(
            {
                skip: fraudIncidentsSkip,
                limit: fraudIncidentsLimit,
            },
            appliedFilters,
            sort
        );
        setStates({ fraudIncidentsSkip: 1 });
        setIsReloading(false);
    }, [
        fraudIncidentsSkip,
        fraudIncidentsLimit,
        appliedFilters,
        sort,
        setStates,
        loadFraudIncidents,
    ]);

    const setSortingOrder = useCallback(
        async ({ sortBy, sortDirection }) => {
            try {
                if (
                    sortDirection !== sort.sortDirection ||
                    sortBy !== sort.sortBy
                ) {
                    setSort({
                        sortDirection: sortDirection,
                        sortBy: sortBy,
                    });
                    setStates({ reloadFraudIncidents: true });
                }
            } catch (e) {
                toast.error(
                    <div>
                        Failed to sort table!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
                setSort({
                    sortBy: defaultSortBy,
                    sortDirection: defaultSortDirection,
                });
            }
        },
        [sort, setStates, setSort]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setStates({ fraudIncidentsSkip: newSkip });
            loadFraudIncidents(
                {
                    skip: newSkip,
                    limit: fraudIncidentsLimit,
                },
                appliedFilters,
                sort
            );
        },
        [
            fraudIncidentsLimit,
            appliedFilters,
            sort,
            setStates,
            loadFraudIncidents,
        ]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setStates({ fraudIncidentsLimit: newLimit });
            loadFraudIncidents(
                {
                    skip: fraudIncidentsSkip,
                    limit: newLimit,
                },
                appliedFilters,
                sort
            );
        },
        [
            fraudIncidentsSkip,
            appliedFilters,
            sort,
            setStates,
            loadFraudIncidents,
        ]
    );

    const onShowExportIncidents = useCallback(
        () => setShowExportIncidents(true),
        [setShowExportIncidents]
    );

    const onCloseExportIncidents = useCallback(
        () => setShowExportIncidents(false),
        [setShowExportIncidents]
    );

    const onExportPointActivityOverviewData = useCallback(
        async (notificationEmails = []) => {
            try {
                let queryObj = {
                    regionId: selectedRegion._id,
                    ...(activeStatus !== "ALL" ? { status: activeStatus } : {}),
                    notificationEmails,
                };
                let sortObj = {};

                if (Object.values(sort).length !== 0) {
                    sortObj = {
                        ...sort,
                        sortDirection: sort?.sortDirection?.toUpperCase(),
                    };
                    queryObj = { ...queryObj, ...sortObj };
                }

                if (appliedFilters.length !== 0) {
                    queryObj = {
                        ...queryObj,
                        ...getQueryFilters(appliedFilters),
                    };
                }

                await exportListOfIncidents(queryObj);
            } catch (e) {
                throw e;
            }
        },
        [activeStatus, appliedFilters, selectedRegion._id, sort]
    );

    const tableData = useMemo(
        () =>
            data.map(
                ({
                    ruleId,
                    ruleTypeId,
                    defectsCount,
                    createdOn,
                    status,
                    memberIdentifier,
                    _id,
                    ...rest
                }) => ({
                    ruleName: getTruncatedStringWithTooltip({
                        value: fraudRules[ruleId]?.name,
                        customUnknownValue: applyBadgeStyling({
                            customValue: "Rule name not found.",
                        }),
                    }),
                    ruleType: getTruncatedStringWithTooltip({
                        value: ruleTypes.rulesObject.hasOwnProperty(ruleTypeId)
                            ? ruleTypes.rulesObject[ruleTypeId]?.name
                            : "",
                        valueMaxLength: 30,
                        customUnknownValue: applyBadgeStyling({
                            customValue: "Rule type not found.",
                        }),
                    }),
                    memberIdentifier: getTruncatedStringWithTooltip({
                        value: memberIdentifier,
                        valueMaxLength: 36,
                        customUnknownValue: applyBadgeStyling({
                            customValue: `Member ${memberIdentifierLabel} not found.`,
                        }),
                    }),
                    defectsCount: getTruncatedStringWithTooltip({
                        value:
                            typeof Number(defectsCount) === "number"
                                ? defectsCount
                                : "",
                        valueMaxLength: 10,
                        customUnknownValue: applyBadgeStyling({
                            customValue: "Transactions count not found.",
                        }),
                    }),
                    status: applyBadgeStyling({
                        text: status,
                        variant: status === "RESOLVED" ? "success" : "warning",
                    }),
                    createdOn: createdOn
                        ? formatToCommonReadableFormat(createdOn)
                        : "~ unknown",
                    ruleTypeId,
                    id: _id,
                    ...rest,
                })
            ),
        [data, fraudRules, ruleTypes.rulesObject, memberIdentifierLabel]
    );

    const rowEvents = {
        onClick: (e, row) => {
            window.open(`/fraudulence/fraud/${row.id}`, "_blank");
        },
    };

    useEffect(() => {
        if (
            fraudIncidentSubTab &&
            FraudFunctionalAccessControl({
                isAuthorizedForAction: isAuthorizedForAction,
                actionList: {
                    [`${AccessPermissionModuleNames.INCIDENT}`]: [
                        AccessPermissionModules[
                            AccessPermissionModuleNames.INCIDENT
                        ].actions.ListIncidents,
                    ],
                },
                logic: "AND",
            })
        ) {
            loadFraudIncidents(
                {
                    skip: fraudIncidentsSkip,
                    limit: fraudIncidentsLimit,
                },
                appliedFilters,
                sort
            );
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fraudIncidentSubTab, isAuthorizedForAction, appliedFilters, sort]);

    return (
        <UnauthorizedAccessControl
            actionList={{
                [`${AccessPermissionModuleNames.INCIDENT}`]: [
                    AccessPermissionModules[
                        AccessPermissionModuleNames.INCIDENT
                    ].actions.ListIncidents,
                ],
                [`${AccessPermissionModuleNames.RULE_TYPE}`]: [
                    AccessPermissionModules[
                        AccessPermissionModuleNames.RULE_TYPE
                    ].actions.ListRuleTypes,
                ],
                [`${AccessPermissionModuleNames.RULE}`]: [
                    AccessPermissionModules[AccessPermissionModuleNames.RULE]
                        .actions.ListRules,
                ],
            }}
            logic={"AND"}
        >
            <BaseLayout
                containerClassName="py-3"
                bottom={
                    <div>
                        <div className="d-flex justify-content-between mb-">
                            <div className="d-flex align-items-center">
                                <Button
                                    className="shadow-none"
                                    variant="link"
                                    size="sm"
                                    disabled={isLoading || isReloading}
                                    onClick={onReloadFraudIncidents}
                                >
                                    {!isReloading && (
                                        <div className="d-flex align-items-center">
                                            <IcIcon
                                                size="md"
                                                className="mr-2"
                                                icon={faSync}
                                            />
                                            {`Reload${
                                                activeStatus
                                                    ? " " +
                                                        toTitleCase(
                                                            activeStatus
                                                        ) +
                                                    " "
                                                    : ""
                                            }Fraud
                                            Incidents`}
                                        </div>
                                    )}
                                </Button>
                                <div style={{ fontSize: "0.9rem" }}>
                                    {isReloading && (
                                        <div className="text-primary">
                                            Reloading...
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div>
                                <Button
                                    size="sm"
                                    variant="outline-primary"
                                    disabled={isLoading || isReloading}
                                    onClick={toggleShowFilters}
                                >
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={
                                            showFilters
                                                ? faFilterSlash
                                                : faFilter
                                        }
                                    />
                                    Filter By
                                </Button>
                                <UnauthorizedAccessControl
                                    actionList={{
                                        [`${AccessPermissionModuleNames.INCIDENT}`]:
                                            [
                                                AccessPermissionModules[
                                                    AccessPermissionModuleNames
                                                        .INCIDENT
                                                ].actions.ExportIncidents,
                                            ],
                                    }}
                                    logic={"AND"}
                                    renderEmpty="true"
                                >
                                    <Button
                                        className="ml-2"
                                        size="sm"
                                        variant="dark"
                                        disabled={
                                            isLoading ||
                                            isReloading ||
                                            totalCount <= 0
                                        }
                                        onClick={onShowExportIncidents}
                                    >
                                        <div className="d-flex align-items-center">
                                            <IcIcon
                                                className="mr-2"
                                                size="md"
                                                icon={faExport}
                                            />
                                            Export Fraud Incidents
                                        </div>
                                    </Button>
                                </UnauthorizedAccessControl>
                            </div>
                        </div>
                        <div className="mt-3">
                            {!showFilters && appliedFilters.length !== 0 && (
                                <div className="d-flex align-items-center">
                                    <h3 className="mb-0 mr-2">
                                        {appliedFilters.length === 1
                                            ? "A filter is "
                                            : appliedFilters.length +
                                                " filters are "}
                                        applied.
                                    </h3>
                                    <Button
                                        variant="info"
                                        size="sm"
                                        disabled={isLoading || isReloading}
                                        onClick={toggleShowFilters}
                                    >
                                        Show Applied Filters
                                    </Button>
                                </div>
                            )}
                            {showFilters && (
                                <QueryParamFilters
                                    multipleFilters
                                    queryParamFilterMetadata={
                                        tabQueryFilterData.queryParamFilterMetadata
                                    }
                                    queryParamFilterOptions={
                                        tabQueryFilterData.queryParamFilterOptions
                                    }
                                    isLoading={isLoading}
                                    appliedFilters={appliedFilters}
                                    appliedFilterRows={appliedFilterRows}
                                    setAppliedFilters={setAppliedFilters}
                                    setAppliedFilterRows={setAppliedFilterRows}
                                />
                            )}
                            <hr />
                        </div>
                        <div>
                            <h3 className="px-3 font-weight-bold">
                                {isLoading || isReloading
                                    ? "Loading..."
                                    : `Total - ${totalCount}`}
                            </h3>
                            <FraudTableView
                                columns={columns}
                                data={tableData}
                                defaultSorted={defaultSorted}
                                sizePerPage={fraudIncidentsLimit}
                                page={fraudIncidentsSkip}
                                totalCount={totalCount}
                                rowEvents={rowEvents}
                                isLoading={isLoading || isReloading}
                                setSortingOrder={setSortingOrder}
                                onChangePagination={onChangePagination}
                                onChangePageSize={onChangePageSize}
                            />
                        </div>
                        {showExportIncidents && (
                            <ReportsViaEmail
                                show={showExportIncidents}
                                exportHeader="Export Anomaly Incidents"
                                customInfoPanel={
                                    sort.sortBy ||
                                    (appliedFilters.length !== 0 &&
                                        appliedFilterRows.length !== 0) ? (
                                        <div className="w-100 mb-3">
                                            {sort.sortBy && (
                                                <Card className="my-2">
                                                    <Card.Body className="d-flex align-items-center grey-bg rounded">
                                                        Sorted
                                                        <div className="font-weight-bold mx-1">
                                                            Anomaly Incidents
                                                        </div>
                                                        by
                                                        <div className="font-weight-bold mx-1">
                                                            {
                                                                columnDataProperties.find(
                                                                    (item) =>
                                                                        item.dataField ===
                                                                        sort.sortBy
                                                                ).displayName
                                                            }
                                                        </div>
                                                        in
                                                        <div className="font-weight-bold mx-1">
                                                            {SortOrderReadable?.[
                                                                sort
                                                                    .sortDirection
                                                            ] ?? "~unknown"}
                                                        </div>
                                                        order.
                                                    </Card.Body>
                                                </Card>
                                            )}
                                            {appliedFilters.length !== 0 &&
                                                appliedFilterRows.length !==
                                                    0 && (
                                                    <QueryParamFilters
                                                        queryParamFilterMetadata={
                                                            tabQueryFilterData?.queryParamFilterMetadata
                                                        }
                                                        queryParamFilterOptions={
                                                            tabQueryFilterData?.queryParamFilterOptions
                                                        }
                                                        appliedFilters={
                                                            appliedFilters
                                                        }
                                                        appliedFilterRows={
                                                            appliedFilterRows
                                                        }
                                                        viewOnly
                                                        viewOnlyTitle={
                                                            <div className="font-weight-bold mb-2">
                                                                {`Filter${
                                                                    appliedFilters.length ===
                                                                    1
                                                                        ? ""
                                                                        : "s"
                                                                } Applied for Anomaly Incidents`}
                                                            </div>
                                                        }
                                                    />
                                                )}
                                        </div>
                                    ) : null
                                }
                                successText="Successfully created a job to export Anomaly Incidents. The report will be sent to the given email address/addresses when ready."
                                errorText="Failed to export Anomaly Incidents!"
                                onExport={onExportPointActivityOverviewData}
                                onHide={onCloseExportIncidents}
                            />
                        )}
                    </div>
                }
            />
        </UnauthorizedAccessControl>
    );
};
export default FraudIncidentsHoC;
