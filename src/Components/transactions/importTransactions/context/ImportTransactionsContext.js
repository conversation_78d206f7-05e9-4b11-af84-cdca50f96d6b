import React, { useCallback, useContext, useEffect, useReducer } from "react";
import { toast } from "react-toastify";
import { UserContext } from "Contexts";
import { getImportTransactions, importTransaction } from "Services";
import { getQueryFilters } from "Utils";

const ImportTransactionContext = React.createContext();

const defaultSkip = 1;

const transactionLoadingStatus = {
    transactionsList: [],
    limit: 25,
    skip: 1,
    isLoading: true,
    totalCount: 0,
};

const locationOptions = { STATIC: "STATIC", DYNAMIC: "DYNAMIC" };

const importTransactionsStatus = {
    merchant: [],
    transactionType: [],
    transactionSubTypeId: [],
    transactionsFile: "",
    selectedMerchantLocation: [],
    locationOption: "",
    fileName: null,
    fileType: null,
    fileSample: [],
    fileHeaders: [],
    fileId: null,
    columnNameMap: {},
    headerMap: {},
    selectedLocationField: "",
    fileToken: "",
    appliedFilters: [],
};

const initialState = {
    showImportTransactionWizard: false,
    isImporting: false,
    ...importTransactionsStatus,
    ...transactionLoadingStatus,
};

const ImportTransactionsContextActions = {
    SET_TRANSACTIONS_LIST: "setTransactionsList",
    SET_IS_LOADING: "setIsLoading",
    SET_PAGEVALUE: "setPageValue",
    RESET: "reset",
    SET_SHOW_WIZARD: "setShowWizard",
    SET_IS_IMPORTING: "setIsImporting",
    SET_FILE: "setFile",
    SET_FILE_DATA: "setFileData",
    SET_STEP_STATE: "setStepState",
    SET_SELECTED_LOCATION_FIELD: "setSelectedLocationField",
    SET_PAGINATION_INFO: "setPaginationInfo",
    SET_FILE_TOKEN: "setFileToken",
    SET_COLUMN_NAME_MAP: "setColumnNameMap",
    SET_LIMIT: "setLimit",
    SET_SKIP: "SetSkip",
    SET_MERCHANT: "setMerchant",
    SET_TRANSACTION_TYPE: "setTransactionType",
    SET_SUB_TRANSACTION_TYPE: "setSubTransactionType",
    SET_MERCHANT_LOCATION: "setMerchantLocation",
    SET_APPLIED_FILTERS: "setAppliedFilters",
};

const reducer = (state, action) => {
    switch (action.type) {
        case ImportTransactionsContextActions.SET_TRANSACTIONS_LIST: {
            return {
                ...state,
                isLoading: false,
                transactionsList: action.transactionsList.items,
                totalCount: action.transactionsList.total,
            };
        }
        case ImportTransactionsContextActions.SET_IS_LOADING: {
            return {
                ...state,
                isLoading: action.isLoading,
            };
        }
        case ImportTransactionsContextActions.SET_PAGINATION_INFO: {
            return {
                ...state,
                isLoading: true,
                limit: action.limit,
                skip: action.skip,
            };
        }
        case ImportTransactionsContextActions.SET_LIMIT: {
            return {
                ...state,
                limit: action.limit,
            };
        }
        case ImportTransactionsContextActions.SET_SKIP: {
            return {
                ...state,
                skip: action.skip,
            };
        }
        case ImportTransactionsContextActions.SET_PAGEVALUE: {
            return {
                ...state,
                [action.key]: action.value,
            };
        }
        case ImportTransactionsContextActions.SET_MERCHANT: {
            return {
                ...state,
                merchant: action.merchant,
            };
        }
        case ImportTransactionsContextActions.SET_SUB_TRANSACTION_TYPE: {
            return {
                ...state,
                transactionSubTypeId: action.subTransactionType,
            };
        }
        case ImportTransactionsContextActions.SET_TRANSACTION_TYPE: {
            return {
                ...state,
                transactionType: action.transactionType,
            };
        }
        case ImportTransactionsContextActions.SET_MERCHANT_LOCATION: {
            return {
                ...state,
                selectedMerchantLocation: action.merchantLocation,
            };
        }
        case ImportTransactionsContextActions.SET_SHOW_WIZARD: {
            return {
                ...state,
                showImportTransactionWizard: action.showImportTransactionWizard,
            };
        }
        case ImportTransactionsContextActions.RESET: {
            return { ...state, ...importTransactionsStatus };
        }
        case ImportTransactionsContextActions.SET_IS_IMPORTING: {
            return {
                ...state,
                isImporting: action.isImporting,
            };
        }
        case ImportTransactionsContextActions.SET_FILE: {
            return {
                ...state,
                transactionsFile: action.transactionsFile,
            };
        }
        case ImportTransactionsContextActions.SET_FILE_DATA: {
            return {
                ...state,
                fileHeaders: action.fileHeaders,
            };
        }
        case ImportTransactionsContextActions.SET_STEP_STATE: {
            return {
                ...state,
                columnNameMap: action.updatedData?.columnNameMap,
                headerMap: action.updatedData?.headerMap,
            };
        }
        case ImportTransactionsContextActions.SET_SELECTED_LOCATION_FIELD: {
            return {
                ...state,
                selectedLocationField: action.selectedLocationField,
            };
        }
        case ImportTransactionsContextActions.SET_FILE_TOKEN: {
            return {
                ...state,
                fileToken: action.fileToken,
            };
        }
        case ImportTransactionsContextActions.SET_COLUMN_NAME_MAP: {
            return {
                ...state,
                columnNameMap: action.columnMap,
            };
        }
        case ImportTransactionsContextActions.SET_APPLIED_FILTERS: {
            return {
                ...state,
                appliedFilters: action.filters,
            };
        }
        default:
            return state;
    }
};

const ImportTransactionContextProvider = (props) => {
    const [state, dispatch] = useReducer(reducer, initialState);
    const { regionId } = useContext(UserContext);

    const setValues = useCallback(
        (event) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_PAGEVALUE,
                key: event.target.name,
                value: event.target.value,
            });
        },
        [dispatch]
    );

    const setSkip = useCallback(
        (skip) =>
            dispatch({ type: ImportTransactionsContextActions.SET_SKIP, skip }),
        [dispatch]
    );

    const setLimit = useCallback(
        (limit) =>
            dispatch({
                type: ImportTransactionsContextActions.SET_LIMIT,
                limit,
            }),
        [dispatch]
    );

    const onChangeMerchant = useCallback(
        (merchant) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_MERCHANT,
                merchant,
            });
        },
        [dispatch]
    );

    const onChangeTransactionType = useCallback(
        (transactionType) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_TRANSACTION_TYPE,
                transactionType,
            });
        },
        [dispatch]
    );

    const onChangeSubTransactionType = useCallback(
        (subTransactionType) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_SUB_TRANSACTION_TYPE,
                subTransactionType,
            });
        },
        [dispatch]
    );

    const onChangeMerchantLocation = useCallback(
        (merchantLocation) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_MERCHANT_LOCATION,
                merchantLocation,
            });
        },
        [dispatch]
    );

    const setTransactionsFile = useCallback(
        (transactionsFile) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_FILE,
                transactionsFile,
            });
        },
        [dispatch]
    );

    const setFileData = useCallback(
        (fileHeaders) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_FILE_DATA,
                fileHeaders,
            });
        },
        [dispatch]
    );

    const setColumnNames = useCallback(
        (columnMap) =>
            dispatch({
                type: ImportTransactionsContextActions.SET_COLUMN_NAME_MAP,
                columnMap,
            }),
        [dispatch]
    );

    const setFileToken = useCallback(
        (fileToken) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_FILE_TOKEN,
                fileToken,
            });
        },
        [dispatch]
    );

    const updateStepState = useCallback(
        (updatedData) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_STEP_STATE,
                updatedData,
            });
        },
        [dispatch]
    );

    const setSelectedLocationField = useCallback(
        (selectedLocationField) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_SELECTED_LOCATION_FIELD,
                selectedLocationField,
            });
        },
        [dispatch]
    );

    const setShowImportTransactionsWizard = useCallback(
        (showImportTransactionWizard) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_SHOW_WIZARD,
                showImportTransactionWizard,
            });
        },
        [dispatch]
    );

    const setAppliedFilters = useCallback(
        (filters = []) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_APPLIED_FILTERS,
                filters,
            });
        },
        [dispatch]
    );

    const reset = useCallback(() => {
        dispatch({ type: ImportTransactionsContextActions.RESET });
    }, [dispatch]);

    const loadImportTransactionsData = useCallback(
        async ({ limit, skip }, filters = []) => {
            let queryObj = {
                limit,
                skip: (skip - 1) * limit,
                regionId,
            };

            if (filters.length !== 0) {
                queryObj = { ...queryObj, ...getQueryFilters(filters) };
            }

            try {
                dispatch({
                    type: ImportTransactionsContextActions.SET_IS_LOADING,
                    isLoading: true,
                });

                const transactionData = await getImportTransactions(queryObj);
                dispatch({
                    type: ImportTransactionsContextActions.SET_TRANSACTIONS_LIST,
                    transactionsList: transactionData,
                });

                dispatch({
                    type: ImportTransactionsContextActions.SET_IS_LOADING,
                    isLoading: false,
                });
            } catch (e) {
                console.error(e);
                dispatch({
                    type: ImportTransactionsContextActions.SET_IS_LOADING,
                    isLoading: false,
                });
                toast.error(
                    <div>
                        Failed to load import transactions data!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [dispatch, regionId]
    );

    const onChangePagination = useCallback(
        async ({ skip = state.skip, limit = state.limit }) => {
            dispatch({
                type: ImportTransactionsContextActions.SET_PAGINATION_INFO,
                limit,
                skip,
            });
            loadImportTransactionsData({ limit, skip }, state.appliedFilters);
        },
        [
            state.skip,
            state.limit,
            state.appliedFilters,
            dispatch,
            loadImportTransactionsData,
        ]
    );

    const importTransactionFile = useCallback(() => {
        return new Promise(async (resolve, reject) => {
            try {
               
                const fieldMappings = Object.keys(state.columnNameMap).reduce(
                    function (result, key) {
                        const systemAttributeName = state.columnNameMap[key].systemAttributeName;
                        const fileColumnName = state.columnNameMap[key].fileColumnName;
                        
                        if (fileColumnName) {
                           
                            if (systemAttributeName === 'POINTS_AMOUNT' && state.columnNameMap['BILL_AMOUNT']) {
                                return result;
                            }
                           
                            if (systemAttributeName === 'BILL_AMOUNT' && state.columnNameMap['POINTS_AMOUNT']) {
                                return result;
                            }
    
                            result.push({ systemAttributeName, fileColumnName });
                        }
                        return result;
                    },
                    []
                );
                const transactionPayload = {
                    merchantLocationSelection: state.locationOption,
                    transactionType: state.transactionType[0].value,
                    transactionSubTypeId: state.transactionSubTypeId[0].value,
                    fieldMappings: Object.values(fieldMappings),
                    fileToken: state.fileToken,
                };
    
                if (state.locationOption === locationOptions.STATIC) {
                    transactionPayload.merchantLocationId = state.selectedMerchantLocation[0].value;
                } else {
                    transactionPayload.merchantLocationColumnName = state.selectedLocationField;
                }
    
                dispatch({
                    type: ImportTransactionsContextActions.SET_IS_IMPORTING,
                    isImporting: true,
                });
    
                await importTransaction(transactionPayload);
                toast.success("Successfully imported transactions.");
                loadImportTransactionsData(
                    {
                        limit: state.limit,
                        skip: state.skip,
                    },
                    state.appliedFilters
                );
                setShowImportTransactionsWizard(false);
                dispatch({
                    type: ImportTransactionsContextActions.SET_IS_IMPORTING,
                    isImporting: false,
                });
                reset();
                resolve();
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to create import transactions job!
                        <br />
                        {e.message ? `Error: ${e.message}` : "Please try again later."}
                    </div>
                );
    
                dispatch({
                    type: ImportTransactionsContextActions.SET_IS_IMPORTING,
                    isImporting: false,
                });
                reject(e);
            }
        });
    }, [
        state.skip,
        state.limit,
        state.locationOption,
        state.transactionType,
        state.selectedMerchantLocation,
        state.selectedLocationField,
        state.fileToken,
        state.columnNameMap,
        state.transactionSubTypeId,
        state.appliedFilters,
        dispatch,
        reset,
        loadImportTransactionsData,
        setShowImportTransactionsWizard,
    ]);
    
    useEffect(() => {
        loadImportTransactionsData(
            {
                limit: state.limit,
                skip: defaultSkip,
            },
            state.appliedFilters
        );
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [state.appliedFilters]);

    const value = {
        ...state,
        setValues,
        setShowImportTransactionsWizard,
        reset,
        loadImportTransactionsData,
        importTransactionFile,
        setTransactionsFile,
        setFileData,
        updateStepState,
        setSelectedLocationField,
        onChangePagination,
        setFileToken,
        setColumnNames,
        setSkip,
        setLimit,
        onChangeMerchantLocation,
        onChangeMerchant,
        onChangeSubTransactionType,
        onChangeTransactionType,
        setAppliedFilters,
    };
    return (
        <ImportTransactionContext.Provider value={value}>
            {props.children}
        </ImportTransactionContext.Provider>
    );
};

const ImportTransactionContextConsumer = ImportTransactionContext.Consumer;

export {
    ImportTransactionContextProvider,
    ImportTransactionContext,
    ImportTransactionContextConsumer,
};
