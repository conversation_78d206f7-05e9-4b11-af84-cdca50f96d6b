import React from 'react';
import {
    But<PERSON>, 
    Modal
} from '@shoutout-labs/shoutout-themes-enterprise';
import "./NotificationDetails.scss";

const PreviewContent = ({
    show,
    onHide,
    title,
    content
}) => {

    return(
        <Modal
            show={show}
            onHide={onHide}
            size="xl"
            backdrop={true}
            centered
            className="notification-details"
        >
            <Modal.Header
                closeButton
            >
                <Modal.Title>
                    Preview {title} Content
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
               <div className="content-preview p-2">
                    {content}
               </div>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="primary"
                    onClick={onHide}
                >
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    )
}

export default PreviewContent
