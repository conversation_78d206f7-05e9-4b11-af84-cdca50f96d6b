import React, { useContext, useCallback, useMemo, useState, useEffect } from "react";
import PropTypes from "prop-types";
import { SMSEditor } from "@shoutout-labs/shoutout-message-editor-enterprise";
import { Form } from "@shoutout-labs/shoutout-themes-enterprise";
import Constants from "Constants";
import { TransportTypes } from "Data";
import { NotificationEditContext } from "./context/NotificationEditContext";
import { DataContext } from "Contexts";

const TemplatesSMSEditor = ({ validated, ...props }) => {
    const {
        smsEditorState,
        isSmsBodyValid,
        onChangeSmsEditor,
        selectedNotification,
        setFrom,
    } = useContext(NotificationEditContext);

    const { smsProvidersList } = useContext(DataContext);
    const [smsProviders, setSMSProviders] = useState([]);

    const onChangeFromSMSProvider = useCallback(
        (smsProvider) => setFrom(smsProvider[0]?.value, TransportTypes.SMS.toLowerCase()),
        [setFrom],
    );

    const selectedSMSProvider = useMemo(() =>
        smsProviders.filter((provider) => provider?.value===selectedNotification[TransportTypes.SMS.toLowerCase()]?.from),
        [smsProviders, selectedNotification]);

    useEffect(()=>{
        setSMSProviders(smsProvidersList.filter((provider) => provider?.fromAddress)
            .map((provider) => ({
                label: `${provider.name ? provider.name + " - " : "" }${provider.fromAddress}`,
                value: provider.fromAddress,
            })))
    },[smsProvidersList]);

    return (
        <div className="templates-editor">
            <Form.Group controlId="sms-sender-id">
                <Form.Label className="d-flex align-items-center">
                    Sender Id
                    <div className="ml-1 text-danger">*</div>
                </Form.Label>
                <Form.Select
                    id="selectedProvider"
                    labelKey="label"
                    options={smsProviders}
                    placeholder={"Select a sender id/mobile number"}
                    selected={selectedSMSProvider}
                    disabled={smsProviders?.length === 0}
                    onChange={onChangeFromSMSProvider}
                    required
                />
            </Form.Group>
            <SMSEditor
                editorState={smsEditorState}
                onChange={onChangeSmsEditor}
                shortUrlOrigin={Constants.SHORT_URL_DOMAIN}
                {...props}
            />
            {validated && !isSmsBodyValid && (
                <Form.Text className="text-danger">
                    * SMS content cannot be empty!
                </Form.Text>
            )}
        </div>
    );
};

TemplatesSMSEditor.defaultProps = { validated: false };

TemplatesSMSEditor.propTypes = { validated: PropTypes.bool };

export default TemplatesSMSEditor;
