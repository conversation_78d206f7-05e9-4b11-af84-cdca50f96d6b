import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import {
    Button,
    Heading,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faCalendar,
    faArrowCircleDown,
    faArrowCircleUp,
    faMessage,
    faSync,
    faPower,
    faAngleLeftB,
    faFilter,
    faFilterSlash,
} from "FaICIconMap";
import { UserContext } from "Contexts";
import { NotificationChannel, NotificationFilterOptions } from "Data";
import { useToggle } from "Hooks";
import {
    getNotificationLogsFromMessageService as getNotificationLogs,
    getNotificationLogsCountFromMessageService as getNotificationLogsCount,
} from "Services";
import {
    formatToCommonFormat,
    formatToCommonReadableFormat,
    isEmptyObject,
} from "Utils";
import BaseLayout from "Layout/BaseLayout";
import TableView from "../shared/tableView/TableView";
import NotificationFilters from "../shared/notificationsFilters/NotificationFilters";
import {
    badgeStyle,
    nameIconTemplate,
} from "../shared/utils/NotificationsUtils";
import PreviewEmail from "Components/common/previewEmail/PreviewEmail";
import NotificationsDetailsView from "../shared/notificationsDetailsView/NotificationsDetailsView";
import PreviewSmsModal from "Components/common/previewSms/PreviewSmsModal";

const columns = [
    {
        dataField: "date",
        text: nameIconTemplate({ name: "Date", icon: faCalendar }),
    },
    {
        dataField: "channel",
        text: nameIconTemplate({ name: "Channel", icon: faMessage }),
    },
    {
        dataField: "from",
        text: nameIconTemplate({ name: "From", icon: faArrowCircleUp }),
    },
    {
        dataField: "to",
        text: nameIconTemplate({ name: "To", icon: faArrowCircleDown }),
    },
    {
        dataField: "status",
        text: nameIconTemplate({ name: "Status", icon: faPower }),
    },
];

const selectOptions = [
    { label: "Channel", value: NotificationFilterOptions.CHANNEL },
    { label: "Status", value: NotificationFilterOptions.STATUS },
    { label: "To Mobile Number", value: NotificationFilterOptions.TO_SMS },
    { label: "To Email", value: NotificationFilterOptions.TO_EMAIL },
];

const defaultSkip = 1,
    defaultLimit = 25;

const NotificationLogs = () => {
    const { regionId } = useContext(UserContext);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [data, setData] = useState([]);
    const [dataCount, setDataCount] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const [show, setShow] = useState(false);
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [selectedOption, setSelectedOption] = useState([]);
    const [fromDate, setFromDate] = useState("");
    const [toDate, setToDate] = useState("");
    const [mobileNumber, setMobileNumber] = useState("");
    const [email, setEmail] = useState("");
    const [channelStatus, setChannelStatus] = useState([]);
    const [notificationLogStatus, setNotificationLogStatus] = useState([]);
    const [otherFilter, setOtherFilter] = useState({});
    const [otherFilterKeyLabelPair, setOtherFilterKeyLabelPair] = useState({});
    const [appliedFilters, setAppliedFilters] = useState();
    const [isApplied, setIsApplied] = useState(false);
    const [isSelectDisabled, setIsSelectDisabled] = useState(false);
    const [selectedRow, setSelectedRow] = useState({});
    const [isReloading, setIsReloading] = useState(false);
    const [showPreviewSms, setShowPreviewSms] = useState(false);
    const [showPreviewEmail, setShowPreviewEmail] = useState(false);

    const loadNotificationLogs = useCallback(
        async ({ limit, skip }, filters, reloadCount = true) => {
            try {
                setIsLoading(true);

                const commonFilters = { regionId };
                let notificationLogsData = {};
                let payload = {
                    limit,
                    skip: (skip - 1) * limit,
                    ...commonFilters,
                };
                let countPayload = commonFilters;

                if (filters) {
                    payload = { ...payload, ...filters };
                    countPayload = { ...countPayload, ...filters };
                }

                if (reloadCount) {
                    const [
                        notificationLogResponse,
                        notificationLogCountResponse,
                    ] = await Promise.all([
                        getNotificationLogs(payload),
                        getNotificationLogsCount(countPayload),
                    ]);
                    setDataCount(notificationLogCountResponse?.total || 0);
                    notificationLogsData = notificationLogResponse;
                } else {
                    notificationLogsData = await getNotificationLogs(payload);
                }

                setData(notificationLogsData?.items || []);
            } catch (error) {
                console.error(error);
                setData([]);
                setDataCount(0);
                toast.error(
                    <div>
                        Failed to load notification logs!
                        <br />
                        {error.message
                            ? `Error: ${error.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setIsLoading(false);
            }
        },
        [regionId, setIsLoading, setData, setDataCount]
    );

    const tableData = useMemo(
        () =>
            data.map(
                ({
                    createdOn,
                    status,
                    transport,
                    _id,
                    subject,
                    body,
                    memberId,
                    to,
                    from,
                    member,
                    ...rest
                }) => ({
                    notificationId: _id,
                    date: formatToCommonReadableFormat(createdOn),
                    channel: badgeStyle(transport),
                    channelText: transport,
                    subject,
                    memberId,
                    from,
                    to,
                    status: badgeStyle(status),
                    body,
                    member,
                    rest,
                })
            ),
        [data]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadNotificationLogs(
                { limit, skip: newSkip },
                appliedFilters,
                false
            );
        },
        [appliedFilters, limit, setSkip, loadNotificationLogs]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadNotificationLogs(
                { limit: newLimit, skip: defaultSkip },
                appliedFilters,
                false
            );
        },
        [appliedFilters, setSkip, setLimit, loadNotificationLogs]
    );

    const onShowContentPreview = useCallback(
        (event) => {
            try {
                setShow(false);
                if (event.currentTarget.id === NotificationChannel.SMS) {
                    setShowPreviewSms(true);
                } else if (
                    event.currentTarget.id === NotificationChannel.EMAIL
                ) {
                    setShowPreviewEmail(true);
                } else {
                    throw new Error("Invalid preview content type");
                }
            } catch (e) {
                toast.error(
                    <div>
                        Failed to preview content!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [setShow, setShowPreviewSms, setShowPreviewEmail]
    );

    const onBackToDetailsView = useCallback(() => {
        setShow(true);
        setShowPreviewSms(false);
        setShowPreviewEmail(false);
    }, [setShow, setShowPreviewSms, setShowPreviewEmail]);

    const onCloseContentPreview = useCallback(() => {
        setSelectedRow({});
        setShow(false);
        setShowPreviewSms(false);
        setShowPreviewEmail(false);
    }, [setSelectedRow, setShow, setShowPreviewSms, setShowPreviewEmail]);

    const onRowClick = useCallback(
        async (event, rowData) => {
            event.stopPropagation();
            event.preventDefault();
            setSelectedRow({
                ...(rowData?.rest ? { ...rowData.rest } : {}),
                ...rowData,
            });
            setShow(true);
        },
        [setSelectedRow, setShow]
    );

    const onClose = useCallback(() => {
        setSelectedRow({});
        setShow(false);
    }, [setSelectedRow, setShow]);

    const onSelectOption = useCallback(
        (e) => setSelectedOption(e),
        [setSelectedOption]
    );

    const onSetFilter = useCallback(
        (e, filterType) => {
            if (appliedFilters) {
                switch (filterType) {
                    case "transport":
                        setIsApplied(e[0]?.value === appliedFilters?.transport);
                        break;
                    case "status":
                        setIsApplied(e[0]?.value === appliedFilters?.status);
                        break;
                    case "toPhoneNumber":
                        setIsApplied(e === appliedFilters?.toPhoneNumber);
                        break;
                    case "toEmail":
                        setIsApplied(e === appliedFilters?.toEmail);
                        break;
                    case "fromDate":
                        setIsApplied(
                            formatToCommonFormat(e) === appliedFilters.fromDate
                        );
                        break;
                    case "toDate":
                        setIsApplied(
                            formatToCommonFormat(e) === appliedFilters.toDate
                        );
                        break;
                    default:
                        break;
                }
            }
        },
        [appliedFilters]
    );

    const onSelectChannelStatus = useCallback(
        (e) => {
            setChannelStatus(e);
            setOtherFilterKeyLabelPair({ key: "transport", label: "Channel" });
            onSetFilter(e, "transport");
        },
        [setChannelStatus, setOtherFilterKeyLabelPair, onSetFilter]
    );

    const onSelectStatus = useCallback(
        (e) => {
            setNotificationLogStatus(e);
            setOtherFilterKeyLabelPair({ key: "status", label: "Status" });
            onSetFilter(e, "status");
        },
        [setNotificationLogStatus, setOtherFilterKeyLabelPair, onSetFilter]
    );

    const onMobileNumberChange = useCallback(
        (status, value, countryData, number, formattedNumber) => {
            setMobileNumber(formattedNumber);
            setOtherFilterKeyLabelPair({
                key: "toPhoneNumber",
                label: "To Mobile Number",
            });
            onSetFilter(formattedNumber, "toPhoneNumber");
        },
        [setMobileNumber, setOtherFilterKeyLabelPair, onSetFilter]
    );

    const onChangeEmail = useCallback(
        (e) => {
            e.stopPropagation();
            setEmail(e.target.value);
            setOtherFilterKeyLabelPair({ key: "toEmail", label: "To Email" });
            onSetFilter(e.target.value, "toEmail");
        },
        [setEmail, setOtherFilterKeyLabelPair, onSetFilter]
    );

    const onFilterBy = useCallback(async () => {
        toggleShowFilters();
        if (!isApplied && !appliedFilters) {
            setOtherFilter({});
            setOtherFilterKeyLabelPair({});
            setSelectedOption([]);
            setEmail("");
            setMobileNumber("");
            setChannelStatus([]);
            setNotificationLogStatus([]);
            setFromDate("");
            setToDate("");
        }
    }, [
        toggleShowFilters,
        isApplied,
        appliedFilters,
        setOtherFilter,
        setOtherFilterKeyLabelPair,
        setSelectedOption,
        setEmail,
        setMobileNumber,
        setChannelStatus,
        setNotificationLogStatus,
        setFromDate,
        setToDate,
    ]);

    const onChangeFromDate = useCallback(
        (date) => {
            setFromDate(date);
            onSetFilter(date, "fromDate");
        },
        [setFromDate, onSetFilter]
    );

    const onChangeToDate = useCallback(
        (date) => {
            setToDate(date);
            onSetFilter(date, "toDate");
        },
        [setToDate, onSetFilter]
    );

    const getOtherFilterValue = useCallback(() => {
        let filterValue = {};

        if (selectedOption[0]?.value) {
            switch (selectedOption[0].value) {
                case NotificationFilterOptions.CHANNEL:
                    filterValue = {
                        transport: channelStatus[0]?.value || "",
                    };
                    break;
                case NotificationFilterOptions.STATUS:
                    filterValue = {
                        status: notificationLogStatus[0]?.value || "",
                    };
                    break;
                case NotificationFilterOptions.TO_SMS:
                    filterValue = { toPhoneNumber: mobileNumber };
                    break;
                case NotificationFilterOptions.TO_EMAIL:
                    if (!/\S+@\S+\.\S+/.test(email)) {
                        throw new Error("Entered email is invalid!");
                    }
                    filterValue = { toEmail: email };
                    break;
                default:
                    setOtherFilterKeyLabelPair({});
                    break;
            }
        }

        return filterValue;
    }, [
        channelStatus,
        email,
        notificationLogStatus,
        mobileNumber,
        selectedOption,
        setOtherFilterKeyLabelPair,
    ]);

    const applyFilter = useCallback(() => {
        try {
            const otherFilter = getOtherFilterValue();

            setOtherFilter(otherFilter);
            setAppliedFilters(otherFilter);

            if (fromDate !== "" || toDate !== "") {
                if (toDate === "") {
                    setAppliedFilters({
                        ...otherFilter,
                        fromDate: formatToCommonFormat(fromDate),
                    });
                } else if (fromDate === "") {
                    setAppliedFilters({
                        ...otherFilter,
                        toDate: formatToCommonFormat(toDate),
                    });
                } else {
                    setAppliedFilters({
                        ...otherFilter,
                        fromDate: formatToCommonFormat(fromDate),
                        toDate: formatToCommonFormat(toDate),
                    });
                }
            }
            setIsApplied(true);
            setIsSelectDisabled(true);
            setSkip(defaultSkip);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to apply filter!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        fromDate,
        toDate,
        getOtherFilterValue,
        setOtherFilter,
        setAppliedFilters,
        setIsApplied,
        setIsSelectDisabled,
        setSkip,
    ]);

    const removeOtherFilter = useCallback(() => {
        setOtherFilter({});
        setOtherFilterKeyLabelPair({});
        setSelectedOption([]);
        setEmail("");
        setMobileNumber("");
        setChannelStatus([]);
        setNotificationLogStatus([]);
        setAppliedFilters();

        if (appliedFilters?.fromDate || appliedFilters?.toDate) {
            setAppliedFilters({
                ...(appliedFilters?.fromDate
                    ? {
                        fromDate: formatToCommonFormat(
                            appliedFilters.fromDate
                        ),
                    }
                    : {}),
                ...(appliedFilters?.toDate
                    ? { toDate: formatToCommonFormat(appliedFilters.toDate) }
                    : {}),
            });
        } else {
            setIsApplied(false);
        }
        setIsSelectDisabled(false);
        setSkip(defaultSkip);
    }, [
        appliedFilters,
        setOtherFilter,
        setOtherFilterKeyLabelPair,
        setSelectedOption,
        setEmail,
        setMobileNumber,
        setChannelStatus,
        setNotificationLogStatus,
        setAppliedFilters,
        setIsApplied,
        setIsSelectDisabled,
        setSkip,
    ]);

    const removeFromDate = useCallback(() => {
        setFromDate("");
        setAppliedFilters();
        if (!isEmptyObject(otherFilter || {}) || appliedFilters?.toDate) {
            setAppliedFilters({
                ...(!isEmptyObject(otherFilter || {}) ? otherFilter : {}),
                ...(appliedFilters?.toDate
                    ? { toDate: formatToCommonFormat(toDate) }
                    : {}),
            });
        } else {
            setIsApplied(false);
        }
        setSkip(defaultSkip);
    }, [
        appliedFilters,
        otherFilter,
        toDate,
        setFromDate,
        setAppliedFilters,
        setIsApplied,
        setSkip,
    ]);

    const removeToDate = useCallback(() => {
        setToDate("");
        setAppliedFilters();
        if (!isEmptyObject(otherFilter || {}) || appliedFilters?.fromDate) {
            setAppliedFilters({
                ...(!isEmptyObject(otherFilter || {}) ? otherFilter : {}),
                ...(appliedFilters?.fromDate
                    ? { fromDate: formatToCommonFormat(fromDate) }
                    : {}),
            });
        } else {
            setIsApplied(false);
        }
        setSkip(defaultSkip);
    }, [
        appliedFilters,
        otherFilter,
        fromDate,
        setToDate,
        setAppliedFilters,
        setIsApplied,
        setSkip,
    ]);

    const resetFilter = useCallback(() => {
        setOtherFilter({});
        setOtherFilterKeyLabelPair({});
        setSelectedOption([]);
        setEmail("");
        setMobileNumber("");
        setChannelStatus([]);
        setNotificationLogStatus([]);
        setFromDate("");
        setToDate("");
        setAppliedFilters();
        setIsApplied(false);
        setIsSelectDisabled(false);
        setSkip(defaultSkip);
    }, [
        setOtherFilter,
        setOtherFilterKeyLabelPair,
        setSelectedOption,
        setEmail,
        setMobileNumber,
        setChannelStatus,
        setNotificationLogStatus,
        setFromDate,
        setToDate,
        setAppliedFilters,
        setIsApplied,
        setIsSelectDisabled,
        setSkip,
    ]);

    const onReloadNotificationLogs = useCallback(async () => {
        setIsReloading(true);
        await loadNotificationLogs(
            { limit, skip: defaultSkip },
            appliedFilters
        );
        setSkip(defaultSkip);
        setIsReloading(false);
    }, [limit, appliedFilters, setSkip, setIsReloading, loadNotificationLogs]);

    useEffect(() => {
        loadNotificationLogs({ limit, skip: defaultSkip }, appliedFilters);
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [appliedFilters]);

    return (
        <BaseLayout
            topLeft={<Heading text="Notification Logs" />}
            bottom={
                <div className="container-fluid mt-3">
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            {isReloading ? (
                                <small className="ml-3 text-primary">
                                    Reloading...
                                </small>
                            ) : (
                                <Button
                                    className="d-flex align-items-center btn shadow-none"
                                    size="sm"
                                    variant="link"
                                    disabled={isLoading || isReloading}
                                    onClick={onReloadNotificationLogs}
                                >
                                    <IcIcon
                                        size="md"
                                        className="mr-2"
                                        icon={faSync}
                                    />
                                    Reload Notification Logs
                                </Button>
                            )}
                        </div>
                        <Button
                            variant={`${!showFilters ? "outline-" : ""}primary`}
                            size="sm"
                            disabled={isLoading || isReloading}
                            onClick={onFilterBy}
                        >
                            <IcIcon
                                className="mr-2"
                                size="lg"
                                icon={showFilters ? faFilterSlash : faFilter}
                            />
                            {showFilters ? "Hide Filters" : "Filter By"}
                        </Button>
                    </div>
                    <NotificationFilters
                        showFilters={showFilters}
                        selectedOption={selectedOption}
                        onSelectOption={onSelectOption}
                        isLoading={isLoading}
                        selectOptions={selectOptions}
                        mobileNumber={mobileNumber}
                        email={email}
                        fromDate={fromDate}
                        toDate={toDate}
                        channelStatus={channelStatus}
                        status={notificationLogStatus}
                        appliedFilters={appliedFilters}
                        appliedOtherFilter={otherFilterKeyLabelPair}
                        appliedFromDateAttr="fromDate"
                        appliedToDateAttr="toDate"
                        isApplied={isApplied}
                        isSelectDisabled={isSelectDisabled}
                        onChangeEmail={onChangeEmail}
                        setFromDate={onChangeFromDate}
                        setToDate={onChangeToDate}
                        onSelectStatus={onSelectStatus}
                        onSelectChannelStatus={onSelectChannelStatus}
                        onMobileNumberChange={onMobileNumberChange}
                        removeOtherFilter={removeOtherFilter}
                        removeFromDate={removeFromDate}
                        removeToDate={removeToDate}
                        applyFilter={applyFilter}
                        clearFilter={resetFilter}
                    />
                    <TableView
                        columns={columns}
                        data={tableData}
                        sizePerPage={limit}
                        setSelectedItems={setSelectedItems}
                        page={skip}
                        onChangePagination={onChangePagination}
                        totalCount={dataCount}
                        onChangePageSize={onChangePageSize}
                        isLoading={isLoading}
                        selectedItems={selectedItems}
                        onRowClick={onRowClick}
                    />
                    {show && (
                        <NotificationsDetailsView
                            show={show}
                            title="Notification Log Details"
                            selectedRow={selectedRow}
                            onHide={onClose}
                            onShowContentPreview={onShowContentPreview}
                        />
                    )}
                    {showPreviewSms && (
                        <PreviewSmsModal
                            show={showPreviewSms}
                            senderId={selectedRow?.from || ""}
                            smsContent={selectedRow?.body}
                            onHide={onCloseContentPreview}
                            otherButton={
                                <Button
                                    className="d-flex align-items-center"
                                    size="sm"
                                    variant="link"
                                    onClick={onBackToDetailsView}
                                >
                                    <IcIcon
                                        icon={faAngleLeftB}
                                        size="lg"
                                        className="mr-1"
                                    />
                                    Back to Message Log Details
                                </Button>
                            }
                        />
                    )}
                    {showPreviewEmail && (
                        <PreviewEmail
                            show={showPreviewEmail}
                            emailBody={selectedRow?.body}
                            onHide={onCloseContentPreview}
                            otherButton={
                                <Button
                                    className="d-flex align-items-center"
                                    size="sm"
                                    variant="link"
                                    onClick={onBackToDetailsView}
                                >
                                    <IcIcon
                                        icon={faAngleLeftB}
                                        size="lg"
                                        className="mr-1"
                                    />
                                    Back to Notification Log Details
                                </Button>
                            }
                        />
                    )}
                </div>
            }
        />
    );
};

export default NotificationLogs;
