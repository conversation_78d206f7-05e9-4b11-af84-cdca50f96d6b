import React from "react";
import { Route, Switch } from "react-router-dom";
import MessageLogs from "./messageLogs/MessageLogs";
import NotificationLogs from "./notificationLogs/NotificationLogs";
import Templates from "./templates/Templates";
import NotificationDetails from "./templates/notificationDetails/NotificationDetails";

import "./NotificationPage.scss";

const NotificationPage = () => {
    return (
        <div className="notifications-page-view">
            <Switch>
                <Route
                    name="message-logs"
                    exact
                    path="/notifications/message-logs"
                    component={MessageLogs}
                />
                <Route
                    name="notification-logs"
                    exact
                    path="/notifications/notification-logs"
                    component={NotificationLogs}
                />
                <Route
                    name="templates"
                    exact
                    path="/notifications/templates"
                    component={Templates}
                />
                <Route
                    name="notification-details"
                    exact
                    path="/notifications/notification-details/:id"
                    component={NotificationDetails}
                />
            </Switch>
        </div>
    );
};
export default NotificationPage;
