.notifications-details-view {
    .grey-wrap {
        background-color: #f6f6f6;
        width: 90%;
        font-size: 12px;
    }

    .badge-SMS {
        color: white;
        background-color: var(--primary);
    }

    .badge-EMAIL {
        color: white;
        background-color: var(--secondary);
    }

    .badge-PUSH {
        color: black;
        background-color: var(--orange);
    }

    .badge-WHATSAPP {
        color: black;
        background-color: #27fd79;
    }

    .badge-QUEUED {
        color: black;
        background-color: var(--warning);
    }

    .badge-SENT {
        color: black;
        background-color: var(--teal);
    }

    .badge-DELIVERED {
        color: white;
        background-color: var(--success);
    }

    .badge-OPENED {
        color: white;
        background-color: var(--info);
    }

    .badge-FAILED {
        color: white;
        background-color: var(--danger);
    }

    .badge-INTERNAL_ERROR {
        color: white;
        background-color: #ab3d3d;
    }

    .badge-READ {
        color: black;
        background-color: #27fd79;
    }

    .badge-UNDELIVERED {
        color: white;
        background-color: #000;
    }

    .badge-ACCEPTED {
        color: white;
        background-color: #018735;
    }

    .badge-BLOCKED {
        color: white;
        background-color: #cc433a;
    }

    .badge-SENDING {
        color: black;
        background-color: #5ee0e7;
    }

    .badge-OPEN {
        color: white;
        background-color: #027d84;
    }

    .badge-BOUNCED {
        color: white;
        background-color: #d76800;
    }

    .badge-RECEIVING {
        color: black;
        background-color: gold;
    }

    .badge-RECEIVED {
        color: black;
        background-color: #ffae00;
    }

    .badge-SCHEDULED {
        color: black;
        background-color: #feef64;
    }

    .badge-DELIVERY_UNKNOWN {
        color: white;
        background-color: #959595;
    }

    .badge-CLICK {
        color: white;
        background-color: #479966;
    }

    .badge-SPAM_REPORT {
        color: white;
        background-color: #ff0000;
    }

    .other-message-data-view {
        max-height: 20rem !important;
        overflow: auto;
    }

    .other-data-values-view {
        background-color: #fbfbfb;
        max-height: 12rem !important;
        overflow: auto;
    }

    .history-view {
        background-color: #fbfbfb;
        max-height: 30rem !important;
        overflow: scroll;
    }
}
