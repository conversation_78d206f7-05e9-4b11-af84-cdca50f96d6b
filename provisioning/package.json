{"name": "web-hosting-cf-s3", "version": "0.1.0", "bin": {"web-hosting-cf-s3": "bin/web-hosting-cf-s3.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "cdk:synth": "cdk synth --profile shoutout2", "cdk:deploy": "cdk deploy --profile shoutout2 -v", "cdk:diff": "cdk diff --profile shoutout2"}, "devDependencies": {"@aws-cdk/assert": "1.92.0", "@types/jest": "^26.0.10", "@types/node": "10.17.27", "aws-cdk": "^1.100.0", "jest": "^26.4.2", "ts-jest": "^26.2.0", "ts-node": "^9.0.0", "typescript": "~3.9.7"}, "dependencies": {"@aws-cdk/aws-cloudfront": "^1.92.0", "@aws-cdk/aws-codebuild": "^1.100.0", "@aws-cdk/aws-iam": "^1.92.0", "@aws-cdk/aws-s3": "^1.92.0", "@aws-cdk/aws-ssm": "^1.100.0", "@aws-cdk/core": "^1.92.0", "source-map-support": "^0.5.16"}}