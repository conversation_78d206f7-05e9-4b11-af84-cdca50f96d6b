import * as cdk from '@aws-cdk/core';
import { Bucket } from '@aws-cdk/aws-s3';
import { CloudFrontWebDistribution, OriginAccessIdentity } from '@aws-cdk/aws-cloudfront';

export class WebHostingCfS3Stack extends cdk.Stack {
  constructor(scope: cdk.Construct, id: string, envSuffix: string, envName: string, certificateArn: string, domainNames: string[], distName: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // The code that defines your stack goes here
    const sourceBucket = new Bucket(this, 'source-bucket', {
      bucketName: `enterprise-loyalty-application-${envName}-${envSuffix}`
    });

    const originAccessIdentity = new OriginAccessIdentity(this, `cf-bucket-oai-${envName}-${envSuffix}`, {
      comment: `cloudfront bucket oai ${envName} ${envSuffix}`
    });
    sourceBucket.grantRead(originAccessIdentity);

    const cfDistribution = new CloudFrontWebDistribution(this, `cf-distribution-${envName}-${envSuffix}`, {
      originConfigs: [
        {
          s3OriginSource: {
            s3BucketSource: sourceBucket,
            originAccessIdentity: originAccessIdentity

          },
          behaviors: [{ isDefaultBehavior: true }]
        }
      ],
      errorConfigurations: [
        {
          errorCode: 403,
          errorCachingMinTtl: 300,
          responseCode: 200,
          responsePagePath: '/index.html'
        },
        {
          errorCode: 404,
          errorCachingMinTtl: 300,
          responseCode: 200,
          responsePagePath: '/index.html'
        }
      ],
      aliasConfiguration: {
        acmCertRef: certificateArn,
        names: domainNames
      },
      comment: distName,
      defaultRootObject: 'index.html'
    });

    new cdk.CfnOutput(this, 'cfDistribution', { value: cfDistribution.distributionId });

  }
}
